-- SUBS<PERSON>IPTION SYSTEM FOR LAW APP MOROCCO - FIXED VERSION
-- Execute this script in Supabase SQL Editor to add subscription functionality

-- 1. Create subscription_plans table for different subscription tiers
CREATE TABLE IF NOT EXISTS public.subscription_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  name_ar VARCHAR(100) NOT NULL,
  description TEXT,
  description_ar TEXT,
  price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  currency VARCHAR(3) NOT NULL DEFAULT 'MAD',
  billing_period VARCHAR(20) NOT NULL DEFAULT 'monthly',
  features JSONB NOT NULL DEFAULT '[]',
  features_ar JSONB NOT NULL DEFAULT '[]',
  is_active BOOLEAN NOT NULL DEFAULT true,
  sort_order INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 2. Create user_subscriptions table for tracking user subscriptions
CREATE TABLE IF NOT EXISTS public.user_subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  plan_id UUID NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'active',
  start_date TIMESTAMPTZ NOT NULL DEFAULT now(),
  end_date TIMESTAMPTZ,
  auto_renew BOOLEAN NOT NULL DEFAULT true,
  payment_method VARCHAR(50),
  last_payment_date TIMESTAMPTZ,
  next_payment_date TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 3. Create subscription_preferences table for notification preferences
CREATE TABLE IF NOT EXISTS public.subscription_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  email_notifications BOOLEAN NOT NULL DEFAULT true,
  sms_notifications BOOLEAN NOT NULL DEFAULT false,
  in_app_notifications BOOLEAN NOT NULL DEFAULT true,
  legal_updates BOOLEAN NOT NULL DEFAULT true,
  question_responses BOOLEAN NOT NULL DEFAULT true,
  system_announcements BOOLEAN NOT NULL DEFAULT true,
  marketing_emails BOOLEAN NOT NULL DEFAULT false,
  weekly_digest BOOLEAN NOT NULL DEFAULT true,
  phone_number VARCHAR(20),
  preferred_language VARCHAR(5) NOT NULL DEFAULT 'ar',
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 4. Create subscription_analytics table for tracking metrics
CREATE TABLE IF NOT EXISTS public.subscription_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  total_subscribers INTEGER NOT NULL DEFAULT 0,
  new_subscribers INTEGER NOT NULL DEFAULT 0,
  cancelled_subscribers INTEGER NOT NULL DEFAULT 0,
  active_subscribers INTEGER NOT NULL DEFAULT 0,
  revenue DECIMAL(12,2) NOT NULL DEFAULT 0.00,
  plan_breakdown JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 5. Add foreign key constraints (separate from table creation to avoid dependency issues)
DO $$ 
BEGIN
  -- Add foreign key for user_subscriptions.user_id if profiles table exists
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles' AND table_schema = 'public') THEN
    ALTER TABLE public.user_subscriptions 
    ADD CONSTRAINT fk_user_subscriptions_user_id 
    FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;
  END IF;
  
  -- Add foreign key for user_subscriptions.plan_id
  ALTER TABLE public.user_subscriptions 
  ADD CONSTRAINT fk_user_subscriptions_plan_id 
  FOREIGN KEY (plan_id) REFERENCES public.subscription_plans(id) ON DELETE RESTRICT;
  
  -- Add foreign key for subscription_preferences.user_id if profiles table exists
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles' AND table_schema = 'public') THEN
    ALTER TABLE public.subscription_preferences 
    ADD CONSTRAINT fk_subscription_preferences_user_id 
    FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;
  END IF;
EXCEPTION
  WHEN duplicate_object THEN
    -- Constraints already exist, continue
    NULL;
END $$;

-- 6. Create unique constraints
DO $$
BEGIN
  -- Unique constraint for subscription_preferences.user_id
  ALTER TABLE public.subscription_preferences 
  ADD CONSTRAINT unique_subscription_preferences_user_id UNIQUE (user_id);
  
  -- Unique constraint for subscription_analytics.date
  ALTER TABLE public.subscription_analytics 
  ADD CONSTRAINT unique_subscription_analytics_date UNIQUE (date);
EXCEPTION
  WHEN duplicate_object THEN
    -- Constraints already exist, continue
    NULL;
END $$;

-- 7. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON public.user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON public.user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_plan_id ON public.user_subscriptions(plan_id);
CREATE INDEX IF NOT EXISTS idx_subscription_preferences_user_id ON public.subscription_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_subscription_analytics_date ON public.subscription_analytics(date);

-- Create unique partial index to ensure one active subscription per user
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_subscriptions_active_unique 
ON public.user_subscriptions(user_id) 
WHERE status = 'active';

-- 8. Enable Row Level Security
ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_analytics ENABLE ROW LEVEL SECURITY;

-- 9. Create RLS policies for subscription_plans
DROP POLICY IF EXISTS "Anyone can view active subscription plans" ON public.subscription_plans;
CREATE POLICY "Anyone can view active subscription plans" ON public.subscription_plans
  FOR SELECT USING (is_active = true);

DROP POLICY IF EXISTS "Admins can manage subscription plans" ON public.subscription_plans;
CREATE POLICY "Admins can manage subscription plans" ON public.subscription_plans
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- 10. Create RLS policies for user_subscriptions
DROP POLICY IF EXISTS "Users can view their own subscriptions" ON public.user_subscriptions;
CREATE POLICY "Users can view their own subscriptions" ON public.user_subscriptions
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can create their own subscriptions" ON public.user_subscriptions;
CREATE POLICY "Users can create their own subscriptions" ON public.user_subscriptions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own subscriptions" ON public.user_subscriptions;
CREATE POLICY "Users can update their own subscriptions" ON public.user_subscriptions
  FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Admins can manage all subscriptions" ON public.user_subscriptions;
CREATE POLICY "Admins can manage all subscriptions" ON public.user_subscriptions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- 11. Create RLS policies for subscription_preferences
DROP POLICY IF EXISTS "Users can manage their own preferences" ON public.subscription_preferences;
CREATE POLICY "Users can manage their own preferences" ON public.subscription_preferences
  FOR ALL USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Admins can view all preferences" ON public.subscription_preferences;
CREATE POLICY "Admins can view all preferences" ON public.subscription_preferences
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- 12. Create RLS policies for subscription_analytics
DROP POLICY IF EXISTS "Admins can manage analytics" ON public.subscription_analytics;
CREATE POLICY "Admins can manage analytics" ON public.subscription_analytics
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- 13. Insert default subscription plans
INSERT INTO public.subscription_plans (name, name_ar, description, description_ar, price, features, features_ar, sort_order) 
VALUES
('Free', 'مجاني', 'Basic legal assistance', 'مساعدة قانونية أساسية', 0.00, 
 '["1 legal question per month", "Basic document creation", "7-day premium trial"]',
 '["سؤال قانوني واحد شهرياً", "إنشاء وثائق محدود", "تجربة مجانية 7 أيام للباقة المتميزة"]', 1),

('Premium', 'المستخدم المتميز', 'Enhanced legal services', 'خدمات قانونية محسنة', 100.00,
 '["Unlimited legal questions", "Unlimited document creation", "Priority support", "Full access to lawyers"]',
 '["أسئلة قانونية غير محدودة", "إنشاء وثائق غير محدود", "دعم ذو أولوية", "وصول كامل للمحامين"]', 2),

('Professional', 'المحامي المتميز', 'Complete legal solution', 'حل قانوني متكامل', 500.00,
 '["Personal legal profile", "Advanced tools and analytics", "Real-time notifications", "Priority in specialized profile display"]',
 '["ملف شخصي مميز في البحث", "أدوات الحجز والتحليلات", "تحديد الأوقات والأسعار", "أولوية في عرض الملف الشخصي"]', 3)
ON CONFLICT DO NOTHING;

-- 14. Create function to update subscription analytics
CREATE OR REPLACE FUNCTION public.update_subscription_analytics()
RETURNS VOID AS $$
DECLARE
  today DATE := CURRENT_DATE;
  total_subs INTEGER;
  active_subs INTEGER;
  new_subs INTEGER;
  cancelled_subs INTEGER;
  total_revenue DECIMAL(12,2);
  plan_stats JSONB;
BEGIN
  -- Calculate metrics
  SELECT COUNT(*) INTO total_subs FROM public.user_subscriptions;
  SELECT COUNT(*) INTO active_subs FROM public.user_subscriptions WHERE status = 'active';
  SELECT COUNT(*) INTO new_subs FROM public.user_subscriptions WHERE DATE(created_at) = today;
  SELECT COUNT(*) INTO cancelled_subs FROM public.user_subscriptions WHERE status = 'cancelled' AND DATE(updated_at) = today;
  
  -- Calculate revenue (simplified - would need payment integration)
  SELECT COALESCE(SUM(sp.price), 0) INTO total_revenue
  FROM public.user_subscriptions us
  JOIN public.subscription_plans sp ON us.plan_id = sp.id
  WHERE us.status = 'active';
  
  -- Calculate plan breakdown
  SELECT jsonb_object_agg(sp.name, plan_count) INTO plan_stats
  FROM (
    SELECT sp.name, COUNT(us.id) as plan_count
    FROM public.subscription_plans sp
    LEFT JOIN public.user_subscriptions us ON sp.id = us.plan_id AND us.status = 'active'
    GROUP BY sp.name
  ) plan_data;
  
  -- Insert or update analytics
  INSERT INTO public.subscription_analytics (
    date, total_subscribers, new_subscribers, cancelled_subscribers, 
    active_subscribers, revenue, plan_breakdown
  ) VALUES (
    today, total_subs, new_subs, cancelled_subs, active_subs, total_revenue, plan_stats
  )
  ON CONFLICT (date) DO UPDATE SET
    total_subscribers = EXCLUDED.total_subscribers,
    new_subscribers = EXCLUDED.new_subscribers,
    cancelled_subscribers = EXCLUDED.cancelled_subscribers,
    active_subscribers = EXCLUDED.active_subscribers,
    revenue = EXCLUDED.revenue,
    plan_breakdown = EXCLUDED.plan_breakdown,
    created_at = now();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 15. Create function to get user subscription details
CREATE OR REPLACE FUNCTION public.get_user_subscription_details(user_uuid UUID)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'subscription', row_to_json(us.*),
    'plan', row_to_json(sp.*),
    'preferences', row_to_json(pref.*)
  ) INTO result
  FROM public.user_subscriptions us
  JOIN public.subscription_plans sp ON us.plan_id = sp.id
  LEFT JOIN public.subscription_preferences pref ON pref.user_id = us.user_id
  WHERE us.user_id = user_uuid AND us.status = 'active'
  ORDER BY us.created_at DESC
  LIMIT 1;
  
  RETURN COALESCE(result, '{"subscription": null, "plan": null, "preferences": null}'::json);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 16. Grant permissions
GRANT EXECUTE ON FUNCTION public.update_subscription_analytics TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_subscription_details TO authenticated;

-- 17. Success message
SELECT 'Subscription system created successfully! 🎉' as status;
