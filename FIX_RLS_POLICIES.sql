-- 🔧 CORRECTION DES POLITIQUES RLS - LAW APP MOROCCO
-- Exécutez ce script dans Supabase SQL Editor pour corriger tous les problèmes d'accès

-- =====================================================
-- 1. SUPPRESSION DES POLITIQUES PROBLÉMATIQUES
-- =====================================================

-- Supprimer toutes les politiques existantes pour recommencer proprement
DROP POLICY IF EXISTS "Users can view their own questions" ON public.legal_questions;
DROP POLICY IF EXISTS "Users can create questions" ON public.legal_questions;
DROP POLICY IF EXISTS "Users can update their own questions" ON public.legal_questions;
DROP POLICY IF EXISTS "Advocates can view all questions" ON public.legal_questions;
DROP POLICY IF EXISTS "Advocates can update questions assigned to them" ON public.legal_questions;
DROP POLICY IF EXISTS "<PERSON><PERSON> can manage all questions" ON public.legal_questions;

DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON public.profiles;

DROP POLICY IF EXISTS "Anyone can view verified advocates" ON public.advocates;
DROP POLICY IF EXISTS "Advocates can manage their own profile" ON public.advocates;
DROP POLICY IF EXISTS "Admins can manage all advocates" ON public.advocates;

-- =====================================================
-- 2. NOUVELLES POLITIQUES SIMPLIFIÉES ET FONCTIONNELLES
-- =====================================================

-- POLITIQUES POUR PROFILES (plus permissives pour éviter les blocages)
CREATE POLICY "profiles_select_own" ON public.profiles
  FOR SELECT USING (
    auth.uid() = id OR 
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
  );

CREATE POLICY "profiles_update_own" ON public.profiles
  FOR UPDATE USING (
    auth.uid() = id OR 
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
  );

CREATE POLICY "profiles_insert_own" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- POLITIQUES POUR LEGAL_QUESTIONS (accès simplifié)
CREATE POLICY "questions_select" ON public.legal_questions
  FOR SELECT USING (
    -- L'utilisateur peut voir ses propres questions
    auth.uid() = user_id OR
    -- Les avocats vérifiés peuvent voir toutes les questions
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'advocate' AND is_verified = true
    ) OR
    -- Les admins peuvent voir toutes les questions
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "questions_insert" ON public.legal_questions
  FOR INSERT WITH CHECK (
    auth.uid() = user_id OR
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
  );

CREATE POLICY "questions_update" ON public.legal_questions
  FOR UPDATE USING (
    -- L'utilisateur peut modifier ses propres questions
    auth.uid() = user_id OR
    -- Les avocats peuvent modifier les questions qui leur sont assignées
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id
    ) OR
    -- Les admins peuvent modifier toutes les questions
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- POLITIQUES POUR ADVOCATES (accès simplifié)
CREATE POLICY "advocates_select" ON public.advocates
  FOR SELECT USING (
    -- Tout le monde peut voir les avocats vérifiés
    EXISTS (SELECT 1 FROM public.profiles WHERE id = profile_id AND is_verified = true) OR
    -- L'avocat peut voir son propre profil même non vérifié
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND id = profile_id) OR
    -- Les admins peuvent voir tous les avocats
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
  );

CREATE POLICY "advocates_insert" ON public.advocates
  FOR INSERT WITH CHECK (
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND id = profile_id) OR
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
  );

CREATE POLICY "advocates_update" ON public.advocates
  FOR UPDATE USING (
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND id = profile_id) OR
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
  );

-- POLITIQUES POUR RESPONSES (accès simplifié)
CREATE POLICY "responses_select" ON public.responses
  FOR SELECT USING (
    -- L'utilisateur peut voir les réponses à ses questions
    EXISTS (
      SELECT 1 FROM public.legal_questions lq
      WHERE lq.id = question_id AND lq.user_id = auth.uid()
    ) OR
    -- L'avocat peut voir ses propres réponses
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id
    ) OR
    -- Les admins peuvent voir toutes les réponses
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "responses_insert" ON public.responses
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id AND p.is_verified = true
    ) OR
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
  );

CREATE POLICY "responses_update" ON public.responses
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id
    ) OR
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
  );

-- POLITIQUES POUR LEGAL_DOCUMENTS (accès simplifié)
CREATE POLICY "documents_all_access" ON public.legal_documents
  FOR ALL USING (
    auth.uid() = user_id OR
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
  );

-- POLITIQUES POUR SUBSCRIPTIONS (accès simplifié)
CREATE POLICY "subscriptions_all_access" ON public.subscriptions
  FOR ALL USING (
    auth.uid() = user_id OR
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
  );

-- POLITIQUES POUR USAGE_TRACKING (accès simplifié)
CREATE POLICY "usage_tracking_all_access" ON public.usage_tracking
  FOR ALL USING (
    auth.uid() = user_id OR
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
  );

-- POLITIQUES POUR SUBSCRIBERS (accès simplifié)
CREATE POLICY "subscribers_select" ON public.subscribers
  FOR SELECT USING (
    user_id = auth.uid() OR 
    email = auth.email() OR
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
  );

CREATE POLICY "subscribers_insert" ON public.subscribers
  FOR INSERT WITH CHECK (true);

CREATE POLICY "subscribers_update" ON public.subscribers
  FOR UPDATE USING (true);

-- =====================================================
-- 3. CRÉATION DE COMPTES DE TEST POUR DIAGNOSTIC
-- =====================================================

-- Créer des comptes de test avec des UUIDs fixes pour faciliter les tests
-- Note: Ces comptes seront créés seulement s'ils n'existent pas déjà

-- Admin de test
INSERT INTO public.profiles (id, email, full_name, role, subscription_tier, is_verified, created_at, updated_at)
VALUES 
  ('00000000-0000-0000-0000-000000000001', '<EMAIL>', 'Admin Test', 'admin', 'pro_advocate', true, now(), now())
ON CONFLICT (id) DO UPDATE SET
  role = 'admin',
  is_verified = true,
  full_name = 'Admin Test';

-- Avocat de test
INSERT INTO public.profiles (id, email, full_name, role, subscription_tier, is_verified, created_at, updated_at)
VALUES 
  ('00000000-0000-0000-0000-000000000002', '<EMAIL>', 'Avocat Test', 'advocate', 'pro_advocate', true, now(), now())
ON CONFLICT (id) DO UPDATE SET
  role = 'advocate',
  is_verified = true,
  full_name = 'Avocat Test';

-- Utilisateur de test
INSERT INTO public.profiles (id, email, full_name, role, subscription_tier, is_verified, created_at, updated_at)
VALUES 
  ('00000000-0000-0000-0000-000000000003', '<EMAIL>', 'User Test', 'user', 'free', true, now(), now())
ON CONFLICT (id) DO UPDATE SET
  role = 'user',
  is_verified = true,
  full_name = 'User Test';

-- Profil avocat pour l'avocat de test
INSERT INTO public.advocates (id, profile_id, specializations, bio, hourly_rate, is_featured, rating, total_reviews, created_at, updated_at)
VALUES 
  ('00000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000002', 
   ARRAY['family', 'civil'], 'Avocat de test pour diagnostic', 500.00, false, 4.5, 10, now(), now())
ON CONFLICT (id) DO NOTHING;

-- Questions de test
INSERT INTO public.legal_questions (id, user_id, title, description, category, status, is_answered, created_at, updated_at)
VALUES 
  ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000003',
   'Question de test', 'Ceci est une question de test pour vérifier le système', 'general', 'pending', false, now(), now()),
  ('00000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000003',
   'Deuxième question de test', 'Une autre question pour tester l\'affichage', 'family', 'pending', false, now(), now())
ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- 4. REQUÊTES DE VÉRIFICATION
-- =====================================================

-- Vérifiez que tout fonctionne avec ces requêtes :

/*
-- 1. Vérifier les profils créés
SELECT id, email, full_name, role, is_verified FROM public.profiles WHERE email LIKE '%@test.ma';

-- 2. Vérifier les questions de test
SELECT id, title, user_id, status FROM public.legal_questions WHERE user_id = '00000000-0000-0000-0000-000000000003';

-- 3. Vérifier les politiques actives
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public' 
ORDER BY tablename, policyname;

-- 4. Test d'accès pour un utilisateur spécifique (remplacez l'UUID par un vrai)
-- SET LOCAL "request.jwt.claims" = '{"sub": "00000000-0000-0000-0000-000000000003"}';
-- SELECT * FROM public.legal_questions;
*/

-- =====================================================
-- MESSAGE DE CONFIRMATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '✅ CORRECTION DES POLITIQUES RLS TERMINÉE !';
    RAISE NOTICE '📋 Comptes de test créés :';
    RAISE NOTICE '   - <EMAIL> (Admin)';
    RAISE NOTICE '   - <EMAIL> (Avocat vérifié)';
    RAISE NOTICE '   - <EMAIL> (Utilisateur)';
    RAISE NOTICE '🔧 Testez maintenant l''application avec ces comptes !';
END $$;
