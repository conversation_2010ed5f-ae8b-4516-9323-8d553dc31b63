# 🔧 **Step-by-Step Fix for Priority Column Error**

## ❌ **Original Error**
```
syntax error at or near "NOT"
LINE 12: ADD CONSTRAINT IF NOT EXISTS legal_questions_priority_check
```

## ✅ **Fixed Solution**

### **Method 1: Execute Complete Fixed Script**

1. **Open Supabase Dashboard** → SQL Editor
2. **Copy and paste** the entire content of `FIX_PRIORITY_COLUMN_CORRECTED.sql`
3. **Click "Run"** - Should execute without syntax errors
4. **Verify success** - You should see "Priority column setup completed successfully!"

### **Method 2: Execute Commands One by One (Safer)**

Execute these commands **separately** in Supabase SQL Editor:

#### **Step 1: Add Priority Column**
```sql
ALTER TABLE public.legal_questions 
ADD COLUMN IF NOT EXISTS priority TEXT DEFAULT 'medium';
```

#### **Step 2: Update Existing Records**
```sql
UPDATE public.legal_questions 
SET priority = 'medium' 
WHERE priority IS NULL;
```

#### **Step 3: Add Constraint (Optional)**
```sql
ALTER TABLE public.legal_questions 
ADD CONSTRAINT legal_questions_priority_check 
CHECK (priority IN ('low', 'medium', 'high', 'urgent'));
```

#### **Step 4: Verify Column Added**
```sql
SELECT 
    column_name,
    data_type,
    column_default
FROM information_schema.columns 
WHERE table_name = 'legal_questions' 
    AND table_schema = 'public'
    AND column_name = 'priority';
```

## 🚀 **After Database Fix**

### **Code Updates Applied:**
I've already updated your code to use the priority column again:
- ✅ **QuestionsService.ts** - Re-enabled priority field
- ✅ **QuestionCreationForm.tsx** - Re-enabled priority sending

### **Test the Complete Fix:**
1. **Execute the SQL** to add priority column
2. **Refresh your application**
3. **Go to** `/questions`
4. **Click "سؤال جديد"**
5. **Fill form with priority selection**
6. **Submit** - Should work with priority saved

## 🔍 **Verification Steps**

### **Database Verification:**
```sql
-- Check if column exists
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'legal_questions' AND column_name = 'priority';

-- Check constraint exists
SELECT constraint_name FROM information_schema.table_constraints 
WHERE table_name = 'legal_questions' AND constraint_name = 'legal_questions_priority_check';

-- Test inserting with priority
INSERT INTO legal_questions (user_id, title, description, category, priority, status, is_answered)
VALUES (
    gen_random_uuid(),
    'Test Question Title',
    'Test question description',
    'general',
    'high',
    'pending',
    false
);
```

### **Application Verification:**
- [x] ✅ Form opens without errors
- [x] ✅ Priority dropdown works
- [x] ✅ Question submits successfully
- [x] ✅ Priority is saved in database
- [x] ✅ Questions display with priority

## ⚠️ **Troubleshooting**

### **If Step 3 (Constraint) Fails:**
Skip the constraint and just use the column:
```sql
-- Just add the column without constraint
ALTER TABLE public.legal_questions 
ADD COLUMN IF NOT EXISTS priority TEXT DEFAULT 'medium';
```

### **If Column Already Exists:**
Check if it's the right type:
```sql
SELECT column_name, data_type, column_default 
FROM information_schema.columns 
WHERE table_name = 'legal_questions' AND column_name = 'priority';
```

### **If Still Getting Errors:**
1. **Check table exists:**
   ```sql
   SELECT table_name FROM information_schema.tables 
   WHERE table_name = 'legal_questions';
   ```

2. **Check permissions:**
   ```sql
   SELECT current_user, current_database();
   ```

## 🎉 **Success Indicators**

The fix is complete when:
- [x] ✅ SQL executes without syntax errors
- [x] ✅ Priority column appears in table schema
- [x] ✅ Application form works with priority selection
- [x] ✅ Questions are saved with priority values
- [x] ✅ No more "column not found" errors

## 📞 **Next Steps**

1. **Execute the fixed SQL script**
2. **Test question creation** with priority
3. **Verify priority appears** in saved questions
4. **Enjoy the full priority feature!** 🎉

The syntax error should now be completely resolved with the corrected SQL script.
