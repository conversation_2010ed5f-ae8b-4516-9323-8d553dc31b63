# 🔧 **Guide de Dépannage - Accès Interface Avocat**

## ❌ **Problème Identifié**

### **Erreur:** "غير مصرح" (Non autorisé) lors de l'accès à `/advocate-dashboard`

### **Causes Possibles:**
1. **Role incorrect** - L'utilisateur n'a pas le role 'advocate' dans la table profiles
2. **Compte non vérifié** - L'utilisateur a `is_verified = false` dans la table profiles
3. **Enregistrement advocate manquant** - Pas d'enregistrement correspondant dans la table advocates
4. **Problème d'authentification** - Session utilisateur non valide

## 🔍 **Diagnostic Étape par Étape**

### **Étape 1: Vérifier l'État du Compte**

1. **Ouvrir Supabase Dashboard** → SQL Editor
2. **Exécuter le script de diagnostic** `FIX_ADVOCATE_ACCESS.sql`
3. **Analyser les résultats** pour identifier le problème

### **Étape 2: Identifier le Problème Spécifique**

#### **Problème A: Role Incorrect**
```sql
-- Si l'utilisateur a role = 'user' au lieu de 'advocate'
UPDATE public.profiles 
SET role = 'advocate', updated_at = now()
WHERE email = '<EMAIL>';
```

#### **Problème B: Compte Non Vérifié**
```sql
-- Si l'utilisateur a is_verified = false
UPDATE public.profiles 
SET is_verified = true, updated_at = now()
WHERE email = '<EMAIL>';
```

#### **Problème C: Enregistrement Advocate Manquant**
```sql
-- Créer l'enregistrement advocate manquant
INSERT INTO public.advocates (profile_id, specializations, bio, hourly_rate, rating, total_reviews, availability, created_at, updated_at)
SELECT id, ARRAY['general'], 'Avocat professionnel', 500.00, 0.0, 0, '{"status": "available"}', now(), now()
FROM public.profiles 
WHERE email = '<EMAIL>';
```

## 🛠️ **Solutions Rapides**

### **Solution 1: Correction Automatique (Recommandée)**

1. **Exécuter** `FIX_ADVOCATE_ACCESS.sql` dans Supabase SQL Editor
2. **Vérifier** les résultats du diagnostic
3. **Tester** l'accès à l'interface advocate-dashboard

### **Solution 2: Créer un Compte Test**

1. **Modifier** l'email dans `CREATE_TEST_ADVOCATE.sql`
2. **Exécuter** le script dans Supabase SQL Editor
3. **Utiliser** ce compte pour tester l'interface

### **Solution 3: Correction Manuelle**

Si vous connaissez l'email de l'avocat:

```sql
-- 1. Corriger le profil
UPDATE public.profiles 
SET 
  role = 'advocate',
  is_verified = true,
  updated_at = now()
WHERE email = '<EMAIL>';

-- 2. Créer l'enregistrement advocate
INSERT INTO public.advocates (
  profile_id,
  specializations,
  bio,
  hourly_rate,
  rating,
  total_reviews,
  availability,
  created_at,
  updated_at
)
SELECT 
  id,
  ARRAY['droit général'],
  'Avocat professionnel',
  500.00,
  0.0,
  0,
  '{"status": "available"}',
  now(),
  now()
FROM public.profiles 
WHERE email = '<EMAIL>'
  AND NOT EXISTS (
    SELECT 1 FROM public.advocates WHERE profile_id = profiles.id
  );
```

## ✅ **Vérification du Succès**

### **Critères de Succès:**
- [x] ✅ `role = 'advocate'` dans la table profiles
- [x] ✅ `is_verified = true` dans la table profiles
- [x] ✅ Enregistrement correspondant dans la table advocates
- [x] ✅ Session utilisateur active et valide

### **Test de Fonctionnement:**
1. **Se connecter** avec le compte avocat
2. **Naviguer** vers `/advocate-dashboard`
3. **Vérifier** que l'interface se charge sans erreur "غير مصرح"
4. **Confirmer** l'affichage du nom de l'avocat et des statistiques

## 🔍 **Dépannage Avancé**

### **Problème: Interface se charge mais reste vide**

#### **Cause:** Problèmes de données ou de permissions RLS
#### **Solution:**
1. **Vérifier** que les fonctions de base de données sont créées
2. **Exécuter** les scripts d'assignment des questions si nécessaire
3. **Vérifier** les politiques RLS pour les avocats

### **Problème: Erreurs JavaScript dans la console**

#### **Cause:** Problèmes de code ou de données manquantes
#### **Solution:**
1. **Ouvrir** la console du navigateur (F12)
2. **Identifier** les erreurs spécifiques
3. **Vérifier** que toutes les tables et fonctions existent

### **Problème: "En attente de vérification" au lieu de "Non autorisé"**

#### **Cause:** Le compte a le bon role mais n'est pas vérifié
#### **Solution:**
```sql
UPDATE public.profiles 
SET is_verified = true, updated_at = now()
WHERE email = '<EMAIL>';
```

## 📋 **Checklist de Vérification Complète**

### **Base de Données:**
- [ ] Table `profiles` existe et contient des données
- [ ] Table `advocates` existe et contient des données
- [ ] L'utilisateur a `role = 'advocate'` dans profiles
- [ ] L'utilisateur a `is_verified = true` dans profiles
- [ ] Enregistrement correspondant existe dans advocates
- [ ] Politiques RLS permettent l'accès aux avocats

### **Application:**
- [ ] Authentification fonctionne correctement
- [ ] Session utilisateur est active
- [ ] Route `/advocate-dashboard` est configurée
- [ ] Composant `ProtectedRoute` fonctionne
- [ ] Pas d'erreurs JavaScript dans la console

### **Interface:**
- [ ] Page se charge sans erreur "غير مصرح"
- [ ] Nom de l'avocat s'affiche correctement
- [ ] Statistiques se chargent
- [ ] Sections "Questions disponibles" et "Mes questions" apparaissent

## 🚀 **Instructions d'Exécution**

### **Étape 1: Diagnostic**
```bash
# Ouvrir Supabase Dashboard
# Aller à SQL Editor
# Exécuter FIX_ADVOCATE_ACCESS.sql
```

### **Étape 2: Correction**
```bash
# Si problèmes détectés, les corrections automatiques s'appliquent
# Sinon, utiliser CREATE_TEST_ADVOCATE.sql pour créer un compte test
```

### **Étape 3: Test**
```bash
# Se connecter avec le compte avocat
# Naviguer vers http://localhost:8081/advocate-dashboard
# Vérifier que l'interface se charge correctement
```

## 📞 **Support Supplémentaire**

Si le problème persiste après avoir suivi ce guide:

1. **Vérifier** les logs Supabase pour des erreurs de base de données
2. **Examiner** la console du navigateur pour des erreurs JavaScript
3. **Confirmer** que toutes les migrations de base de données ont été appliquées
4. **Tester** avec un compte avocat complètement nouveau

## 🎯 **Résultat Attendu**

Après avoir suivi ce guide, vous devriez pouvoir:

- ✅ **Se connecter** avec un compte avocat
- ✅ **Accéder** à `/advocate-dashboard` sans erreur
- ✅ **Voir** l'interface avocat avec toutes les sections
- ✅ **Utiliser** les fonctionnalités d'assignment de questions
- ✅ **Naviguer** dans l'interface sans problèmes d'autorisation

Votre interface avocat devrait maintenant être **entièrement fonctionnelle** ! 🎉
