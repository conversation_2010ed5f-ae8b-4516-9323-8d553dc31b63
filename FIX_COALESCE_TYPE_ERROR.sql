
-- FIX COALESCE TYPE ERROR FOR ADMIN FUNCTIONS
-- Execute this script in Supabase SQL Editor to fix the type mismatch error

-- 1. Drop the existing function with type issues
DROP FUNCTION IF EXISTS public.admin_update_user(UUID, TEXT, TEXT, TEXT, TEXT, BOOLEAN, TEXT[], TEXT, DECIMAL);

-- 2. Create the corrected admin_update_user function with proper type casting
CREATE OR REPLACE FUNCTION public.admin_update_user(
  user_id UUID,
  user_name TEXT DEFAULT NULL,
  user_phone TEXT DEFAULT NULL,
  user_role TEXT DEFAULT NULL,
  subscription_tier TEXT DEFAULT NULL,
  is_verified BOOLEAN DEFAULT NULL,
  advocate_specializations TEXT[] DEFAULT NULL,
  advocate_bio TEXT DEFAULT NULL,
  advocate_hourly_rate DECIMAL DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  result JSON;
  advocate_record RECORD;
  current_profile RECORD;
BEGIN
  -- Check if user exists and get current values
  SELECT * INTO current_profile FROM public.profiles WHERE id = user_id;
  
  IF NOT FOUND THEN
    RETURN json_build_object('success', false, 'message', 'User not found');
  END IF;

  -- Update profile table with explicit type handling
  UPDATE public.profiles 
  SET 
    full_name = CASE 
      WHEN user_name IS NOT NULL THEN user_name 
      ELSE full_name 
    END,
    phone = CASE 
      WHEN user_phone IS NOT NULL THEN user_phone 
      ELSE phone 
    END,
    role = CASE 
      WHEN user_role IS NOT NULL THEN user_role::user_role 
      ELSE role 
    END,
    subscription_tier = CASE 
      WHEN subscription_tier IS NOT NULL THEN subscription_tier::subscription_tier 
      ELSE subscription_tier 
    END,
    is_verified = CASE 
      WHEN is_verified IS NOT NULL THEN is_verified 
      ELSE is_verified 
    END,
    updated_at = NOW()
  WHERE id = user_id;

  -- Handle advocate-specific updates
  IF user_role = 'advocate' OR advocate_specializations IS NOT NULL OR advocate_bio IS NOT NULL OR advocate_hourly_rate IS NOT NULL THEN
    -- Check if advocate record exists
    SELECT * INTO advocate_record FROM public.advocates WHERE profile_id = user_id;
    
    IF FOUND THEN
      -- Update existing advocate record
      UPDATE public.advocates 
      SET 
        specializations = CASE 
          WHEN advocate_specializations IS NOT NULL THEN advocate_specializations 
          ELSE specializations 
        END,
        bio = CASE 
          WHEN advocate_bio IS NOT NULL THEN advocate_bio 
          ELSE bio 
        END,
        hourly_rate = CASE 
          WHEN advocate_hourly_rate IS NOT NULL THEN advocate_hourly_rate 
          ELSE hourly_rate 
        END,
        is_verified = CASE 
          WHEN is_verified IS NOT NULL THEN is_verified 
          ELSE is_verified 
        END,
        updated_at = NOW()
      WHERE profile_id = user_id;
    ELSIF user_role = 'advocate' THEN
      -- Create new advocate record
      INSERT INTO public.advocates (
        profile_id,
        specializations,
        bio,
        hourly_rate,
        is_verified,
        created_at,
        updated_at
      ) VALUES (
        user_id,
        COALESCE(advocate_specializations, ARRAY[]::TEXT[]),
        COALESCE(advocate_bio, ''),
        COALESCE(advocate_hourly_rate, 500.00),
        COALESCE(is_verified, false),
        NOW(),
        NOW()
      );
    END IF;
  END IF;

  result := json_build_object(
    'success', true,
    'user_id', user_id,
    'message', 'User updated successfully'
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'success', false,
    'error', SQLERRM,
    'message', 'Failed to update user: ' || SQLERRM
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Grant permissions
GRANT EXECUTE ON FUNCTION public.admin_update_user TO authenticated;

-- 4. Test the function with a simple update
SELECT 'Function updated successfully! COALESCE type error fixed.' as status;

-- 5. Show the enum types to verify they exist
SELECT 
  'Enum Types Check' as check_type,
  typname as enum_name,
  enumlabel as enum_value
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid 
WHERE typname IN ('user_role', 'subscription_tier')
ORDER BY typname, enumsortorder;
