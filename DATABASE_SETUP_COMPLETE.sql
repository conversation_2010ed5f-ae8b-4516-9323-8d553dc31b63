-- Complete Database Setup for Law App Morocco
-- Run this script in your Supabase SQL editor

-- 1. Create responses table for storing answers to legal questions
CREATE TABLE IF NOT EXISTS public.responses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  question_id UUID NOT NULL REFERENCES public.legal_questions(id) ON DELETE CASCADE,
  advocate_id UUID NOT NULL REFERENCES public.advocates(id) ON DELETE CASCADE,
  response_text TEXT NOT NULL,
  is_approved BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 2. Enable Row Level Security for responses
ALTER TABLE public.responses ENABLE ROW LEVEL SECURITY;

-- 3. Drop existing restrictive policies
DROP POLICY IF EXISTS "Advocates can view assigned questions" ON public.legal_questions;
DROP POLICY IF EXISTS "Advocates can update their own profile" ON public.advocates;

-- 4. Create comprehensive RLS policies for responses
CREATE POLICY "Users can view responses to their questions" ON public.responses
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.legal_questions lq
      WHERE lq.id = question_id AND lq.user_id = auth.uid()
    )
  );

CREATE POLICY "Advocates can view their own responses" ON public.responses
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id
    )
  );

CREATE POLICY "Advocates can create responses" ON public.responses
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id
    )
  );

CREATE POLICY "Advocates can update their own responses" ON public.responses
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id
    )
  );

CREATE POLICY "Admins can manage all responses" ON public.responses
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 5. Create better policies for legal_questions
CREATE POLICY "Advocates can view all questions" ON public.legal_questions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'advocate' AND is_verified = true
    )
  );

CREATE POLICY "Advocates can update questions assigned to them" ON public.legal_questions
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id
    )
  );

CREATE POLICY "Admins can manage all questions" ON public.legal_questions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 6. Create better policies for advocates table
CREATE POLICY "Advocates can manage their own profile" ON public.advocates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND id = profile_id
    )
  );

CREATE POLICY "Admins can manage all advocates" ON public.advocates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 7. Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_responses_question_id ON public.responses(question_id);
CREATE INDEX IF NOT EXISTS idx_responses_advocate_id ON public.responses(advocate_id);
CREATE INDEX IF NOT EXISTS idx_legal_questions_advocate_id ON public.legal_questions(advocate_id);
CREATE INDEX IF NOT EXISTS idx_legal_questions_user_id ON public.legal_questions(user_id);
CREATE INDEX IF NOT EXISTS idx_advocates_profile_id ON public.advocates(profile_id);

-- 8. Create function to automatically update question status when response is added
CREATE OR REPLACE FUNCTION public.update_question_status_on_response()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the question to mark it as answered
  UPDATE public.legal_questions
  SET 
    is_answered = true,
    status = 'answered',
    updated_at = now()
  WHERE id = NEW.question_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. Create trigger to automatically update question status
DROP TRIGGER IF EXISTS on_response_created ON public.responses;
CREATE TRIGGER on_response_created
  AFTER INSERT ON public.responses
  FOR EACH ROW EXECUTE PROCEDURE public.update_question_status_on_response();

-- 10. Create function to assign question to advocate
CREATE OR REPLACE FUNCTION public.assign_question_to_advocate(
  question_uuid UUID,
  advocate_profile_uuid UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  advocate_uuid UUID;
BEGIN
  -- Get advocate ID from profile ID
  SELECT id INTO advocate_uuid
  FROM public.advocates
  WHERE profile_id = advocate_profile_uuid;
  
  IF advocate_uuid IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Update the question
  UPDATE public.legal_questions
  SET 
    advocate_id = advocate_uuid,
    status = 'assigned',
    updated_at = now()
  WHERE id = question_uuid;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 11. Insert sample data for testing (optional)
-- Uncomment the following lines if you want sample data

/*
-- Insert sample advocates (make sure these profile IDs exist)
INSERT INTO public.advocates (profile_id, specializations, bio, hourly_rate, is_featured, rating, total_reviews)
VALUES 
  ('your-advocate-profile-id-1', ARRAY['family', 'civil'], 'محامي متخصص في قانون الأسرة والقانون المدني', 500.00, true, 4.5, 25),
  ('your-advocate-profile-id-2', ARRAY['commercial', 'labor'], 'محامي متخصص في القانون التجاري وقانون العمل', 600.00, false, 4.2, 18);

-- Insert sample questions
INSERT INTO public.legal_questions (user_id, title, description, category, status)
VALUES 
  ('your-user-profile-id', 'سؤال حول الطلاق', 'أريد معرفة إجراءات الطلاق في المغرب', 'family', 'pending'),
  ('your-user-profile-id', 'سؤال حول العقود', 'ما هي شروط صحة العقد؟', 'civil', 'pending');
*/

-- 12. Grant necessary permissions
GRANT ALL ON public.responses TO authenticated;
GRANT ALL ON public.responses TO service_role;
