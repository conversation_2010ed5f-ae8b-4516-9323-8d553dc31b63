-- CRÉER UN COMPTE AVOCAT DE TEST
-- Exécutez ce script pour créer un compte avocat de test fonctionnel

-- 1. Créer un profil avocat de test (remplacez l'email par celui que vous voulez utiliser)
-- IMPORTANT: Remplacez '<EMAIL>' par l'email réel que vous voulez utiliser

DO $$
DECLARE
  test_email TEXT := '<EMAIL>'; -- CHANGEZ CET EMAIL
  test_name TEXT := 'Avocat Test';
  test_user_id UUID;
BEGIN
  -- Générer un UUID pour le test
  test_user_id := gen_random_uuid();
  
  -- Insérer le profil avocat
  INSERT INTO public.profiles (
    id,
    email,
    full_name,
    role,
    subscription_tier,
    is_verified,
    created_at,
    updated_at
  ) VALUES (
    test_user_id,
    test_email,
    test_name,
    'advocate',
    'premium',
    true, -- Vérifié automatiquement
    now(),
    now()
  ) ON CONFLICT (email) DO UPDATE SET
    role = 'advocate',
    is_verified = true,
    full_name = test_name,
    updated_at = now();
  
  -- Récupérer l'ID réel (en cas de conflit)
  SELECT id INTO test_user_id FROM public.profiles WHERE email = test_email;
  
  -- Créer l'enregistrement advocate correspondant
  INSERT INTO public.advocates (
    profile_id,
    specializations,
    bio,
    hourly_rate,
    rating,
    total_reviews,
    availability,
    is_featured,
    created_at,
    updated_at
  ) VALUES (
    test_user_id,
    ARRAY['droit civil', 'droit commercial', 'droit de la famille'],
    'Avocat expérimenté spécialisé en droit civil et commercial. Plus de 10 ans d''expérience dans le conseil juridique et la représentation devant les tribunaux.',
    750.00,
    4.8,
    25,
    '{"status": "available", "hours": "9h-17h", "days": ["lundi", "mardi", "mercredi", "jeudi", "vendredi"]}',
    true,
    now(),
    now()
  ) ON CONFLICT (profile_id) DO UPDATE SET
    specializations = ARRAY['droit civil', 'droit commercial', 'droit de la famille'],
    bio = 'Avocat expérimenté spécialisé en droit civil et commercial. Plus de 10 ans d''expérience dans le conseil juridique et la représentation devant les tribunaux.',
    hourly_rate = 750.00,
    rating = 4.8,
    total_reviews = 25,
    availability = '{"status": "available", "hours": "9h-17h", "days": ["lundi", "mardi", "mercredi", "jeudi", "vendredi"]}',
    is_featured = true,
    updated_at = now();
  
  RAISE NOTICE 'Compte avocat de test créé avec succès pour: %', test_email;
  RAISE NOTICE 'ID utilisateur: %', test_user_id;
END $$;

-- 2. Vérifier que le compte a été créé correctement
SELECT 
  'VÉRIFICATION DU COMPTE TEST' as section,
  p.id,
  p.email,
  p.full_name,
  p.role,
  p.is_verified,
  p.subscription_tier,
  a.specializations,
  a.bio,
  a.hourly_rate,
  a.rating,
  CASE 
    WHEN p.role = 'advocate' AND p.is_verified = true AND a.id IS NOT NULL 
    THEN '✅ COMPTE PRÊT POUR L''ACCÈS'
    ELSE '❌ PROBLÈME DE CONFIGURATION'
  END as status
FROM public.profiles p
LEFT JOIN public.advocates a ON p.id = a.profile_id
WHERE p.email = '<EMAIL>'; -- Utilisez le même email que ci-dessus

-- 3. Instructions pour l'authentification
SELECT 
  'INSTRUCTIONS D''AUTHENTIFICATION' as section,
  'Pour vous connecter avec ce compte de test:' as step_1,
  '1. Allez sur la page de connexion de votre application' as step_2,
  '2. Utilisez l''email: <EMAIL>' as step_3,
  '3. Le mot de passe doit être défini via Supabase Auth' as step_4,
  '4. Ou créez le compte via l''interface d''inscription' as step_5;

-- 4. Alternative: Mettre à jour un compte existant
-- Si vous avez déjà un compte et voulez le transformer en avocat, utilisez ceci:
/*
UPDATE public.profiles 
SET 
  role = 'advocate',
  is_verified = true,
  updated_at = now()
WHERE email = '<EMAIL>';

-- Puis créer l'enregistrement advocate
INSERT INTO public.advocates (
  profile_id,
  specializations,
  bio,
  hourly_rate,
  rating,
  total_reviews,
  availability,
  created_at,
  updated_at
)
SELECT 
  id,
  ARRAY['general'],
  'Avocat professionnel',
  500.00,
  0.0,
  0,
  '{"status": "available"}',
  now(),
  now()
FROM public.profiles 
WHERE email = '<EMAIL>'
  AND NOT EXISTS (
    SELECT 1 FROM public.advocates WHERE profile_id = profiles.id
  );
*/

-- 5. Message final
SELECT 
  'CRÉATION TERMINÉE' as status,
  'Le compte avocat de test a été créé avec succès.' as message,
  'Vous pouvez maintenant vous connecter et accéder à l''interface advocate-dashboard.' as next_step;
