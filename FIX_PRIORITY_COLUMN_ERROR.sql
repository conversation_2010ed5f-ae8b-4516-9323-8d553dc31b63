-- FIX FOR PRIORITY COLUMN ERROR IN LEGAL_QUESTIONS TABLE
-- Execute this script in Supabase SQL Editor to fix the missing priority column

-- 1. Check if priority column exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'legal_questions' 
        AND column_name = 'priority'
        AND table_schema = 'public'
    ) THEN
        -- Add the priority column if it doesn't exist
        ALTER TABLE public.legal_questions 
        ADD COLUMN priority TEXT DEFAULT 'medium';
        
        RAISE NOTICE 'Priority column added successfully to legal_questions table';
    ELSE
        RAISE NOTICE 'Priority column already exists in legal_questions table';
    END IF;
END $$;

-- 2. Update existing records to have default priority if they are NULL
UPDATE public.legal_questions 
SET priority = 'medium' 
WHERE priority IS NULL;

-- 3. Add constraint to ensure priority has valid values
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'legal_questions_priority_check'
    ) THEN
        ALTER TABLE public.legal_questions 
        ADD CONSTRAINT legal_questions_priority_check 
        CHECK (priority IN ('low', 'medium', 'high', 'urgent'));
        
        RAISE NOTICE 'Priority constraint added successfully';
    ELSE
        RAISE NOTICE 'Priority constraint already exists';
    END IF;
END $$;

-- 4. Verify the column was added correctly
SELECT 
    column_name,
    data_type,
    column_default,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'legal_questions' 
    AND table_schema = 'public'
    AND column_name = 'priority';

-- 5. Show current table structure
SELECT 
    column_name,
    data_type,
    column_default,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'legal_questions' 
    AND table_schema = 'public'
ORDER BY ordinal_position;

-- Success message
SELECT 'Priority column fix completed successfully!' as status,
       'You can now submit questions with priority levels.' as message;
