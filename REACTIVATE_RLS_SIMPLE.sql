-- RÉACTIVATION RLS AVEC POLITIQUES SIMPLIFIÉES
-- Ex<PERSON><PERSON>z ce script SEULEMENT après avoir testé que tout fonctionne

-- 1. Réactiver RLS sur les tables principales
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.advocates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.legal_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.responses ENABLE ROW LEVEL SECURITY;

-- 2. <PERSON><PERSON><PERSON> des politiques très permissives pour éviter les blocages
CREATE POLICY "profiles_permissive" ON public.profiles
  FOR ALL USING (
    auth.uid() = id OR 
    auth.uid() IN (
      SELECT id FROM public.profiles WHERE role = 'admin'
    )
  );

CREATE POLICY "advocates_permissive" ON public.advocates
  FOR ALL USING (
    auth.uid() = profile_id OR
    auth.uid() IN (
      SELECT id FROM public.profiles WHERE role = 'admin'
    )
  );

CREATE POLICY "questions_permissive" ON public.legal_questions
  FOR ALL USING (
    auth.uid() = user_id OR
    auth.uid() IN (
      SELECT id FROM public.profiles WHERE role IN ('admin', 'advocate') AND is_verified = true
    )
  );

CREATE POLICY "responses_permissive" ON public.responses
  FOR ALL USING (
    auth.uid() IN (
      SELECT id FROM public.profiles WHERE role IN ('admin', 'advocate', 'user')
    )
  );

-- 3. Message de succès
SELECT 'RLS réactivé avec politiques permissives.' as status;
