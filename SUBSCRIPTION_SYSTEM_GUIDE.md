# Guide du Système d'Abonnement - Law App Morocco

## Vue d'ensemble

Ce guide explique comment utiliser et intégrer le système d'abonnement complet pour la plateforme d'assistance juridique Law App Morocco.

## Architecture du Système

### 1. Base de Données (Supabase)

Le système utilise 4 tables principales :

- **`subscription_plans`** : Plans d'abonnement disponibles
- **`user_subscriptions`** : Abonnements des utilisateurs
- **`subscription_preferences`** : Préférences de notification
- **`subscription_analytics`** : Métriques et statistiques

### 2. Services

- **`SubscriptionService`** : Service principal pour toutes les opérations d'abonnement
- **`useSubscription`** : Hook React pour la gestion d'état des abonnements

### 3. Composants

- **`SubscriptionForm`** : Formulaire de sélection de plan
- **`SubscriptionManagement`** : Interface complète de gestion
- **`SubscriptionStats`** : Tableau de bord des statistiques
- **`SubscriptionPreferences`** : Gestion des préférences
- **`SubscriptionBadge`** : Affichage du statut d'abonnement
- **`SubscriptionLimitNotification`** : Notifications de limites

## Installation et Configuration

### 1. Exécuter le Script de Base de Données

```sql
-- Exécuter le fichier SUBSCRIPTION_SYSTEM_SCHEMA.sql dans Supabase
-- Cela créera toutes les tables, politiques RLS, et données initiales
```

### 2. Importer les Composants

```typescript
import { 
  SubscriptionManagement,
  SubscriptionForm,
  SubscriptionStats,
  SubscriptionBadge,
  useSubscription
} from '@/components/subscription';
```

### 3. Utiliser le Hook useSubscription

```typescript
const {
  subscriptionDetails,
  currentPlan,
  hasActiveSubscription,
  isFreePlan,
  isPremiumPlan,
  isProfessionalPlan,
  canAccessFeature,
  subscribe,
  cancelSubscription
} = useSubscription();
```

## Utilisation des Composants

### 1. Page d'Abonnement Complète

```typescript
import { SubscriptionManagement } from '@/components/subscription';

function SubscriptionPage() {
  const { user } = useAuth();
  
  return (
    <SubscriptionManagement userId={user.id} />
  );
}
```

### 2. Formulaire de Sélection de Plan

```typescript
import { SubscriptionForm } from '@/components/subscription';

function PricingPage() {
  const { user } = useAuth();
  
  return (
    <SubscriptionForm 
      userId={user.id}
      onSubscriptionChange={(planId) => {
        console.log('Nouveau plan:', planId);
      }}
    />
  );
}
```

### 3. Badge de Statut d'Abonnement

```typescript
import { SubscriptionBadge } from '@/components/subscription';

function Navbar() {
  return (
    <div className="navbar">
      <SubscriptionBadge 
        showUpgradeButton={true}
        onUpgradeClick={() => navigate('/subscription')}
      />
    </div>
  );
}
```

### 4. Contrôle d'Accès aux Fonctionnalités

```typescript
import { useSubscription } from '@/hooks/useSubscription';

function PremiumFeature() {
  const { canAccessFeature } = useSubscription();
  
  if (!canAccessFeature('unlimited_questions')) {
    return <UpgradePrompt />;
  }
  
  return <PremiumContent />;
}
```

### 5. Notifications de Limites

```typescript
import { SubscriptionLimitNotification } from '@/components/subscription';

function QuestionForm() {
  const [questionsUsed, setQuestionsUsed] = useState(5);
  const questionsLimit = 10;
  
  return (
    <div>
      <UsageQuotaNotification
        used={questionsUsed}
        limit={questionsLimit}
        feature="الأسئلة القانونية"
        onUpgrade={() => navigate('/subscription')}
      />
      <QuestionFormContent />
    </div>
  );
}
```

## Gestion des Fonctionnalités par Plan

### Plans Disponibles

1. **Gratuit (مجاني)**
   - 1 question juridique par mois
   - Création de documents limitée
   - Essai gratuit de 7 jours

2. **Premium (المستخدم المتميز)**
   - Questions juridiques illimitées
   - Création de documents illimitée
   - Support prioritaire
   - Accès complet aux avocats

3. **Professionnel (المحامي المتميز)**
   - Toutes les fonctionnalités Premium
   - Profil personnel d'avocat
   - Outils avancés et analyses
   - Priorité dans l'affichage du profil

### Contrôle d'Accès

```typescript
// Vérifier l'accès à une fonctionnalité
const { canAccessFeature } = useSubscription();

// Exemples d'utilisation
const canAskUnlimitedQuestions = canAccessFeature('unlimited_questions');
const canCreateLawyerProfile = canAccessFeature('lawyer_profile');
const canAccessAnalytics = canAccessFeature('advanced_analytics');
```

## API du Service d'Abonnement

### Méthodes Principales

```typescript
// Obtenir les plans disponibles
const { data: plans } = await SubscriptionService.getSubscriptionPlans();

// Obtenir les détails d'abonnement d'un utilisateur
const { data: details } = await SubscriptionService.getUserSubscriptionDetails(userId);

// S'abonner à un plan
const { data } = await SubscriptionService.subscribeUser(userId, planId, paymentMethod);

// Annuler un abonnement
const { data } = await SubscriptionService.cancelUserSubscription(userId);

// Mettre à jour les préférences
const { data } = await SubscriptionService.updateSubscriptionPreferences(userId, preferences);

// Obtenir les statistiques (admin)
const { data: stats } = await SubscriptionService.getSubscriptionStats();
```

## Gestion des Préférences de Notification

### Types de Notifications

- **Email** : Notifications par email
- **SMS** : Messages texte (nécessite numéro de téléphone)
- **In-App** : Notifications dans l'application
- **Mises à jour juridiques** : Actualités et changements légaux
- **Réponses aux questions** : Notifications de réponses
- **Annonces système** : Mises à jour importantes
- **Emails marketing** : Offres et nouveautés
- **Résumé hebdomadaire** : Digest des activités

### Utilisation

```typescript
import { SubscriptionPreferencesComponent } from '@/components/subscription';

function SettingsPage() {
  const { user } = useAuth();
  
  return (
    <SubscriptionPreferencesComponent userId={user.id} />
  );
}
```

## Statistiques et Analytics

### Métriques Disponibles

- Nombre total d'abonnés
- Taux de conversion
- Revenus totaux
- Répartition par plan
- Nouveaux abonnements
- Annulations

### Utilisation (Admin uniquement)

```typescript
import { SubscriptionStats } from '@/components/subscription';

function AdminDashboard() {
  return (
    <div>
      <h1>Tableau de Bord Admin</h1>
      <SubscriptionStats />
    </div>
  );
}
```

## Sécurité et Permissions

### Row Level Security (RLS)

- Les utilisateurs ne peuvent voir que leurs propres abonnements
- Les admins ont accès à toutes les données
- Les plans publics sont visibles par tous
- Les analytics sont réservés aux admins

### Validation

- Validation côté client et serveur
- Vérification des permissions avant chaque action
- Gestion des erreurs et états de chargement

## Intégration avec l'Authentification

Le système s'intègre automatiquement avec le système d'authentification existant :

```typescript
const { user } = useAuth();
const { hasActiveSubscription } = useSubscription();

// Utiliser ensemble pour contrôler l'accès
if (user && hasActiveSubscription) {
  // Utilisateur connecté avec abonnement actif
}
```

## Personnalisation et Extension

### Ajouter de Nouvelles Fonctionnalités

1. Mettre à jour les types dans `src/types/subscription.ts`
2. Ajouter la logique dans `canAccessFeature`
3. Créer les composants de contrôle d'accès nécessaires

### Ajouter de Nouveaux Plans

1. Insérer dans la table `subscription_plans`
2. Mettre à jour les constantes de limites
3. Ajuster la logique de contrôle d'accès

## Dépannage

### Problèmes Courants

1. **Erreur RLS** : Vérifier que l'utilisateur est authentifié
2. **Plans non visibles** : Vérifier que `is_active = true`
3. **Permissions manquantes** : Vérifier les politiques RLS
4. **Erreurs de type** : Vérifier les imports TypeScript

### Logs et Debugging

Le service inclut des logs détaillés pour faciliter le débogage :

```typescript
// Activer les logs en développement
console.log('🔍 Getting subscription details for user:', userId);
console.log('✅ User subscribed successfully:', data);
```

## Support et Maintenance

### Mise à Jour des Analytics

```typescript
// Mettre à jour manuellement les statistiques
await SubscriptionService.updateSubscriptionAnalytics();
```

### Nettoyage des Données

- Les abonnements expirés sont automatiquement marqués
- Les préférences par défaut sont créées automatiquement
- Les analytics sont mis à jour quotidiennement

## Conclusion

Ce système d'abonnement offre une solution complète et évolutive pour gérer les abonnements, les préférences utilisateur, et les statistiques. Il est conçu pour être facilement intégrable et personnalisable selon les besoins spécifiques de l'application.
