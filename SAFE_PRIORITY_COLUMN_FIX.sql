-- SAFE PRIORITY COLUMN FIX - Handles existing constraints
-- Execute this script in Supabase SQL Editor

-- 1. Add priority column if it doesn't exist (safe)
ALTER TABLE public.legal_questions 
ADD COLUMN IF NOT EXISTS priority TEXT DEFAULT 'medium';

-- 2. Update existing records to have default priority
UPDATE public.legal_questions 
SET priority = 'medium' 
WHERE priority IS NULL OR priority = '';

-- 3. Check if constraint already exists and only add if needed
DO $$
BEGIN
    -- Only add constraint if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'legal_questions_priority_check'
        AND table_name = 'legal_questions'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.legal_questions 
        ADD CONSTRAINT legal_questions_priority_check 
        CHECK (priority IN ('low', 'medium', 'high', 'urgent'));
        
        RAISE NOTICE 'Priority constraint added successfully';
    ELSE
        RAISE NOTICE 'Priority constraint already exists - skipping';
    END IF;
END $$;

-- 4. Verify the setup is complete
SELECT 
    'Priority column setup verified!' as status,
    column_name,
    data_type,
    column_default,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'legal_questions' 
    AND table_schema = 'public'
    AND column_name = 'priority';

-- 5. Check constraint exists
SELECT 
    'Constraint status:' as info,
    constraint_name,
    constraint_type
FROM information_schema.table_constraints 
WHERE table_name = 'legal_questions' 
    AND constraint_name = 'legal_questions_priority_check';

-- 6. Test that priority values work
SELECT 'Setup complete - ready to use!' as message;
