# 🚀 **Guide Complet - Nouveau Projet Supabase Law App Morocco**

## 📋 **Étapes d'Installation**

### **1. Création du Projet Supabase**
1. **Allez sur** [supabase.com](https://supabase.com)
2. **Cliquez sur** "New Project"
3. **Choisissez votre organisation**
4. **Nommez le projet** : `law-app-morocco-v2`
5. **Choisissez une région** proche (Europe West par exemple)
6. **Définissez un mot de passe** pour la base de données
7. **Cliquez sur** "Create new project"

### **2. Configuration de la Base de Données**
1. **Attendez** que le projet soit créé (2-3 minutes)
2. **Allez dans** "SQL Editor" dans le menu de gauche
3. **Copiez tout le contenu** du fichier `COMPLETE_DATABASE_SETUP.sql`
4. **Collez-le** dans l'éditeur SQL
5. **C<PERSON>z sur** "Run" pour exécuter le script
6. **Attendez** la fin de l'exécution (1-2 minutes)

### **3. Vérification de l'Installation**
```sql
-- Vérifiez que toutes les tables sont créées
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Vérifiez les comptes créés
SELECT email, full_name, role, is_verified 
FROM public.profiles 
ORDER BY role, email;
```

## 🔑 **Comptes Pré-configurés**

### **👨‍💼 Administrateurs**
| Email | Nom | Rôle | Statut |
|-------|-----|------|--------|
| `<EMAIL>` | Administrateur Principal | admin | ✅ Vérifié |
| `<EMAIL>` | Modérateur Système | admin | ✅ Vérifié |

### **⚖️ Avocats**
| Email | Nom | Spécialités | Tarif/h |
|-------|-----|-------------|---------|
| `<EMAIL>` | Me. Ahmed Benali | Droit famille, Civil | 800 DH |
| `<EMAIL>` | Me. Fatima Alaoui | Droit commercial | 1200 DH |
| `<EMAIL>` | Me. Omar Tazi | Droit pénal | 1000 DH |

### **👥 Utilisateurs Test**
| Email | Nom | Type | Statut |
|-------|-----|------|--------|
| `<EMAIL>` | Youssef Mansouri | Gratuit | ✅ Actif |
| `<EMAIL>` | Aicha Benjelloun | Pro User | ✅ Actif |

## 🎯 **Comment Se Connecter**

### **Méthode 1: Inscription Normale**
1. **Allez sur votre application**
2. **Cliquez sur** "S'inscrire"
3. **Utilisez un des emails** listés ci-dessus
4. **Définissez un mot de passe**
5. **Le rôle sera automatiquement assigné**

### **Méthode 2: Création Manuelle**
```sql
-- Si vous voulez créer votre propre admin
INSERT INTO public.profiles (id, email, full_name, role, is_verified)
VALUES 
  (gen_random_uuid(), '<EMAIL>', 'Votre Nom', 'admin', true);
```

## 📊 **Données de Test Incluses**

### **Questions Légales (4 exemples)**
- ✅ **2 questions avec réponses** d'avocats
- ⏳ **2 questions en attente** de réponse
- 📝 **Catégories** : famille, commercial, travail

### **Réponses d'Avocats (2 exemples)**
- 📋 **Procédure de divorce** - Réponse détaillée
- 🏢 **Création SARL** - Guide complet

### **Documents Légaux (2 exemples)**
- 🏠 **Contrat de bail** résidentiel
- 📄 **Procuration** générale

## 🔧 **Configuration Frontend**

### **Variables d'Environnement**
Créez un fichier `.env.local` :
```env
VITE_SUPABASE_URL=https://votre-projet-id.supabase.co
VITE_SUPABASE_ANON_KEY=votre-clé-publique
```

### **Récupération des Clés**
1. **Allez dans** Settings → API
2. **Copiez** l'URL du projet
3. **Copiez** la clé `anon/public`

## 🎮 **Test de l'Application**

### **Test Admin**
1. **Connectez-vous** avec `<EMAIL>`
2. **Allez sur** `/admin`
3. **Vérifiez** l'onglet "Utilisateurs"
4. **Testez** la vérification d'avocats

### **Test Avocat**
1. **Connectez-vous** avec `<EMAIL>`
2. **Allez sur** `/advocate-dashboard`
3. **Voyez** les questions assignées
4. **Testez** la création de réponses

### **Test Utilisateur**
1. **Connectez-vous** avec `<EMAIL>`
2. **Allez sur** `/questions`
3. **Créez** une nouvelle question
4. **Sélectionnez** un avocat

## 🔍 **Requêtes Utiles**

### **Voir Toutes les Questions**
```sql
SELECT 
  lq.title,
  lq.category,
  lq.status,
  p.full_name as user_name,
  a_prof.full_name as advocate_name
FROM legal_questions lq
JOIN profiles p ON lq.user_id = p.id
LEFT JOIN advocates a ON lq.advocate_id = a.id
LEFT JOIN profiles a_prof ON a.profile_id = a_prof.id
ORDER BY lq.created_at DESC;
```

### **Statistiques Système**
```sql
SELECT 
  (SELECT COUNT(*) FROM profiles WHERE role = 'user') as total_users,
  (SELECT COUNT(*) FROM profiles WHERE role = 'advocate') as total_advocates,
  (SELECT COUNT(*) FROM legal_questions) as total_questions,
  (SELECT COUNT(*) FROM responses) as total_responses;
```

### **Promouvoir un Utilisateur en Admin**
```sql
UPDATE public.profiles 
SET role = 'admin', is_verified = true 
WHERE email = '<EMAIL>';
```

## 🚨 **Dépannage**

### **Si les Comptes ne Fonctionnent Pas**
```sql
-- Vérifiez les profils
SELECT * FROM public.profiles WHERE email LIKE '%@lawapp.ma';

-- Réinitialisez les rôles
UPDATE public.profiles SET role = 'admin', is_verified = true 
WHERE email IN ('<EMAIL>', '<EMAIL>');
```

### **Si les RLS Bloquent l'Accès**
```sql
-- Désactivez temporairement RLS pour debug
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;
-- N'oubliez pas de le réactiver après !
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
```

## 🎉 **Félicitations !**

Votre nouveau projet Law App Morocco est maintenant :
- ✅ **Configuré** avec toutes les tables
- ✅ **Sécurisé** avec RLS
- ✅ **Peuplé** avec des données de test
- ✅ **Prêt** pour le développement

**Prochaines étapes :**
1. 🔗 **Connectez** votre frontend
2. 🧪 **Testez** toutes les fonctionnalités
3. 🚀 **Déployez** en production
4. 📧 **Configurez** les notifications email

**Support :** Si vous rencontrez des problèmes, vérifiez les logs Supabase et les politiques RLS ! 🛠️
