-- PER<PERSON>OR<PERSON><PERSON><PERSON> OPTIMIZATION FOR LAW APP MOROCCO
-- Execute this script to improve database performance and query speed

-- 1. Create comprehensive indexes for better query performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_profiles_role_verified 
ON public.profiles(role, is_verified) WHERE is_verified = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_profiles_email_lower 
ON public.profiles(lower(email));

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_profiles_subscription_tier 
ON public.profiles(subscription_tier);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_advocates_specializations_gin 
ON public.advocates USING gin(specializations);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_advocates_rating 
ON public.advocates(rating DESC) WHERE rating > 0;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_advocates_hourly_rate 
ON public.advocates(hourly_rate);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_legal_questions_status_created 
ON public.legal_questions(status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_legal_questions_category 
ON public.legal_questions(category);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_legal_questions_answered 
ON public.legal_questions(is_answered, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_legal_questions_user_status 
ON public.legal_questions(user_id, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_legal_questions_advocate_status 
ON public.legal_questions(advocate_id, status) WHERE advocate_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_responses_created_approved 
ON public.responses(created_at DESC, is_approved);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_responses_advocate_created 
ON public.responses(advocate_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_legal_documents_user_status 
ON public.legal_documents(user_id, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_legal_documents_created 
ON public.legal_documents(created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscriptions_status_period 
ON public.subscriptions(status, current_period_end);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_usage_tracking_reset_date 
ON public.usage_tracking(last_reset_date);

-- 2. Create materialized view for dashboard statistics (faster loading)
CREATE MATERIALIZED VIEW IF NOT EXISTS public.dashboard_stats AS
SELECT 
  (SELECT COUNT(*) FROM public.profiles) as total_users,
  (SELECT COUNT(*) FROM public.profiles WHERE role = 'advocate') as total_advocates,
  (SELECT COUNT(*) FROM public.profiles WHERE role = 'advocate' AND is_verified = true) as verified_advocates,
  (SELECT COUNT(*) FROM public.legal_questions) as total_questions,
  (SELECT COUNT(*) FROM public.legal_questions WHERE is_answered = true) as answered_questions,
  (SELECT COUNT(*) FROM public.responses) as total_responses,
  (SELECT COUNT(*) FROM public.legal_documents) as total_documents,
  (SELECT COUNT(*) FROM public.subscriptions WHERE status = 'active') as active_subscriptions,
  (SELECT COUNT(*) FROM public.profiles WHERE created_at >= date_trunc('month', now())) as new_users_this_month,
  (SELECT COUNT(*) FROM public.legal_documents WHERE created_at >= date_trunc('month', now())) as new_documents_this_month,
  now() as last_updated;

-- Create unique index for materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_dashboard_stats_last_updated 
ON public.dashboard_stats(last_updated);

-- 3. Create function to refresh dashboard stats
CREATE OR REPLACE FUNCTION public.refresh_dashboard_stats()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY public.dashboard_stats;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Create optimized views for common queries

-- View for advocate profiles with statistics
CREATE OR REPLACE VIEW public.advocate_profiles_with_stats AS
SELECT 
  p.id,
  p.email,
  p.full_name,
  p.phone,
  p.is_verified,
  p.created_at,
  a.specializations,
  a.bio,
  a.hourly_rate,
  a.is_featured,
  a.rating,
  a.total_reviews,
  a.availability,
  (SELECT COUNT(*) FROM public.legal_questions lq WHERE lq.advocate_id = a.id) as total_questions_handled,
  (SELECT COUNT(*) FROM public.legal_questions lq WHERE lq.advocate_id = a.id AND lq.is_answered = true) as questions_answered,
  (SELECT COUNT(*) FROM public.responses r WHERE r.advocate_id = a.id) as total_responses
FROM public.profiles p
JOIN public.advocates a ON p.id = a.profile_id
WHERE p.role = 'advocate';

-- View for questions with full details
CREATE OR REPLACE VIEW public.questions_with_details AS
SELECT 
  lq.id,
  lq.title,
  lq.description,
  lq.category,
  lq.status,
  lq.is_answered,
  lq.created_at,
  lq.updated_at,
  up.email as user_email,
  up.full_name as user_name,
  ap.email as advocate_email,
  ap.full_name as advocate_name,
  (SELECT COUNT(*) FROM public.responses r WHERE r.question_id = lq.id) as response_count
FROM public.legal_questions lq
JOIN public.profiles up ON lq.user_id = up.id
LEFT JOIN public.advocates a ON lq.advocate_id = a.id
LEFT JOIN public.profiles ap ON a.profile_id = ap.id;

-- 5. Create function for efficient user search
CREATE OR REPLACE FUNCTION public.search_users(
  search_term TEXT,
  role_filter TEXT DEFAULT NULL,
  limit_count INTEGER DEFAULT 50
)
RETURNS TABLE(
  id UUID,
  email TEXT,
  full_name TEXT,
  role TEXT,
  is_verified BOOLEAN,
  created_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.email,
    p.full_name,
    p.role::TEXT,
    p.is_verified,
    p.created_at
  FROM public.profiles p
  WHERE 
    (search_term IS NULL OR 
     lower(p.email) LIKE lower('%' || search_term || '%') OR
     lower(p.full_name) LIKE lower('%' || search_term || '%'))
    AND (role_filter IS NULL OR p.role::TEXT = role_filter)
  ORDER BY p.created_at DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Create function for efficient question search
CREATE OR REPLACE FUNCTION public.search_questions(
  search_term TEXT DEFAULT NULL,
  category_filter TEXT DEFAULT NULL,
  status_filter TEXT DEFAULT NULL,
  advocate_id_filter UUID DEFAULT NULL,
  limit_count INTEGER DEFAULT 50
)
RETURNS TABLE(
  id UUID,
  title TEXT,
  description TEXT,
  category TEXT,
  status TEXT,
  is_answered BOOLEAN,
  created_at TIMESTAMPTZ,
  user_name TEXT,
  advocate_name TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    qwd.id,
    qwd.title,
    qwd.description,
    qwd.category,
    qwd.status,
    qwd.is_answered,
    qwd.created_at,
    qwd.user_name,
    qwd.advocate_name
  FROM public.questions_with_details qwd
  WHERE 
    (search_term IS NULL OR 
     lower(qwd.title) LIKE lower('%' || search_term || '%') OR
     lower(qwd.description) LIKE lower('%' || search_term || '%'))
    AND (category_filter IS NULL OR qwd.category = category_filter)
    AND (status_filter IS NULL OR qwd.status = status_filter)
    AND (advocate_id_filter IS NULL OR qwd.id IN (
      SELECT lq.id FROM public.legal_questions lq 
      JOIN public.advocates a ON lq.advocate_id = a.id 
      WHERE a.profile_id = advocate_id_filter
    ))
  ORDER BY qwd.created_at DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Create automated maintenance procedures

-- Function to clean up old sessions and temporary data
CREATE OR REPLACE FUNCTION public.cleanup_old_data()
RETURNS void AS $$
BEGIN
  -- Clean up old usage tracking records (older than 1 year)
  DELETE FROM public.usage_tracking 
  WHERE last_reset_date < now() - interval '1 year';
  
  -- Update statistics
  ANALYZE public.profiles;
  ANALYZE public.advocates;
  ANALYZE public.legal_questions;
  ANALYZE public.responses;
  ANALYZE public.legal_documents;
  
  -- Refresh materialized view
  PERFORM public.refresh_dashboard_stats();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Grant permissions for new functions and views
GRANT SELECT ON public.dashboard_stats TO authenticated;
GRANT SELECT ON public.advocate_profiles_with_stats TO authenticated;
GRANT SELECT ON public.questions_with_details TO authenticated;
GRANT EXECUTE ON FUNCTION public.refresh_dashboard_stats TO authenticated;
GRANT EXECUTE ON FUNCTION public.search_users TO authenticated;
GRANT EXECUTE ON FUNCTION public.search_questions TO authenticated;
GRANT EXECUTE ON FUNCTION public.cleanup_old_data TO service_role;

-- 9. Set up automatic statistics refresh (run this manually when needed)
-- SELECT public.refresh_dashboard_stats();

-- 10. Performance monitoring queries (for debugging)

-- Check index usage
CREATE OR REPLACE FUNCTION public.get_index_usage()
RETURNS TABLE(
  table_name TEXT,
  index_name TEXT,
  index_scans BIGINT,
  tuples_read BIGINT,
  tuples_fetched BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    schemaname||'.'||tablename as table_name,
    indexname as index_name,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
  FROM pg_stat_user_indexes 
  WHERE schemaname = 'public'
  ORDER BY idx_scan DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Check table sizes
CREATE OR REPLACE FUNCTION public.get_table_sizes()
RETURNS TABLE(
  table_name TEXT,
  row_count BIGINT,
  total_size TEXT,
  index_size TEXT,
  table_size TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    schemaname||'.'||tablename as table_name,
    n_tup_ins - n_tup_del as row_count,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_indexes_size(schemaname||'.'||tablename)) as index_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size
  FROM pg_stat_user_tables 
  WHERE schemaname = 'public'
  ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 11. Success message
SELECT 'Performance optimization completed successfully!' as status,
       'All indexes, views, and functions created.' as details,
       'Run SELECT public.refresh_dashboard_stats(); to initialize stats.' as next_step;
