-- FIX ADVOCATE ACCESS - DIAGNOSTIC ET CORRECTION
-- Exécutez ce script dans Supabase SQL Editor pour diagnostiquer et corriger l'accès avocat

-- 1. DIAGNOSTIC - Vérifier l'état actuel des comptes avocats
SELECT 
  'DIAGNOSTIC: État des comptes avocats' as section,
  p.id,
  p.email,
  p.full_name,
  p.role,
  p.is_verified,
  p.created_at,
  CASE 
    WHEN p.role != 'advocate' THEN 'PROBLÈME: Role incorrect'
    WHEN p.is_verified = false THEN 'PROBLÈME: Compte non vérifié'
    ELSE 'OK: Compte valide'
  END as status
FROM public.profiles p
WHERE p.role = 'advocate' OR p.email LIKE '%avocat%' OR p.email LIKE '%advocate%'
ORDER BY p.created_at DESC;

-- 2. DIAGNOSTIC - Vérifier les enregistrements advocates
SELECT 
  'DIAGNOSTIC: Enregistrements advocates' as section,
  a.id as advocate_id,
  a.profile_id,
  p.email,
  p.full_name,
  p.role,
  p.is_verified,
  a.specializations,
  a.bio,
  a.hourly_rate
FROM public.advocates a
JOIN public.profiles p ON a.profile_id = p.id
ORDER BY a.created_at DESC;

-- 3. CORRECTION AUTOMATIQUE - Activer tous les comptes avocats
UPDATE public.profiles 
SET 
  is_verified = true,
  role = 'advocate',
  updated_at = now()
WHERE role = 'advocate' AND is_verified = false;

-- 4. CORRECTION - Créer des enregistrements advocates manquants
INSERT INTO public.advocates (
  profile_id,
  specializations,
  bio,
  hourly_rate,
  rating,
  total_reviews,
  availability,
  created_at,
  updated_at
)
SELECT 
  p.id,
  ARRAY['general'],
  'Avocat professionnel',
  500.00,
  0.0,
  0,
  '{"status": "available"}',
  now(),
  now()
FROM public.profiles p
WHERE p.role = 'advocate' 
  AND NOT EXISTS (
    SELECT 1 FROM public.advocates a WHERE a.profile_id = p.id
  );

-- 5. CORRECTION SPÉCIFIQUE - Si vous connaissez l'email de l'avocat
-- Remplacez '<EMAIL>' par l'email réel de l'avocat
/*
UPDATE public.profiles 
SET 
  role = 'advocate',
  is_verified = true,
  updated_at = now()
WHERE email = '<EMAIL>';
*/

-- 6. VÉRIFICATION FINALE - État après correction
SELECT 
  'VÉRIFICATION FINALE: Comptes avocats après correction' as section,
  p.id,
  p.email,
  p.full_name,
  p.role,
  p.is_verified,
  CASE 
    WHEN p.role = 'advocate' AND p.is_verified = true THEN '✅ ACCÈS AUTORISÉ'
    WHEN p.role = 'advocate' AND p.is_verified = false THEN '❌ NON VÉRIFIÉ'
    WHEN p.role != 'advocate' THEN '❌ ROLE INCORRECT'
    ELSE '❓ STATUT INCONNU'
  END as access_status,
  CASE 
    WHEN EXISTS (SELECT 1 FROM public.advocates a WHERE a.profile_id = p.id) 
    THEN '✅ Enregistrement advocate existe'
    ELSE '❌ Enregistrement advocate manquant'
  END as advocate_record_status
FROM public.profiles p
WHERE p.role = 'advocate' OR p.email LIKE '%avocat%' OR p.email LIKE '%advocate%'
ORDER BY p.created_at DESC;

-- 7. STATISTIQUES FINALES
SELECT 
  'STATISTIQUES FINALES' as section,
  COUNT(*) as total_advocates,
  COUNT(CASE WHEN is_verified = true THEN 1 END) as verified_advocates,
  COUNT(CASE WHEN is_verified = false THEN 1 END) as unverified_advocates
FROM public.profiles 
WHERE role = 'advocate';

-- 8. MESSAGE DE SUCCÈS
SELECT 
  'CORRECTION TERMINÉE' as status,
  'Les comptes avocats ont été vérifiés et corrigés.' as message,
  'Essayez maintenant de vous connecter à l''interface advocate-dashboard.' as next_step;
