# 🌐 Guide des Traductions - المساعدة القانونية المغربية

Ce guide explique comment utiliser le système de traductions bilingue (Arabe/Français) de la plateforme juridique marocaine.

## 📁 Structure des Fichiers

### Fichiers de Traductions
- **`translations.json`** - Traductions principales de l'interface utilisateur
- **`legal-translations.json`** - Traductions spécialisées du domaine juridique marocain
- **`src/lib/translations-config.ts`** - Configuration et utilitaires de traduction
- **`src/hooks/useTranslations.ts`** - Hook personnalisé pour les traductions

### Configuration i18n
- **`src/lib/i18n.ts`** - Configuration principale d'i18next
- **`src/components/LanguageSwitcher.tsx`** - Composant de changement de langue

## 🔧 Utilisation

### 1. Hook useTranslations (Recommandé)

```typescript
import { useTranslations } from '@/hooks/useTranslations';

const MyComponent = () => {
  const { 
    t, 
    currentLanguage, 
    getLegalTerm, 
    formatCurrency, 
    isRTL,
    getPlaceholder 
  } = useTranslations();

  return (
    <div dir={isRTL() ? 'rtl' : 'ltr'}>
      <h1>{t('nav.home')}</h1>
      <p>{getLegalTerm('contract')}</p>
      <span>{formatCurrency(100)}</span>
      <input placeholder={getPlaceholder('search')} />
    </div>
  );
};
```

### 2. Hook useTranslation Standard

```typescript
import { useTranslation } from 'react-i18next';

const MyComponent = () => {
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language as 'ar' | 'fr';

  return (
    <div>
      <h1>{t('nav.home')}</h1>
      <p>{t('pricing.title')}</p>
    </div>
  );
};
```

## 📚 Catégories de Traductions

### 1. Interface Utilisateur (`translations.json`)

#### Navigation
```json
{
  "nav": {
    "home": "الرئيسية / Accueil",
    "pricing": "الأسعار / Tarifs",
    "documents": "الوثائق / Documents"
  }
}
```

#### Authentification
```json
{
  "auth": {
    "signin": {
      "title": "تسجيل الدخول / Connexion",
      "button": "تسجيل الدخول / Se connecter"
    }
  }
}
```

#### Administration
```json
{
  "admin": {
    "title": "إدارة النظام / Administration",
    "users": {
      "total": "إجمالي المستخدمين / Total Utilisateurs"
    }
  }
}
```

### 2. Termes Juridiques (`legal-translations.json`)

#### Termes Généraux
```typescript
getLegalTerm('contract') // عقد / contrat
getLegalTerm('lawsuit') // دعوى قضائية / procès
getLegalTerm('court') // محكمة / tribunal
```

#### Droit Marocain
```typescript
getMoroccanLawTerm('family_code') // مدونة الأسرة
getMoroccanLawTerm('commercial_code') // القانون التجاري
getMoroccanLawTerm('supreme_court') // المجلس الأعلى
```

#### Types de Documents
```typescript
getDocumentType('birth_certificate') // شهادة الميلاد
getDocumentType('marriage_certificate') // شهادة الزواج
getDocumentType('power_of_attorney') // وكالة / procuration
```

## 🛠️ Fonctions Utilitaires

### Formatage
```typescript
const { formatCurrency, formatDate } = useTranslations();

formatCurrency(100) // "100 درهم" ou "100 MAD"
formatDate(new Date()) // Format arabe ou français
```

### Messages d'Interface
```typescript
const { getErrorMessage, getSuccessMessage, getConfirmationMessage } = useTranslations();

getErrorMessage('required') // "هذا الحقل مطلوب" ou "Ce champ est requis"
getSuccessMessage('saved') // "تم الحفظ بنجاح" ou "Sauvegardé avec succès"
getConfirmationMessage('delete') // "هل أنت متأكد من الحذف؟" ou "Êtes-vous sûr ?"
```

### Placeholders
```typescript
const { getPlaceholder } = useTranslations();

getPlaceholder('search') // "ابحث..." ou "Rechercher..."
getPlaceholder('email') // "البريد الإلكتروني" ou "Email"
```

## 🎨 Gestion RTL/LTR

### Direction Automatique
```typescript
const { isRTL, getDirection } = useTranslations();

// Dans le composant
<div dir={getDirection()}>
  {/* Contenu */}
</div>

// CSS conditionnel
className={`text-${isRTL() ? 'right' : 'left'}`}
```

### Classes Tailwind RTL
```typescript
// Espacement conditionnel
className={`${isRTL() ? 'space-x-reverse' : ''} space-x-4`}

// Marges conditionnelles
className={`${isRTL() ? 'mr-4' : 'ml-4'}`}
```

## 📝 Ajout de Nouvelles Traductions

### 1. Interface Utilisateur
Modifiez `translations.json` :

```json
{
  "ar": {
    "translation": {
      "new_section": {
        "title": "عنوان جديد",
        "description": "وصف جديد"
      }
    }
  },
  "fr": {
    "translation": {
      "new_section": {
        "title": "Nouveau titre",
        "description": "Nouvelle description"
      }
    }
  }
}
```

### 2. Termes Juridiques
Modifiez `legal-translations.json` :

```json
{
  "ar": {
    "legal_terms": {
      "new_term": "مصطلح جديد"
    },
    "moroccan_law": {
      "new_law": "قانون جديد"
    }
  },
  "fr": {
    "legal_terms": {
      "new_term": "nouveau terme"
    }
  }
}
```

## 🔍 Exemples d'Utilisation Pratiques

### Composant de Navigation
```typescript
const Navigation = () => {
  const { t, isRTL } = useTranslations();
  
  return (
    <nav className={`flex ${isRTL() ? 'space-x-reverse' : ''} space-x-4`}>
      <Link to="/">{t('nav.home')}</Link>
      <Link to="/documents">{t('nav.documents')}</Link>
      <Link to="/questions">{t('nav.questions')}</Link>
    </nav>
  );
};
```

### Formulaire avec Validation
```typescript
const ContactForm = () => {
  const { getPlaceholder, getErrorMessage, getSuccessMessage } = useTranslations();
  
  return (
    <form>
      <input 
        placeholder={getPlaceholder('email')}
        required
      />
      {error && <span>{getErrorMessage('required')}</span>}
      {success && <span>{getSuccessMessage('sent')}</span>}
    </form>
  );
};
```

### Page d'Administration
```typescript
const AdminDashboard = () => {
  const { t, formatCurrency, getLegalTerm } = useTranslations();
  
  return (
    <div>
      <h1>{t('admin.title')}</h1>
      <div>
        <span>{t('admin.stats.revenue')}: {formatCurrency(monthlyRevenue)}</span>
        <span>{t('admin.users.total')}: {totalUsers}</span>
      </div>
      <p>{getLegalTerm('jurisdiction')}</p>
    </div>
  );
};
```

## 🌍 Langues Supportées

- **العربية (ar)** - Arabe (RTL)
- **Français (fr)** - Français (LTR)

## 📋 Checklist pour Nouveaux Composants

- [ ] Utiliser `useTranslations()` ou `useTranslation()`
- [ ] Gérer la direction RTL/LTR
- [ ] Utiliser les placeholders appropriés
- [ ] Implémenter les messages d'erreur/succès
- [ ] Tester dans les deux langues
- [ ] Vérifier l'alignement du texte
- [ ] Valider les espacements conditionnels

## 🚀 Bonnes Pratiques

1. **Toujours utiliser les clés de traduction** au lieu du texte en dur
2. **Tester dans les deux langues** avant de déployer
3. **Utiliser les utilitaires de formatage** pour les devises et dates
4. **Gérer la direction RTL** pour tous les composants
5. **Maintenir la cohérence** dans les termes juridiques
6. **Documenter les nouvelles clés** de traduction

Ce système de traductions garantit une expérience utilisateur cohérente et professionnelle dans les deux langues officielles du Maroc.
