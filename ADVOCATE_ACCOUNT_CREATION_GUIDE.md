# 🏛️ **Complete Guide: Creating Advocate Accounts in Law App Morocco**

## 📋 **Overview**

This guide provides step-by-step instructions for creating and managing advocate accounts in the Law App Morocco application.

## 🔧 **Prerequisites**

1. ✅ **Database setup completed** (run `COMPREHENSIVE_DATABASE_FIX.sql`)
2. ✅ **Admin account created** and verified
3. ✅ **Application running** and accessible

## 🎯 **Method 1: Create Advocate via Application Interface**

### **Step 1: User Registration**
1. **Go to the application** homepage
2. **Click "Sign Up"** or registration button
3. **Fill out the form:**
   - **Email**: <EMAIL>
   - **Password**: Strong password
   - **Full Name**: Advocate's full name
   - **Role**: Select "Advocate" (محامي)
4. **Submit the form**

### **Step 2: Admin Verification**
1. **Log in as admin** to the application
2. **Go to Admin Dashboard** (`/admin`)
3. **Click on "Users" tab**
4. **Find the new advocate** in the user list
5. **Click "Verify"** button next to their name
6. **Advocate can now access** `/advocate-dashboard`

## 🗄️ **Method 2: Create Advocate via Database (Direct)**

### **Step 1: Execute SQL Commands**

```sql
-- 1. Create advocate account (replace with real email)
SELECT public.create_advocate_account(
  '<EMAIL>',           -- Email
  'Ahmed El Mansouri',              -- Full name
  ARRAY['family', 'civil'],        -- Specializations
  'Experienced family law attorney with 10+ years', -- Bio
  600.00,                          -- Hourly rate
  true                             -- Auto-verify (true/false)
);

-- 2. Verify the account was created
SELECT 
  p.email,
  p.full_name,
  p.role,
  p.is_verified,
  a.specializations,
  a.hourly_rate
FROM public.profiles p
JOIN public.advocates a ON p.id = a.profile_id
WHERE p.email = '<EMAIL>';
```

### **Step 2: User Must Set Password**
The advocate must:
1. **Go to the application**
2. **Click "Sign Up"** with the same email
3. **Set their password**
4. **They will automatically have advocate role**

## 🚀 **Method 3: Bulk Advocate Creation**

### **Create Multiple Advocates at Once**

```sql
-- Create multiple advocate accounts
SELECT public.create_advocate_account('<EMAIL>', 'Ahmed El Mansouri', ARRAY['family', 'civil'], 'Family law specialist', 600.00, true);
SELECT public.create_advocate_account('<EMAIL>', 'Fatima Benali', ARRAY['commercial', 'labor'], 'Commercial law expert', 700.00, true);
SELECT public.create_advocate_account('<EMAIL>', 'Omar Tazi', ARRAY['criminal', 'civil'], 'Criminal defense attorney', 800.00, true);
SELECT public.create_advocate_account('<EMAIL>', 'Aicha Alami', ARRAY['real_estate', 'family'], 'Real estate and family law', 650.00, false);

-- Verify all advocates were created
SELECT 
  p.email,
  p.full_name,
  p.is_verified,
  a.specializations,
  a.hourly_rate
FROM public.profiles p
JOIN public.advocates a ON p.id = a.profile_id
WHERE p.role = 'advocate'
ORDER BY p.created_at DESC;
```

## 🔐 **Method 4: Create Admin Account**

### **Create Admin Account via SQL**

```sql
-- Create admin account (replace with your email)
SELECT public.create_admin_account(
  '<EMAIL>',              -- Your email
  'System Administrator'          -- Your name
);

-- Verify admin was created
SELECT email, full_name, role, is_verified 
FROM public.profiles 
WHERE role = 'admin';
```

## 📊 **Verification and Testing**

### **Step 1: Check Database Statistics**
```sql
-- Get comprehensive statistics
SELECT * FROM public.get_user_statistics();

-- View all users by role
SELECT 
  role,
  COUNT(*) as total,
  COUNT(*) FILTER (WHERE is_verified = true) as verified,
  COUNT(*) FILTER (WHERE is_verified = false) as unverified
FROM public.profiles 
GROUP BY role;
```

### **Step 2: Test Advocate Login**
1. **Go to application** homepage
2. **Click "Sign In"**
3. **Use advocate credentials**
4. **Should redirect to** `/advocate-dashboard`
5. **Verify advocate can see:**
   - ✅ Dashboard statistics
   - ✅ Assigned questions
   - ✅ Available questions to take
   - ✅ Response interface

### **Step 3: Test Admin Functions**
1. **Log in as admin**
2. **Go to** `/admin`
3. **Verify admin can:**
   - ✅ View all users
   - ✅ Change user roles
   - ✅ Verify/unverify advocates
   - ✅ View system statistics

## 🛠️ **Troubleshooting Common Issues**

### **Issue 1: Advocate Can't Access Dashboard**
```sql
-- Check advocate verification status
SELECT email, role, is_verified 
FROM public.profiles 
WHERE email = '<EMAIL>';

-- Manually verify advocate
UPDATE public.profiles 
SET is_verified = true 
WHERE email = '<EMAIL>' AND role = 'advocate';
```

### **Issue 2: Advocate Profile Missing**
```sql
-- Check if advocate profile exists
SELECT p.email, a.id as advocate_id
FROM public.profiles p
LEFT JOIN public.advocates a ON p.id = a.profile_id
WHERE p.email = '<EMAIL>';

-- Create missing advocate profile
INSERT INTO public.advocates (profile_id, specializations, bio, hourly_rate)
SELECT id, ARRAY['general'], 'Legal professional', 500.00
FROM public.profiles 
WHERE email = '<EMAIL>' AND role = 'advocate';
```

### **Issue 3: Admin Can't Access Admin Panel**
```sql
-- Check admin status
SELECT email, role, is_verified 
FROM public.profiles 
WHERE email = '<EMAIL>';

-- Fix admin role
UPDATE public.profiles 
SET role = 'admin', is_verified = true 
WHERE email = '<EMAIL>';
```

## 📋 **Account Management Checklist**

### **For Each New Advocate:**
- [ ] ✅ **Email verified** in auth.users
- [ ] ✅ **Profile created** with role 'advocate'
- [ ] ✅ **Advocate profile** created in advocates table
- [ ] ✅ **Verification status** set appropriately
- [ ] ✅ **Specializations** defined
- [ ] ✅ **Bio and hourly rate** set
- [ ] ✅ **Can log in** to application
- [ ] ✅ **Can access** advocate dashboard
- [ ] ✅ **Can view and take** questions
- [ ] ✅ **Can create** responses

### **For Admin Accounts:**
- [ ] ✅ **Email verified** in auth.users
- [ ] ✅ **Profile created** with role 'admin'
- [ ] ✅ **Verification status** set to true
- [ ] ✅ **Can log in** to application
- [ ] ✅ **Can access** admin dashboard
- [ ] ✅ **Can manage** users and advocates
- [ ] ✅ **Can view** system statistics

## 🎉 **Success Indicators**

### **Application Working Correctly When:**
1. ✅ **Users can register** and log in
2. ✅ **Advocates can access** their dashboard after verification
3. ✅ **Admins can manage** all users and system
4. ✅ **Questions and responses** flow works properly
5. ✅ **Role-based access control** functions correctly
6. ✅ **No infinite loading** or authentication errors
7. ✅ **Page refreshes** maintain authentication state

## 📞 **Support Commands**

### **Quick Status Check:**
```sql
-- One command to check everything
SELECT 
  'Total Users' as metric, COUNT(*)::text as value FROM public.profiles
UNION ALL
SELECT 'Advocates', COUNT(*)::text FROM public.profiles WHERE role = 'advocate'
UNION ALL
SELECT 'Verified Advocates', COUNT(*)::text FROM public.profiles WHERE role = 'advocate' AND is_verified = true
UNION ALL
SELECT 'Admins', COUNT(*)::text FROM public.profiles WHERE role = 'admin'
UNION ALL
SELECT 'Total Questions', COUNT(*)::text FROM public.legal_questions
UNION ALL
SELECT 'Total Responses', COUNT(*)::text FROM public.responses;
```

Your Law App Morocco is now ready for production use! 🚀
