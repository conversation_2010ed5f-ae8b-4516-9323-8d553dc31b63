-- SUBSCRIPTION SYSTEM DEBUG AND FIX
-- Execute this script to debug and fix subscription issues

-- 1. Check if subscription tables exist and their structure
DO $$
BEGIN
  RAISE NOTICE 'Checking subscription system tables...';
  
  -- Check subscription_plans table
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subscription_plans' AND table_schema = 'public') THEN
    RAISE NOTICE '✅ subscription_plans table exists';
  ELSE
    RAISE NOTICE '❌ subscription_plans table missing';
  END IF;
    user_role TEXT DEFAULT NULL,

  -- Check user_subscriptions table
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_subscriptions' AND table_schema = 'public') THEN
    RAISE NOTICE '✅ user_subscriptions table exists';
  ELSE
    RAISE NOTICE '❌ user_subscriptions table missing';
  END IF;
  
  -- Check subscription_preferences table
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subscription_preferences' AND table_schema = 'public') THEN
    RAISE NOTICE '✅ subscription_preferences table exists';
  ELSE
    RAISE NOTICE '❌ subscription_preferences table missing';
  END IF;
END $$;

-- 2. Check if profiles table exists (required for foreign keys)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles' AND table_schema = 'public') THEN
    RAISE NOTICE '✅ profiles table exists';
  ELSE
    RAISE NOTICE '❌ profiles table missing - this will cause foreign key errors';
  END IF;
END $$;

-- 3. Check current subscription plans
DO $$
DECLARE
  plan_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO plan_count FROM public.subscription_plans WHERE is_active = true;
  RAISE NOTICE 'Active subscription plans: %', plan_count;
  
  IF plan_count = 0 THEN
    RAISE NOTICE '❌ No active subscription plans found';
  END IF;
END $$;

-- 4. Fix foreign key constraints if they don't exist
DO $$
BEGIN
  -- Add foreign key for user_subscriptions.user_id if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'fk_user_subscriptions_user_id' 
    AND table_name = 'user_subscriptions'
  ) THEN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles' AND table_schema = 'public') THEN
      ALTER TABLE public.user_subscriptions 
      ADD CONSTRAINT fk_user_subscriptions_user_id 
      FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;
      RAISE NOTICE '✅ Added foreign key constraint for user_subscriptions.user_id';
    ELSE
      RAISE NOTICE '❌ Cannot add foreign key - profiles table missing';
    END IF;
  END IF;
  
  -- Add foreign key for user_subscriptions.plan_id if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'fk_user_subscriptions_plan_id' 
    AND table_name = 'user_subscriptions'
  ) THEN
    ALTER TABLE public.user_subscriptions 
    ADD CONSTRAINT fk_user_subscriptions_plan_id 
    FOREIGN KEY (plan_id) REFERENCES public.subscription_plans(id) ON DELETE RESTRICT;
    RAISE NOTICE '✅ Added foreign key constraint for user_subscriptions.plan_id';
  END IF;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE '❌ Error adding foreign key constraints: %', SQLERRM;
END $$;

-- 5. Ensure unique index for active subscriptions exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE indexname = 'idx_user_subscriptions_active_unique'
  ) THEN
    CREATE UNIQUE INDEX idx_user_subscriptions_active_unique 
    ON public.user_subscriptions(user_id) 
    WHERE status = 'active';
    RAISE NOTICE '✅ Created unique index for active subscriptions';
  ELSE
    RAISE NOTICE '✅ Unique index for active subscriptions already exists';
  END IF;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE '❌ Error creating unique index: %', SQLERRM;
END $$;

-- 6. Insert default subscription plans if they don't exist
DO $$
DECLARE
  free_plan_id UUID;
  premium_plan_id UUID;
  professional_plan_id UUID;
BEGIN
  -- Check if plans already exist
  SELECT id INTO free_plan_id FROM public.subscription_plans WHERE name = 'Free' LIMIT 1;
  
  IF free_plan_id IS NULL THEN
    -- Insert Free plan
    INSERT INTO public.subscription_plans (name, name_ar, description, description_ar, price, features, features_ar, sort_order) 
    VALUES (
      'Free', 
      'مجاني', 
      'Basic legal assistance', 
      'مساعدة قانونية أساسية', 
      0.00, 
      '["1 legal question per month", "Basic document creation", "7-day premium trial"]',
      '["سؤال قانوني واحد شهرياً", "إنشاء وثائق محدود", "تجربة مجانية 7 أيام للباقة المتميزة"]', 
      1
    ) RETURNING id INTO free_plan_id;
    RAISE NOTICE '✅ Created Free plan with ID: %', free_plan_id;
  END IF;
  
  SELECT id INTO premium_plan_id FROM public.subscription_plans WHERE name = 'Premium' LIMIT 1;
  
  IF premium_plan_id IS NULL THEN
    -- Insert Premium plan
    INSERT INTO public.subscription_plans (name, name_ar, description, description_ar, price, features, features_ar, sort_order) 
    VALUES (
      'Premium', 
      'المستخدم المتميز', 
      'Enhanced legal services', 
      'خدمات قانونية محسنة', 
      100.00,
      '["Unlimited legal questions", "Unlimited document creation", "Priority support", "Full access to lawyers"]',
      '["أسئلة قانونية غير محدودة", "إنشاء وثائق غير محدود", "دعم ذو أولوية", "وصول كامل للمحامين"]', 
      2
    ) RETURNING id INTO premium_plan_id;
    RAISE NOTICE '✅ Created Premium plan with ID: %', premium_plan_id;
  END IF;
  
  SELECT id INTO professional_plan_id FROM public.subscription_plans WHERE name = 'Professional' LIMIT 1;
  
  IF professional_plan_id IS NULL THEN
    -- Insert Professional plan
    INSERT INTO public.subscription_plans (name, name_ar, description, description_ar, price, features, features_ar, sort_order) 
    VALUES (
      'Professional', 
      'المحامي المتميز', 
      'Complete legal solution', 
      'حل قانوني متكامل', 
      500.00,
      '["Personal legal profile", "Advanced tools and analytics", "Real-time notifications", "Priority in specialized profile display"]',
      '["ملف شخصي مميز في البحث", "أدوات الحجز والتحليلات", "تحديد الأوقات والأسعار", "أولوية في عرض الملف الشخصي"]', 
      3
    ) RETURNING id INTO professional_plan_id;
    RAISE NOTICE '✅ Created Professional plan with ID: %', professional_plan_id;
  END IF;
  
  RAISE NOTICE 'Plan IDs - Free: %, Premium: %, Professional: %', free_plan_id, premium_plan_id, professional_plan_id;
END $$;

-- 7. Check RLS policies
DO $$
BEGIN
  -- Check if RLS is enabled
  IF EXISTS (
    SELECT 1 FROM pg_tables 
    WHERE tablename = 'subscription_plans' 
    AND rowsecurity = true
  ) THEN
    RAISE NOTICE '✅ RLS enabled on subscription_plans';
  ELSE
    RAISE NOTICE '❌ RLS not enabled on subscription_plans';
  END IF;
  
  IF EXISTS (
    SELECT 1 FROM pg_tables 
    WHERE tablename = 'user_subscriptions' 
    AND rowsecurity = true
  ) THEN
    RAISE NOTICE '✅ RLS enabled on user_subscriptions';
  ELSE
    RAISE NOTICE '❌ RLS not enabled on user_subscriptions';
  END IF;
END $$;

-- 8. Test subscription creation function
DO $$
DECLARE
  test_result JSON;
  test_user_id UUID := '00000000-0000-0000-0000-000000000000'; -- Dummy UUID for testing
BEGIN
  -- Test the get_user_subscription_details function
  SELECT public.get_user_subscription_details(test_user_id) INTO test_result;
  
  IF test_result IS NOT NULL THEN
    RAISE NOTICE '✅ get_user_subscription_details function works';
  ELSE
    RAISE NOTICE '❌ get_user_subscription_details function failed';
  END IF;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE '❌ Error testing get_user_subscription_details: %', SQLERRM;
END $$;

-- 9. Clean up any orphaned subscriptions (subscriptions without valid user_id or plan_id)
DO $$
DECLARE
  orphaned_count INTEGER;
BEGIN
  -- Count orphaned subscriptions
  SELECT COUNT(*) INTO orphaned_count
  FROM public.user_subscriptions us
  LEFT JOIN public.profiles p ON us.user_id = p.id
  LEFT JOIN public.subscription_plans sp ON us.plan_id = sp.id
  WHERE p.id IS NULL OR sp.id IS NULL;
  
  IF orphaned_count > 0 THEN
    RAISE NOTICE '❌ Found % orphaned subscriptions', orphaned_count;
    
    -- Optionally delete orphaned subscriptions (uncomment if needed)
    -- DELETE FROM public.user_subscriptions us
    -- WHERE NOT EXISTS (SELECT 1 FROM public.profiles p WHERE p.id = us.user_id)
    --    OR NOT EXISTS (SELECT 1 FROM public.subscription_plans sp WHERE sp.id = us.plan_id);
    
    RAISE NOTICE 'To clean up orphaned subscriptions, uncomment the DELETE statement in this script';
  ELSE
    RAISE NOTICE '✅ No orphaned subscriptions found';
  END IF;
END $$;

-- 10. Final status check
DO $$
DECLARE
  plans_count INTEGER;
  subscriptions_count INTEGER;
  preferences_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO plans_count FROM public.subscription_plans WHERE is_active = true;
  SELECT COUNT(*) INTO subscriptions_count FROM public.user_subscriptions;
  SELECT COUNT(*) INTO preferences_count FROM public.subscription_preferences;
  
  RAISE NOTICE '=== SUBSCRIPTION SYSTEM STATUS ===';
  RAISE NOTICE 'Active plans: %', plans_count;
  RAISE NOTICE 'Total subscriptions: %', subscriptions_count;
  RAISE NOTICE 'User preferences: %', preferences_count;
  
  IF plans_count >= 3 THEN
    RAISE NOTICE '✅ Subscription system appears to be working correctly';
  ELSE
    RAISE NOTICE '❌ Subscription system may have issues - check the logs above';
  END IF;
END $$;
