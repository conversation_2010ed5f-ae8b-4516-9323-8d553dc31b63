-- =====================================================
-- SCRIPT SQL COMPLET - LAW APP MOROCCO
-- Nouveau Projet Supabase avec Données de Test
-- =====================================================

-- 1. CRÉATION DES ENUMS
CREATE TYPE user_role AS ENUM ('admin', 'user', 'advocate');
CREATE TYPE subscription_tier AS ENUM ('free', 'pro_user', 'pro_advocate');
CREATE TYPE document_status AS ENUM ('draft', 'completed', 'archived');

-- 2. CRÉATION DE LA TABLE PROFILES
CREATE TABLE public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  full_name TEXT,
  phone TEXT,
  role user_role NOT NULL DEFAULT 'user',
  subscription_tier subscription_tier NOT NULL DEFAULT 'free',
  subscription_end TIMESTAMPTZ,
  trial_end TIMESTAMPTZ,
  stripe_customer_id TEXT,
  is_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 3. CRÉATION DE LA TABLE ADVOCATES
CREATE TABLE public.advocates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  specializations TEXT[],
  bio TEXT,
  hourly_rate DECIMAL(10,2),
  is_featured BOOLEAN DEFAULT FALSE,
  rating DECIMAL(3,2) DEFAULT 0.0,
  total_reviews INTEGER DEFAULT 0,
  availability JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 4. CRÉATION DE LA TABLE LEGAL_QUESTIONS
CREATE TABLE public.legal_questions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  advocate_id UUID REFERENCES public.advocates(id) ON DELETE SET NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  category TEXT DEFAULT 'general',
  status TEXT DEFAULT 'pending',
  is_answered BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 5. CRÉATION DE LA TABLE RESPONSES
CREATE TABLE public.responses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  question_id UUID NOT NULL REFERENCES public.legal_questions(id) ON DELETE CASCADE,
  advocate_id UUID NOT NULL REFERENCES public.advocates(id) ON DELETE CASCADE,
  response_text TEXT NOT NULL,
  is_approved BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 6. CRÉATION DE LA TABLE LEGAL_DOCUMENTS
CREATE TABLE public.legal_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  content JSONB,
  template_id TEXT,
  language TEXT DEFAULT 'ar',
  status document_status DEFAULT 'draft',
  file_url TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 7. CRÉATION DE LA TABLE SUBSCRIPTIONS
CREATE TABLE public.subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  stripe_subscription_id TEXT,
  tier subscription_tier NOT NULL,
  status TEXT NOT NULL,
  current_period_start TIMESTAMPTZ,
  current_period_end TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 8. CRÉATION DE LA TABLE USAGE_TRACKING
CREATE TABLE public.usage_tracking (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  questions_this_month INTEGER DEFAULT 0,
  documents_this_month INTEGER DEFAULT 0,
  last_reset_date TIMESTAMPTZ DEFAULT now(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 9. CRÉATION DE LA TABLE SUBSCRIBERS
CREATE TABLE public.subscribers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  stripe_customer_id TEXT,
  subscribed BOOLEAN DEFAULT FALSE,
  subscription_tier subscription_tier DEFAULT 'free',
  subscription_end TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 10. ACTIVATION DE ROW LEVEL SECURITY
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.advocates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.legal_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.legal_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.usage_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscribers ENABLE ROW LEVEL SECURITY;

-- 11. CRÉATION DES POLITIQUES RLS POUR PROFILES
CREATE POLICY "Users can view their own profile" ON public.profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles" ON public.profiles
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 12. CRÉATION DES POLITIQUES RLS POUR ADVOCATES
CREATE POLICY "Anyone can view verified advocates" ON public.advocates
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = profile_id AND is_verified = true
    )
  );

CREATE POLICY "Advocates can manage their own profile" ON public.advocates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND id = profile_id
    )
  );

CREATE POLICY "Admins can manage all advocates" ON public.advocates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 13. CRÉATION DES POLITIQUES RLS POUR LEGAL_QUESTIONS
CREATE POLICY "Users can view their own questions" ON public.legal_questions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create questions" ON public.legal_questions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own questions" ON public.legal_questions
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Advocates can view all questions" ON public.legal_questions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'advocate' AND is_verified = true
    )
  );

CREATE POLICY "Advocates can update questions assigned to them" ON public.legal_questions
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id
    )
  );

CREATE POLICY "Admins can manage all questions" ON public.legal_questions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 14. CRÉATION DES POLITIQUES RLS POUR RESPONSES
CREATE POLICY "Users can view responses to their questions" ON public.responses
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.legal_questions lq
      WHERE lq.id = question_id AND lq.user_id = auth.uid()
    )
  );

CREATE POLICY "Advocates can view their own responses" ON public.responses
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id
    )
  );

CREATE POLICY "Advocates can create responses" ON public.responses
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id
    )
  );

CREATE POLICY "Advocates can update their own responses" ON public.responses
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id
    )
  );

CREATE POLICY "Admins can manage all responses" ON public.responses
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 15. CRÉATION DES POLITIQUES RLS POUR LEGAL_DOCUMENTS
CREATE POLICY "Users can manage their own documents" ON public.legal_documents
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all documents" ON public.legal_documents
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 16. CRÉATION DES POLITIQUES RLS POUR SUBSCRIPTIONS
CREATE POLICY "Users can view their own subscriptions" ON public.subscriptions
  FOR ALL USING (auth.uid() = user_id);

-- 17. CRÉATION DES POLITIQUES RLS POUR USAGE_TRACKING
CREATE POLICY "Users can view their own usage" ON public.usage_tracking
  FOR ALL USING (auth.uid() = user_id);

-- 18. CRÉATION DES POLITIQUES RLS POUR SUBSCRIBERS
CREATE POLICY "select_own_subscription" ON public.subscribers
FOR SELECT
USING (user_id = auth.uid() OR email = auth.email());

CREATE POLICY "update_own_subscription" ON public.subscribers
FOR UPDATE
USING (true);

CREATE POLICY "insert_subscription" ON public.subscribers
FOR INSERT
WITH CHECK (true);

-- 19. CRÉATION DES INDEX POUR PERFORMANCE
CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
CREATE INDEX IF NOT EXISTS idx_advocates_profile_id ON public.advocates(profile_id);
CREATE INDEX IF NOT EXISTS idx_legal_questions_user_id ON public.legal_questions(user_id);
CREATE INDEX IF NOT EXISTS idx_legal_questions_advocate_id ON public.legal_questions(advocate_id);
CREATE INDEX IF NOT EXISTS idx_responses_question_id ON public.responses(question_id);
CREATE INDEX IF NOT EXISTS idx_responses_advocate_id ON public.responses(advocate_id);
CREATE INDEX IF NOT EXISTS idx_legal_documents_user_id ON public.legal_documents(user_id);

-- 20. CRÉATION DES FONCTIONS
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email)
  );

  INSERT INTO public.usage_tracking (user_id)
  VALUES (NEW.id);

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 21. CRÉATION DU TRIGGER POUR NOUVEAUX UTILISATEURS
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- 22. FONCTION POUR METTRE À JOUR LE STATUT DES QUESTIONS
CREATE OR REPLACE FUNCTION public.update_question_status_on_response()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.legal_questions
  SET
    is_answered = true,
    status = 'answered',
    updated_at = now()
  WHERE id = NEW.question_id;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 23. TRIGGER POUR MISE À JOUR AUTOMATIQUE DU STATUT
CREATE TRIGGER on_response_created
  AFTER INSERT ON public.responses
  FOR EACH ROW EXECUTE PROCEDURE public.update_question_status_on_response();

-- 24. FONCTION POUR ASSIGNER UNE QUESTION À UN AVOCAT
CREATE OR REPLACE FUNCTION public.assign_question_to_advocate(
  question_uuid UUID,
  advocate_profile_uuid UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  advocate_uuid UUID;
BEGIN
  SELECT id INTO advocate_uuid
  FROM public.advocates
  WHERE profile_id = advocate_profile_uuid;

  IF advocate_uuid IS NULL THEN
    RETURN FALSE;
  END IF;

  UPDATE public.legal_questions
  SET
    advocate_id = advocate_uuid,
    status = 'assigned',
    updated_at = now()
  WHERE id = question_uuid;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 25. FONCTION POUR VÉRIFIER LES LIMITES D'USAGE
CREATE OR REPLACE FUNCTION public.check_usage_limits(user_uuid UUID, action_type TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  user_tier subscription_tier;
  current_usage INTEGER;
BEGIN
  SELECT subscription_tier INTO user_tier
  FROM public.profiles
  WHERE id = user_uuid;

  IF user_tier IN ('pro_user', 'pro_advocate') THEN
    RETURN TRUE;
  END IF;

  IF action_type = 'question' THEN
    SELECT questions_this_month INTO current_usage
    FROM public.usage_tracking
    WHERE user_id = user_uuid;

    RETURN current_usage < 1;
  END IF;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- INSERTION DES DONNÉES DE TEST
-- =====================================================

-- 26. INSERTION DES COMPTES UTILISATEURS DE TEST
-- Note: Ces UUIDs sont des exemples, remplacez-les par de vrais UUIDs après création des comptes

-- Compte Admin Principal
INSERT INTO public.profiles (id, email, full_name, role, subscription_tier, is_verified, created_at, updated_at)
VALUES
  ('11111111-1111-1111-1111-111111111111', '<EMAIL>', 'Administrateur Principal', 'admin', 'pro_advocate', true, now(), now())
ON CONFLICT (id) DO UPDATE SET
  role = 'admin',
  is_verified = true,
  full_name = 'Administrateur Principal';

-- Compte Admin Modérateur
INSERT INTO public.profiles (id, email, full_name, role, subscription_tier, is_verified, created_at, updated_at)
VALUES
  ('*************-2222-2222-************', '<EMAIL>', 'Modérateur Système', 'admin', 'pro_advocate', true, now(), now())
ON CONFLICT (id) DO UPDATE SET
  role = 'admin',
  is_verified = true,
  full_name = 'Modérateur Système';

-- Avocat 1 - Spécialiste Droit de la Famille
INSERT INTO public.profiles (id, email, full_name, phone, role, subscription_tier, is_verified, created_at, updated_at)
VALUES
  ('*************-3333-3333-333333333333', '<EMAIL>', 'Me. Ahmed Benali', '+212661234567', 'advocate', 'pro_advocate', true, now(), now())
ON CONFLICT (id) DO UPDATE SET
  role = 'advocate',
  is_verified = true,
  full_name = 'Me. Ahmed Benali';

-- Avocat 2 - Spécialiste Droit Commercial
INSERT INTO public.profiles (id, email, full_name, phone, role, subscription_tier, is_verified, created_at, updated_at)
VALUES
  ('44444444-4444-4444-4444-444444444444', '<EMAIL>', 'Me. Fatima Alaoui', '+212662345678', 'advocate', 'pro_advocate', true, now(), now())
ON CONFLICT (id) DO UPDATE SET
  role = 'advocate',
  is_verified = true,
  full_name = 'Me. Fatima Alaoui';

-- Avocat 3 - Spécialiste Droit Pénal
INSERT INTO public.profiles (id, email, full_name, phone, role, subscription_tier, is_verified, created_at, updated_at)
VALUES
  ('55555555-5555-5555-5555-555555555555', '<EMAIL>', 'Me. Omar Tazi', '+212663456789', 'advocate', 'pro_advocate', true, now(), now())
ON CONFLICT (id) DO UPDATE SET
  role = 'advocate',
  is_verified = true,
  full_name = 'Me. Omar Tazi';

-- Utilisateur Test 1
INSERT INTO public.profiles (id, email, full_name, phone, role, subscription_tier, is_verified, created_at, updated_at)
VALUES
  ('66666666-6666-6666-6666-666666666666', '<EMAIL>', 'Youssef Mansouri', '+212664567890', 'user', 'free', true, now(), now())
ON CONFLICT (id) DO UPDATE SET
  role = 'user',
  is_verified = true,
  full_name = 'Youssef Mansouri';

-- Utilisateur Test 2
INSERT INTO public.profiles (id, email, full_name, phone, role, subscription_tier, is_verified, created_at, updated_at)
VALUES
  ('********-7777-7777-7777-********7777', '<EMAIL>', 'Aicha Benjelloun', '+212665678901', 'user', 'pro_user', true, now(), now())
ON CONFLICT (id) DO UPDATE SET
  role = 'user',
  is_verified = true,
  full_name = 'Aicha Benjelloun';

-- 27. INSERTION DES PROFILS AVOCATS
INSERT INTO public.advocates (id, profile_id, specializations, bio, hourly_rate, is_featured, rating, total_reviews, availability, created_at, updated_at)
VALUES
  ('aaaa1111-1111-1111-1111-111111111111', '*************-3333-3333-333333333333',
   ARRAY['family', 'civil', 'inheritance'],
   'Avocat spécialisé en droit de la famille avec plus de 15 ans d''expérience. Expert en divorce, garde d''enfants et succession.',
   800.00, true, 4.8, 45,
   '{"monday": {"start": "09:00", "end": "17:00"}, "tuesday": {"start": "09:00", "end": "17:00"}, "wednesday": {"start": "09:00", "end": "17:00"}, "thursday": {"start": "09:00", "end": "17:00"}, "friday": {"start": "09:00", "end": "17:00"}}',
   now(), now()),

  ('bbbb2222-2222-2222-2222-************', '44444444-4444-4444-4444-444444444444',
   ARRAY['commercial', 'business', 'contracts'],
   'Avocate d''affaires spécialisée en droit commercial et des sociétés. Accompagne les entreprises dans leurs projets juridiques.',
   1200.00, true, 4.9, 38,
   '{"monday": {"start": "08:30", "end": "18:00"}, "tuesday": {"start": "08:30", "end": "18:00"}, "wednesday": {"start": "08:30", "end": "18:00"}, "thursday": {"start": "08:30", "end": "18:00"}, "friday": {"start": "08:30", "end": "16:00"}}',
   now(), now()),

  ('cccc3333-3333-3333-3333-333333333333', '55555555-5555-5555-5555-555555555555',
   ARRAY['criminal', 'penal', 'defense'],
   'Avocat pénaliste expérimenté, spécialisé dans la défense pénale et les affaires criminelles complexes.',
   1000.00, false, 4.6, 28,
   '{"monday": {"start": "10:00", "end": "18:00"}, "tuesday": {"start": "10:00", "end": "18:00"}, "wednesday": {"start": "10:00", "end": "18:00"}, "thursday": {"start": "10:00", "end": "18:00"}, "friday": {"start": "10:00", "end": "16:00"}}',
   now(), now())
ON CONFLICT (id) DO NOTHING;

-- 28. INSERTION DES QUESTIONS LÉGALES DE TEST
INSERT INTO public.legal_questions (id, user_id, advocate_id, title, description, category, status, is_answered, created_at, updated_at)
VALUES
  ('q1111111-1111-1111-1111-111111111111', '66666666-6666-6666-6666-666666666666', 'aaaa1111-1111-1111-1111-111111111111',
   'Procédure de divorce au Maroc',
   'Je souhaite entamer une procédure de divorce. Quelles sont les étapes à suivre et les documents nécessaires ?',
   'family', 'answered', true, now() - interval '2 days', now()),

  ('q2222222-2222-2222-2222-************', '********-7777-7777-7777-********7777', 'bbbb2222-2222-2222-2222-************',
   'Création d''une SARL au Maroc',
   'Je veux créer une SARL avec des associés. Quelles sont les formalités juridiques et le capital minimum requis ?',
   'commercial', 'answered', true, now() - interval '1 day', now()),

  ('q3333333-3333-3333-3333-333333333333', '66666666-6666-6666-6666-666666666666', NULL,
   'Héritage et succession',
   'Mon père est décédé sans testament. Comment se répartit l''héritage selon la loi marocaine ?',
   'family', 'pending', false, now() - interval '3 hours', now()),

  ('q4444444-4444-4444-4444-444444444444', '********-7777-7777-7777-********7777', NULL,
   'Contrat de travail et licenciement',
   'Mon employeur veut me licencier sans préavis. Quels sont mes droits et recours possibles ?',
   'labor', 'pending', false, now() - interval '1 hour', now())
ON CONFLICT (id) DO NOTHING;

-- 29. INSERTION DES RÉPONSES D'AVOCATS
INSERT INTO public.responses (id, question_id, advocate_id, response_text, is_approved, created_at, updated_at)
VALUES
  ('r1111111-1111-1111-1111-111111111111', 'q1111111-1111-1111-1111-111111111111', 'aaaa1111-1111-1111-1111-111111111111',
   'Pour entamer une procédure de divorce au Maroc, vous devez d''abord déterminer le type de divorce souhaité (divorce par consentement mutuel, khôl, ou divorce pour discorde).

Les documents nécessaires sont :
- Acte de mariage
- Copies des cartes d''identité nationales
- Certificat de résidence
- Acte de naissance des enfants (si applicable)

La procédure se déroule devant le tribunal de la famille. Je recommande de consulter un avocat pour vous accompagner dans cette démarche importante.',
   true, now() - interval '1 day', now()),

  ('r2222222-2222-2222-2222-************', 'q2222222-2222-2222-2222-************', 'bbbb2222-2222-2222-2222-************',
   'Pour créer une SARL au Maroc, voici les étapes principales :

1. **Capital minimum** : 10 000 DH minimum
2. **Nombre d''associés** : 2 à 50 associés maximum
3. **Formalités** :
   - Établissement des statuts
   - Dépôt du capital en banque
   - Enregistrement au registre de commerce
   - Publication dans un journal d''annonces légales
   - Inscription à la patente

**Documents requis** :
- Statuts de la société
- Attestation de blocage de capital
- Copies des CIN des associés
- Justificatifs du siège social

Le processus prend généralement 2-3 semaines. Je peux vous accompagner dans toutes ces démarches.',
   true, now() - interval '12 hours', now())
ON CONFLICT (id) DO NOTHING;

-- 30. INSERTION DES DONNÉES DE SUIVI D'USAGE
INSERT INTO public.usage_tracking (user_id, questions_this_month, documents_this_month, last_reset_date, created_at, updated_at)
VALUES
  ('66666666-6666-6666-6666-666666666666', 2, 1, date_trunc('month', now()), now(), now()),
  ('********-7777-7777-7777-********7777', 2, 3, date_trunc('month', now()), now(), now())
ON CONFLICT (user_id) DO UPDATE SET
  questions_this_month = EXCLUDED.questions_this_month,
  documents_this_month = EXCLUDED.documents_this_month;

-- 31. INSERTION DES ABONNEMENTS DE TEST
INSERT INTO public.subscriptions (user_id, tier, status, current_period_start, current_period_end, created_at, updated_at)
VALUES
  ('********-7777-7777-7777-********7777', 'pro_user', 'active', now() - interval '15 days', now() + interval '15 days', now(), now()),
  ('*************-3333-3333-333333333333', 'pro_advocate', 'active', now() - interval '20 days', now() + interval '10 days', now(), now()),
  ('44444444-4444-4444-4444-444444444444', 'pro_advocate', 'active', now() - interval '25 days', now() + interval '5 days', now(), now())
ON CONFLICT (user_id) DO NOTHING;

-- 32. INSERTION DES DOCUMENTS LÉGAUX DE TEST
INSERT INTO public.legal_documents (id, user_id, title, content, template_id, language, status, created_at, updated_at)
VALUES
  ('d1111111-1111-1111-1111-111111111111', '66666666-6666-6666-6666-666666666666',
   'Contrat de Bail Résidentiel',
   '{"type": "rental_contract", "parties": {"landlord": "Ahmed Mansouri", "tenant": "Youssef Mansouri"}, "property": {"address": "Rue Hassan II, Casablanca", "rent": 3500}}',
   'rental_contract', 'ar', 'completed', now() - interval '5 days', now()),

  ('d2222222-2222-2222-2222-************', '********-7777-7777-7777-********7777',
   'Procuration Générale',
   '{"type": "power_of_attorney", "grantor": "Aicha Benjelloun", "agent": "Fatima Alaoui", "powers": ["banking", "real_estate", "legal_proceedings"]}',
   'power_of_attorney', 'ar', 'draft', now() - interval '2 days', now())
ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- PERMISSIONS ET FINALISATION
-- =====================================================

-- 33. ATTRIBUTION DES PERMISSIONS
GRANT ALL ON public.profiles TO authenticated;
GRANT ALL ON public.advocates TO authenticated;
GRANT ALL ON public.legal_questions TO authenticated;
GRANT ALL ON public.responses TO authenticated;
GRANT ALL ON public.legal_documents TO authenticated;
GRANT ALL ON public.subscriptions TO authenticated;
GRANT ALL ON public.usage_tracking TO authenticated;
GRANT ALL ON public.subscribers TO authenticated;

GRANT ALL ON public.profiles TO service_role;
GRANT ALL ON public.advocates TO service_role;
GRANT ALL ON public.legal_questions TO service_role;
GRANT ALL ON public.responses TO service_role;
GRANT ALL ON public.legal_documents TO service_role;
GRANT ALL ON public.subscriptions TO service_role;
GRANT ALL ON public.usage_tracking TO service_role;
GRANT ALL ON public.subscribers TO service_role;

-- =====================================================
-- RÉSUMÉ DES COMPTES CRÉÉS
-- =====================================================

/*
COMPTES ADMINISTRATEURS :
- <EMAIL> (Mot de passe à définir lors de l'inscription)
- <EMAIL> (Mot de passe à définir lors de l'inscription)

COMPTES AVOCATS :
- <EMAIL> (Me. Ahmed Benali - Droit de la famille)
- <EMAIL> (Me. Fatima Alaoui - Droit commercial)
- <EMAIL> (Me. Omar Tazi - Droit pénal)

COMPTES UTILISATEURS :
- <EMAIL> (Youssef Mansouri - Gratuit)
- <EMAIL> (Aicha Benjelloun - Pro User)

DONNÉES INCLUSES :
- 4 questions légales (2 avec réponses, 2 en attente)
- 2 réponses d'avocats détaillées
- 3 profils d'avocats complets avec spécialisations
- 2 documents légaux de test
- Abonnements et suivi d'usage

POUR VOUS CONNECTER :
1. Créez un compte avec l'un des emails ci-dessus
2. Le rôle sera automatiquement assigné
3. Accédez à /admin pour les admins
4. Accédez à /advocate-dashboard pour les avocats
*/
