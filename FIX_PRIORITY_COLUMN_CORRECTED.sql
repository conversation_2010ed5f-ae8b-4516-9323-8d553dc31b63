-- FIXED VERSION: Add Priority Column to Legal Questions Table
-- Execute this script in Supabase SQL Editor to fix the missing priority column

-- 1. Add priority column if it doesn't exist
DO $$
BEGIN
    -- Check if priority column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'legal_questions' 
        AND column_name = 'priority'
        AND table_schema = 'public'
    ) THEN
        -- Add the priority column
        ALTER TABLE public.legal_questions 
        ADD COLUMN priority TEXT DEFAULT 'medium';
        
        RAISE NOTICE 'Priority column added successfully';
    ELSE
        RAISE NOTICE 'Priority column already exists';
    END IF;
END $$;

-- 2. Update existing records to have default priority
UPDATE public.legal_questions 
SET priority = 'medium' 
WHERE priority IS NULL OR priority = '';

-- 3. Add constraint for valid priority values (with proper syntax)
DO $$
BEGIN
    -- Check if constraint already exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'legal_questions_priority_check'
        AND table_name = 'legal_questions'
        AND table_schema = 'public'
    ) THEN
        -- Add the constraint
        ALTER TABLE public.legal_questions 
        ADD CONSTRAINT legal_questions_priority_check 
        CHECK (priority IN ('low', 'medium', 'high', 'urgent'));
        
        RAISE NOTICE 'Priority constraint added successfully';
    ELSE
        RAISE NOTICE 'Priority constraint already exists';
    END IF;
END $$;

-- 4. Verify the changes
SELECT 
    'Column added successfully!' as status,
    column_name,
    data_type,
    column_default,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'legal_questions' 
    AND table_schema = 'public'
    AND column_name = 'priority';

-- 5. Show sample of updated table structure
SELECT 
    column_name,
    data_type,
    column_default
FROM information_schema.columns 
WHERE table_name = 'legal_questions' 
    AND table_schema = 'public'
ORDER BY ordinal_position;

-- Success message
SELECT 'Priority column setup completed successfully!' as message;
