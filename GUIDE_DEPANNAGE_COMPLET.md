# 🔧 **Guide de Dépannage Complet - Law App Morocco**

## 🚨 **Solutions aux Problèmes Critiques**

### **Problème 1: Questions Page - Chargement Infini**

#### **Diagnostic:**
```javascript
// Ouvrez la console du navigateur (F12) et exécutez :
import { DiagnosticTool } from './src/utils/diagnosticTool';
DiagnosticTool.runFullDiagnostic();
```

#### **Solutions:**

**Solution A: Correction RLS (Recommandée)**
1. **Allez dans Supabase Dashboard** → SQL Editor
2. **Exécutez le script** `FIX_RLS_POLICIES.sql`
3. **Redémarrez l'application**

**Solution B: Test Direct**
```javascript
// Dans la console du navigateur
import { supabase } from './src/integrations/supabase/client';

// Test direct d'accès aux questions
const testQuestions = async () => {
  const { data, error } = await supabase
    .from('legal_questions')
    .select('*')
    .limit(5);
  
  console.log('Questions:', data);
  console.log('Erreur:', error);
};

testQuestions();
```

**Solution C: Vérification Authentification**
```javascript
// Vérifiez l'état d'authentification
const checkAuth = async () => {
  const { data: { user }, error } = await supabase.auth.getUser();
  console.log('Utilisateur:', user);
  console.log('Erreur auth:', error);
};

checkAuth();
```

### **Problème 2: Déconnexion Automatique**

#### **Causes Possibles:**
- Session expirée
- Politiques RLS trop restrictives
- Erreurs de profil utilisateur

#### **Solutions:**

**Solution A: Vérification Session**
```javascript
// Vérifiez la session actuelle
const checkSession = async () => {
  const { data: { session }, error } = await supabase.auth.getSession();
  console.log('Session:', session);
  console.log('Expire à:', new Date(session?.expires_at * 1000));
};

checkSession();
```

**Solution B: Forcer Reconnexion**
```javascript
// Forcer une nouvelle connexion
const forceReauth = async () => {
  await supabase.auth.signOut();
  // Reconnectez-vous avec vos identifiants
};
```

**Solution C: Vérifier Profil**
```sql
-- Dans Supabase SQL Editor
SELECT id, email, role, is_verified 
FROM public.profiles 
WHERE email = '<EMAIL>';
```

### **Problème 3: Admin Dashboard Vide**

#### **Diagnostic:**
```javascript
// Test accès admin
const testAdminAccess = async () => {
  const { data: profiles, error } = await supabase
    .from('profiles')
    .select('*')
    .limit(5);
  
  console.log('Accès profiles:', profiles);
  console.log('Erreur:', error);
};

testAdminAccess();
```

#### **Solutions:**

**Solution A: Vérifier Rôle Admin**
```sql
-- Promouvoir votre compte en admin
UPDATE public.profiles 
SET role = 'admin', is_verified = true 
WHERE email = '<EMAIL>';
```

**Solution B: Test Comptes Prédéfinis**
Utilisez les comptes de test créés par le script RLS :
- **Email:** `<EMAIL>`
- **Mot de passe:** Créez le compte avec ce email

**Solution C: Vérification Permissions**
```sql
-- Vérifiez les politiques RLS actives
SELECT schemaname, tablename, policyname, permissive, roles, cmd 
FROM pg_policies 
WHERE schemaname = 'public' AND tablename = 'profiles';
```

### **Problème 4: Avocats Non Visibles**

#### **Solutions:**

**Solution A: Vérifier Avocats**
```sql
-- Voir tous les avocats
SELECT a.*, p.full_name, p.email, p.is_verified
FROM public.advocates a
JOIN public.profiles p ON a.profile_id = p.id;
```

**Solution B: Créer Avocat Test**
```sql
-- Créer un avocat de test
INSERT INTO public.advocates (profile_id, specializations, bio, hourly_rate)
VALUES 
  ('00000000-0000-0000-0000-000000000002', 
   ARRAY['family', 'civil'], 
   'Avocat de test', 
   500.00);
```

## 🔧 **Scripts de Réparation Rapide**

### **Script 1: Réinitialisation Complète**
```sql
-- ATTENTION: Ceci supprime toutes les politiques RLS
-- Utilisez seulement en cas d'urgence

-- Désactiver RLS temporairement
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.legal_questions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.advocates DISABLE ROW LEVEL SECURITY;

-- Créer admin d'urgence
UPDATE public.profiles 
SET role = 'admin', is_verified = true 
WHERE email = '<EMAIL>';

-- Réactiver RLS après test
-- ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE public.legal_questions ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE public.advocates ENABLE ROW LEVEL SECURITY;
```

### **Script 2: Données de Test**
```sql
-- Créer des données de test pour vérifier le fonctionnement
INSERT INTO public.legal_questions (user_id, title, description, category, status)
VALUES 
  ('votre-user-id', 'Question de test', 'Description de test', 'general', 'pending');
```

### **Script 3: Nettoyage Cache**
```javascript
// Dans la console du navigateur
localStorage.clear();
sessionStorage.clear();
location.reload();
```

## 📋 **Checklist de Dépannage**

### **Étape 1: Vérifications de Base**
- [ ] Connexion internet stable
- [ ] Supabase accessible (vérifiez status.supabase.com)
- [ ] Clés API correctes dans `.env`
- [ ] Console du navigateur sans erreurs critiques

### **Étape 2: Authentification**
- [ ] Utilisateur connecté
- [ ] Session valide
- [ ] Profil utilisateur existe
- [ ] Rôle utilisateur correct

### **Étape 3: Base de Données**
- [ ] Tables créées
- [ ] Politiques RLS configurées
- [ ] Données de test présentes
- [ ] Permissions accordées

### **Étape 4: Interface**
- [ ] Composants se chargent
- [ ] Données s'affichent
- [ ] Actions fonctionnent
- [ ] Navigation fluide

## 🆘 **Actions d'Urgence**

### **Si Rien ne Fonctionne:**

1. **Exécutez le script de diagnostic complet**
2. **Appliquez le script RLS de correction**
3. **Créez un compte admin de test**
4. **Testez avec les comptes prédéfinis**
5. **Contactez le support si nécessaire**

### **Comptes de Test Garantis:**
Après avoir exécuté `FIX_RLS_POLICIES.sql` :
- `<EMAIL>` (Admin)
- `<EMAIL>` (Avocat vérifié)
- `<EMAIL>` (Utilisateur)

### **URLs de Test:**
- `/questions` - Page des questions
- `/admin` - Dashboard admin
- `/advocate-dashboard` - Dashboard avocat
- `/test` - Page de test simple
- `/debug` - Page de diagnostic

## 📞 **Support Technique**

Si les problèmes persistent :
1. **Exportez les logs** de la console
2. **Notez les messages d'erreur** exacts
3. **Vérifiez les politiques RLS** dans Supabase
4. **Testez avec différents navigateurs**
5. **Vérifiez la configuration** des variables d'environnement

**L'application devrait fonctionner parfaitement après ces corrections !** 🎉
