-- ADD RATING SYSTEM FOR LAW APP MOROCCO
-- Execute this script in Supabase SQL Editor to add rating functionality

-- 1. Create response_ratings table for storing user ratings of advocate responses
CREATE TABLE IF NOT EXISTS public.response_ratings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  response_id UUID NOT NULL REFERENCES public.responses(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  comment TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  
  -- Ensure one rating per user per response
  UNIQUE(response_id, user_id)
);

-- 2. Enable Row Level Security for response_ratings
ALTER TABLE public.response_ratings ENABLE ROW LEVEL SECURITY;

-- 3. Create RLS policies for response_ratings
CREATE POLICY "Users can rate responses to their questions" ON public.response_ratings
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.responses r
      JOIN public.legal_questions lq ON r.question_id = lq.id
      WHERE r.id = response_id AND lq.user_id = auth.uid() AND auth.uid() = user_id
    )
  );

CREATE POLICY "Users can view their own ratings" ON public.response_ratings
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own ratings" ON public.response_ratings
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own ratings" ON public.response_ratings
  FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Advocates can view ratings for their responses" ON public.response_ratings
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.responses r
      JOIN public.advocates a ON r.advocate_id = a.id
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE r.id = response_id AND p.id = auth.uid()
    )
  );

CREATE POLICY "Admins can manage all ratings" ON public.response_ratings
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 4. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_response_ratings_response_id ON public.response_ratings(response_id);
CREATE INDEX IF NOT EXISTS idx_response_ratings_user_id ON public.response_ratings(user_id);
CREATE INDEX IF NOT EXISTS idx_response_ratings_rating ON public.response_ratings(rating);

-- 5. Create function to update advocate rating when response is rated
CREATE OR REPLACE FUNCTION public.update_advocate_rating_on_response_rating()
RETURNS TRIGGER AS $$
DECLARE
  advocate_uuid UUID;
  avg_rating DECIMAL(3,2);
  total_ratings INTEGER;
BEGIN
  -- Get the advocate ID from the response
  SELECT r.advocate_id INTO advocate_uuid
  FROM public.responses r
  WHERE r.id = COALESCE(NEW.response_id, OLD.response_id);
  
  -- Calculate new average rating for this advocate
  SELECT 
    ROUND(AVG(rr.rating)::numeric, 2),
    COUNT(rr.rating)
  INTO avg_rating, total_ratings
  FROM public.response_ratings rr
  JOIN public.responses r ON rr.response_id = r.id
  WHERE r.advocate_id = advocate_uuid;
  
  -- Update advocate's rating and total_reviews
  UPDATE public.advocates
  SET 
    rating = COALESCE(avg_rating, 0.0),
    total_reviews = COALESCE(total_ratings, 0),
    updated_at = now()
  WHERE id = advocate_uuid;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Create triggers to automatically update advocate rating
CREATE TRIGGER on_response_rating_changed
  AFTER INSERT OR UPDATE OR DELETE ON public.response_ratings
  FOR EACH ROW EXECUTE PROCEDURE public.update_advocate_rating_on_response_rating();

-- 7. Create function to get response with rating
CREATE OR REPLACE FUNCTION public.get_response_with_rating(response_uuid UUID, user_uuid UUID)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'response', row_to_json(r.*),
    'advocate', json_build_object(
      'id', a.id,
      'full_name', p.full_name,
      'email', p.email,
      'specializations', a.specializations,
      'rating', a.rating,
      'total_reviews', a.total_reviews
    ),
    'user_rating', rr.rating,
    'user_rating_comment', rr.comment,
    'average_rating', (
      SELECT ROUND(AVG(rating)::numeric, 2)
      FROM public.response_ratings
      WHERE response_id = response_uuid
    ),
    'total_ratings', (
      SELECT COUNT(*)
      FROM public.response_ratings
      WHERE response_id = response_uuid
    )
  ) INTO result
  FROM public.responses r
  JOIN public.advocates a ON r.advocate_id = a.id
  JOIN public.profiles p ON a.profile_id = p.id
  LEFT JOIN public.response_ratings rr ON rr.response_id = r.id AND rr.user_id = user_uuid
  WHERE r.id = response_uuid;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Create function to get question with responses and ratings
CREATE OR REPLACE FUNCTION public.get_question_with_responses_and_ratings(question_uuid UUID, user_uuid UUID)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'question', row_to_json(lq.*),
    'user_profile', json_build_object(
      'full_name', up.full_name,
      'email', up.email
    ),
    'responses', COALESCE(
      (
        SELECT json_agg(
          json_build_object(
            'id', r.id,
            'response_text', r.response_text,
            'is_approved', r.is_approved,
            'created_at', r.created_at,
            'updated_at', r.updated_at,
            'advocate', json_build_object(
              'id', a.id,
              'full_name', ap.full_name,
              'email', ap.email,
              'specializations', a.specializations,
              'rating', a.rating,
              'total_reviews', a.total_reviews
            ),
            'user_rating', rr.rating,
            'user_rating_comment', rr.comment,
            'average_rating', (
              SELECT ROUND(AVG(rating)::numeric, 2)
              FROM public.response_ratings
              WHERE response_id = r.id
            ),
            'total_ratings', (
              SELECT COUNT(*)
              FROM public.response_ratings
              WHERE response_id = r.id
            )
          )
        )
        FROM public.responses r
        JOIN public.advocates a ON r.advocate_id = a.id
        JOIN public.profiles ap ON a.profile_id = ap.id
        LEFT JOIN public.response_ratings rr ON rr.response_id = r.id AND rr.user_id = user_uuid
        WHERE r.question_id = question_uuid AND r.is_approved = true
        ORDER BY r.created_at ASC
      ),
      '[]'::json
    )
  ) INTO result
  FROM public.legal_questions lq
  JOIN public.profiles up ON lq.user_id = up.id
  WHERE lq.id = question_uuid;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. Grant permissions
GRANT EXECUTE ON FUNCTION public.get_response_with_rating TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_question_with_responses_and_ratings TO authenticated;

-- 10. Success message
SELECT 'Rating system added successfully!' as status;
