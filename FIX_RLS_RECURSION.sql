-- FIX POUR L'ERREUR DE RÉCURSION INFINIE RLS
-- Exécutez ce script dans Supabase SQL Editor

-- 1. Supprimer toutes les politiques existantes qui causent la récursion
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Anyone can view verified advocates" ON public.advocates;
DROP POLICY IF EXISTS "Advocates can manage their own profile" ON public.advocates;
DROP POLICY IF EXISTS "Admins can manage all advocates" ON public.advocates;
DROP POLICY IF EXISTS "Users can view their own questions" ON public.legal_questions;
DROP POLICY IF EXISTS "Users can create their own questions" ON public.legal_questions;
DROP POLICY IF EXISTS "Users can update their own questions" ON public.legal_questions;
DROP POLICY IF EXISTS "Advocates can view all questions" ON public.legal_questions;
DROP POLICY IF EXISTS "Advocates can update questions assigned to them" ON public.legal_questions;
DROP POLICY IF EXISTS "Ad<PERSON> can manage all questions" ON public.legal_questions;
DROP POLICY IF EXISTS "Users can view responses to their questions" ON public.responses;
DROP POLICY IF EXISTS "Advocates can view their own responses" ON public.responses;
DROP POLICY IF EXISTS "Advocates can create responses" ON public.responses;
DROP POLICY IF EXISTS "Advocates can update their own responses" ON public.responses;
DROP POLICY IF EXISTS "Admins can manage all responses" ON public.responses;

-- 2. Créer des politiques RLS SIMPLES sans récursion

-- Politiques pour profiles (SANS récursion)
CREATE POLICY "profiles_select_own" ON public.profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "profiles_update_own" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "profiles_insert_own" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Politiques pour advocates (SIMPLES)
CREATE POLICY "advocates_select_all" ON public.advocates
  FOR SELECT USING (true);

CREATE POLICY "advocates_insert_own" ON public.advocates
  FOR INSERT WITH CHECK (auth.uid() = profile_id);

CREATE POLICY "advocates_update_own" ON public.advocates
  FOR UPDATE USING (auth.uid() = profile_id);

CREATE POLICY "advocates_delete_own" ON public.advocates
  FOR DELETE USING (auth.uid() = profile_id);

-- Politiques pour legal_questions (SIMPLES)
CREATE POLICY "questions_select_own" ON public.legal_questions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "questions_insert_own" ON public.legal_questions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "questions_update_own" ON public.legal_questions
  FOR UPDATE USING (auth.uid() = user_id);

-- Politiques pour responses (SIMPLES)
CREATE POLICY "responses_select_all" ON public.responses
  FOR SELECT USING (true);

CREATE POLICY "responses_insert_all" ON public.responses
  FOR INSERT WITH CHECK (true);

CREATE POLICY "responses_update_all" ON public.responses
  FOR UPDATE USING (true);

-- Politiques pour legal_documents (SIMPLES)
CREATE POLICY "documents_select_own" ON public.legal_documents
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "documents_insert_own" ON public.legal_documents
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "documents_update_own" ON public.legal_documents
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "documents_delete_own" ON public.legal_documents
  FOR DELETE USING (auth.uid() = user_id);

-- Politiques pour subscriptions (SIMPLES)
CREATE POLICY "subscriptions_select_own" ON public.subscriptions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "subscriptions_insert_own" ON public.subscriptions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "subscriptions_update_own" ON public.subscriptions
  FOR UPDATE USING (auth.uid() = user_id);

-- Politiques pour usage_tracking (SIMPLES)
CREATE POLICY "usage_select_own" ON public.usage_tracking
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "usage_insert_own" ON public.usage_tracking
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "usage_update_own" ON public.usage_tracking
  FOR UPDATE USING (auth.uid() = user_id);

-- Politiques pour subscribers (SIMPLES)
CREATE POLICY "subscribers_select_own" ON public.subscribers
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "subscribers_insert_all" ON public.subscribers
  FOR INSERT WITH CHECK (true);

CREATE POLICY "subscribers_update_all" ON public.subscribers
  FOR UPDATE USING (true);

-- 3. Créer une fonction pour vérifier si l'utilisateur est admin (SANS récursion)
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  -- Vérification directe sans utiliser les politiques RLS
  RETURN EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Créer une fonction pour vérifier si l'utilisateur est avocat vérifié
CREATE OR REPLACE FUNCTION public.is_verified_advocate()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'advocate' AND is_verified = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Ajouter des politiques admin APRÈS avoir créé les fonctions
CREATE POLICY "profiles_admin_all" ON public.profiles
  FOR ALL USING (public.is_admin());

CREATE POLICY "advocates_admin_all" ON public.advocates
  FOR ALL USING (public.is_admin());

CREATE POLICY "questions_admin_all" ON public.legal_questions
  FOR ALL USING (public.is_admin());

CREATE POLICY "responses_admin_all" ON public.responses
  FOR ALL USING (public.is_admin());

CREATE POLICY "documents_admin_all" ON public.legal_documents
  FOR ALL USING (public.is_admin());

-- 6. Ajouter des politiques pour les avocats vérifiés
CREATE POLICY "questions_advocates_view" ON public.legal_questions
  FOR SELECT USING (public.is_verified_advocate());

CREATE POLICY "questions_advocates_update" ON public.legal_questions
  FOR UPDATE USING (
    public.is_verified_advocate() AND 
    EXISTS (
      SELECT 1 FROM public.advocates a 
      WHERE a.profile_id = auth.uid() AND a.id = advocate_id
    )
  );

-- 7. Désactiver temporairement RLS sur les tables critiques pour les tests
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.advocates DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.legal_questions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.responses DISABLE ROW LEVEL SECURITY;

-- 8. Message de succès
SELECT 'Politiques RLS corrigées! Récursion infinie résolue.' as status,
       'RLS temporairement désactivé pour les tests.' as note,
       'Vous pouvez maintenant tester la connexion.' as next_step;
