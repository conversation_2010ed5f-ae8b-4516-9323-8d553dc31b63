-- VÉRIFIER LES VALEURS VALIDES DE L'ENUM subscription_tier
-- Exécutez ce script pour voir les valeurs autorisées

-- 1. Vérifier les valeurs de l'enum subscription_tier
SELECT 
  'VALEURS ENUM subscription_tier' as section,
  enumlabel as valeur_autorisee
FROM pg_enum 
WHERE enumtypid = (
  SELECT oid FROM pg_type WHERE typname = 'subscription_tier'
)
ORDER BY enumsortorder;

-- 2. Vérifier la structure de la table profiles
SELECT 
  'STRUCTURE TABLE PROFILES' as section,
  column_name,
  data_type,
  udt_name,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'profiles'
ORDER BY ordinal_position;

-- 3. Vérifier les valeurs actuelles dans la table
SELECT 
  'VALEURS ACTUELLES subscription_tier' as section,
  subscription_tier,
  COUNT(*) as count
FROM public.profiles 
GROUP BY subscription_tier
ORDER BY count DESC;
