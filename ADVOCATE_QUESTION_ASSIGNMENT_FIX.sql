-- ADVOCATE QUESTION ASSIGNMENT FIX FOR LAW APP MOROCCO
-- Execute this script in Supabase SQL Editor to fix question assignment functionality

-- 1. Drop existing function if it exists (to avoid conflicts)
DROP FUNCTION IF EXISTS public.assign_question_to_advocate(UUID, UUID);

-- 2. Create the enhanced assign_question_to_advocate function
CREATE OR REPLACE FUNCTION public.assign_question_to_advocate(
  question_uuid UUID,
  advocate_profile_uuid UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  advocate_uuid UUID;
  question_status TEXT;
  current_advocate_id UUID;
BEGIN
  -- Check if question exists and get current status
  SELECT status, advocate_id INTO question_status, current_advocate_id
  FROM public.legal_questions
  WHERE id = question_uuid;
  
  IF question_status IS NULL THEN
    RAISE EXCEPTION 'Question not found';
  END IF;
  
  -- Check if question is already assigned
  IF current_advocate_id IS NOT NULL THEN
    RAISE EXCEPTION 'Question is already assigned to another advocate';
  END IF;
  
  -- Get advocate ID from profile ID
  SELECT id INTO advocate_uuid
  FROM public.advocates
  WHERE profile_id = advocate_profile_uuid;
  
  -- If advocate doesn't exist, create advocate record
  IF advocate_uuid IS NULL THEN
    -- Verify the profile exists and is an advocate
    IF NOT EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = advocate_profile_uuid 
      AND role = 'advocate' 
      AND is_verified = true
    ) THEN
      RAISE EXCEPTION 'Profile is not a verified advocate';
    END IF;
    
    -- Create advocate record
    INSERT INTO public.advocates (
      profile_id,
      specializations,
      bio,
      hourly_rate,
      rating,
      total_reviews,
      availability,
      created_at,
      updated_at
    ) VALUES (
      advocate_profile_uuid,
      ARRAY['general'],
      '',
      500.00,
      0.0,
      0,
      '{"status": "available"}',
      now(),
      now()
    ) RETURNING id INTO advocate_uuid;
    
    RAISE NOTICE 'Created new advocate record for profile %', advocate_profile_uuid;
  END IF;
  
  -- Update the question
  UPDATE public.legal_questions
  SET 
    advocate_id = advocate_uuid,
    status = 'assigned',
    updated_at = now()
  WHERE id = question_uuid
    AND advocate_id IS NULL; -- Double-check it's still unassigned
  
  -- Check if update was successful
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Failed to assign question - it may have been assigned to another advocate';
  END IF;
  
  RAISE NOTICE 'Question % assigned to advocate %', question_uuid, advocate_uuid;
  RETURN TRUE;
  
EXCEPTION 
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in assign_question_to_advocate: %', SQLERRM;
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Create function to get available questions for advocates
CREATE OR REPLACE FUNCTION public.get_available_questions_for_advocate(
  advocate_profile_uuid UUID DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  title TEXT,
  description TEXT,
  category TEXT,
  priority TEXT,
  created_at TIMESTAMPTZ,
  user_name TEXT,
  user_email TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    lq.id,
    lq.title,
    lq.description,
    lq.category,
    lq.priority,
    lq.created_at,
    p.full_name as user_name,
    p.email as user_email
  FROM public.legal_questions lq
  JOIN public.profiles p ON lq.user_id = p.id
  WHERE lq.advocate_id IS NULL 
    AND lq.status = 'pending'
  ORDER BY 
    CASE lq.priority 
      WHEN 'urgent' THEN 1
      WHEN 'high' THEN 2
      WHEN 'medium' THEN 3
      WHEN 'low' THEN 4
      ELSE 5
    END,
    lq.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Create function to get advocate's assigned questions
CREATE OR REPLACE FUNCTION public.get_advocate_assigned_questions(
  advocate_profile_uuid UUID
)
RETURNS TABLE (
  id UUID,
  title TEXT,
  description TEXT,
  category TEXT,
  priority TEXT,
  status TEXT,
  is_answered BOOLEAN,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  user_name TEXT,
  user_email TEXT,
  response_count BIGINT
) AS $$
DECLARE
  advocate_uuid UUID;
BEGIN
  -- Get advocate ID from profile ID
  SELECT a.id INTO advocate_uuid
  FROM public.advocates a
  WHERE a.profile_id = advocate_profile_uuid;
  
  IF advocate_uuid IS NULL THEN
    RAISE EXCEPTION 'Advocate profile not found';
  END IF;
  
  RETURN QUERY
  SELECT 
    lq.id,
    lq.title,
    lq.description,
    lq.category,
    lq.priority,
    lq.status,
    lq.is_answered,
    lq.created_at,
    lq.updated_at,
    p.full_name as user_name,
    p.email as user_email,
    COALESCE(r.response_count, 0) as response_count
  FROM public.legal_questions lq
  JOIN public.profiles p ON lq.user_id = p.id
  LEFT JOIN (
    SELECT question_id, COUNT(*) as response_count
    FROM public.responses
    GROUP BY question_id
  ) r ON lq.id = r.question_id
  WHERE lq.advocate_id = advocate_uuid
  ORDER BY 
    CASE lq.status
      WHEN 'assigned' THEN 1
      WHEN 'in_progress' THEN 2
      WHEN 'answered' THEN 3
      ELSE 4
    END,
    lq.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Create function to unassign question from advocate
CREATE OR REPLACE FUNCTION public.unassign_question_from_advocate(
  question_uuid UUID,
  advocate_profile_uuid UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  advocate_uuid UUID;
BEGIN
  -- Get advocate ID from profile ID
  SELECT id INTO advocate_uuid
  FROM public.advocates
  WHERE profile_id = advocate_profile_uuid;
  
  IF advocate_uuid IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Update the question to unassign it
  UPDATE public.legal_questions
  SET 
    advocate_id = NULL,
    status = 'pending',
    updated_at = now()
  WHERE id = question_uuid
    AND advocate_id = advocate_uuid;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Grant permissions to authenticated users
GRANT EXECUTE ON FUNCTION public.assign_question_to_advocate TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_available_questions_for_advocate TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_advocate_assigned_questions TO authenticated;
GRANT EXECUTE ON FUNCTION public.unassign_question_from_advocate TO authenticated;

-- 7. Create or update RLS policies for question assignment

-- Policy for advocates to view all pending questions
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'legal_questions' 
    AND policyname = 'Advocates can view pending questions'
  ) THEN
    CREATE POLICY "Advocates can view pending questions" ON public.legal_questions
      FOR SELECT USING (
        advocate_id IS NULL 
        AND status = 'pending'
        AND EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND role = 'advocate' 
          AND is_verified = true
        )
      );
  END IF;
END $$;

-- Policy for advocates to update questions assigned to them
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'legal_questions' 
    AND policyname = 'Advocates can assign questions to themselves'
  ) THEN
    CREATE POLICY "Advocates can assign questions to themselves" ON public.legal_questions
      FOR UPDATE USING (
        advocate_id IS NULL 
        AND EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND role = 'advocate' 
          AND is_verified = true
        )
      );
  END IF;
END $$;

-- 8. Create indexes for better performance (without CONCURRENTLY for Supabase compatibility)
CREATE INDEX IF NOT EXISTS idx_legal_questions_pending
ON public.legal_questions(status, advocate_id)
WHERE advocate_id IS NULL AND status = 'pending';

CREATE INDEX IF NOT EXISTS idx_legal_questions_advocate_status
ON public.legal_questions(advocate_id, status)
WHERE advocate_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_advocates_profile_lookup
ON public.advocates(profile_id);

-- 9. Create trigger to automatically update question status
CREATE OR REPLACE FUNCTION public.update_question_assignment_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  -- Update timestamp when advocate_id changes
  IF OLD.advocate_id IS DISTINCT FROM NEW.advocate_id THEN
    NEW.updated_at = now();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop trigger if exists and recreate
DROP TRIGGER IF EXISTS trigger_update_question_assignment ON public.legal_questions;
CREATE TRIGGER trigger_update_question_assignment
  BEFORE UPDATE ON public.legal_questions
  FOR EACH ROW
  EXECUTE FUNCTION public.update_question_assignment_timestamp();

-- 10. Success message
SELECT 
  'Advocate question assignment functions created successfully!' as status,
  'All functions, policies, and triggers are now ready.' as details,
  'Test the assignment functionality in the advocate interface.' as next_step;
