# Law App Morocco - Documentation

A comprehensive legal assistance platform for Moroccan citizens and lawyers.

## Features

### For Users
- Legal document generation
- Legal consultation with verified lawyers
- AI-powered legal assistant (powered by n8n webhook)
- Ask legal questions and get answers from lawyers
- Multi-language support (Arabic/French)
- Subscription-based access

### For Lawyers
- Dedicated lawyer dashboard
- Manage assigned questions
- Answer user questions
- Profile management with specializations
- Rating and review system
- Availability management

### For Administrators
- Complete admin dashboard with statistics
- User management (verify/unverify accounts)
- Lawyer account activation/deactivation
- System analytics and monitoring
- Role-based access control

## Tech Stack

- React 18 with TypeScript
- Vite for build tooling
- Tailwind CSS for styling
- Supabase for backend services
- React Query for data fetching
- React Hook Form for form handling
- Lucide React for icons
- n8n webhook for AI legal assistant

## Database Schema

The application uses the following main tables:
- `profiles` - User profiles with roles (admin, user, advocate)
- `advocates` - Extended advocate information
- `legal_questions` - Questions asked by users
- `legal_documents` - Generated legal documents
- `subscriptions` - Subscription management
- `usage_tracking` - Track usage limits for free users

## User Roles

### User (مستخدم عادي)
- Can ask legal questions
- Can generate documents
- Can use AI assistant
- Limited features on free plan

### Advocate (محامي)
- All user features
- Can answer questions
- Has dedicated dashboard
- Profile verification required by admin

### Admin (مدير)
- Full system access
- User management
- Statistics and analytics
- Lawyer verification

## API Integration

### Legal Assistant API
The AI legal assistant uses n8n webhook:
- **Endpoint**: `https://benzaid.app.n8n.cloud/webhook/legal-assistant`
- **Method**: POST
- **Input**: `{ "message": "user question" }`
- **Output**: `[{ "output": "AI response" }]`

## Authentication Flow

1. User signs up with role selection (user/advocate)
2. Users are auto-verified
3. Advocates require admin approval
4. Role-based dashboard redirection
5. Secure logout with state cleanup

## Key Components

### Services
- `authService.ts` - Authentication and user management
- `questionsService.ts` - Legal questions management
- `chatbotService.ts` - AI assistant integration

### Pages
- `AdvocateDashboard.tsx` - Lawyer dashboard
- `Admin.tsx` - Admin panel
- `Questions.tsx` - Questions management
- `Chatbot.tsx` - AI assistant interface

### Features Implemented

1. **Full-width layout** - Application uses full screen width
2. **Role-based authentication** - Users can select role during signup
3. **Advocate dashboard** - Dedicated interface for lawyers
4. **Question-answer system** - Users ask, lawyers answer
5. **Admin panel** - Complete admin functionality
6. **Updated chatbot API** - Integrated with new n8n webhook
7. **Fixed logout** - Proper state cleanup on logout

## Database Tables Structure

```sql
-- User profiles with roles
CREATE TABLE profiles (
  id UUID PRIMARY KEY,
  email TEXT NOT NULL,
  full_name TEXT,
  role user_role NOT NULL DEFAULT 'user',
  is_verified BOOLEAN DEFAULT FALSE,
  -- ... other fields
);

-- Advocate-specific data
CREATE TABLE advocates (
  id UUID PRIMARY KEY,
  profile_id UUID REFERENCES profiles(id),
  specializations TEXT[],
  bio TEXT,
  hourly_rate DECIMAL(10,2),
  rating DECIMAL(3,2),
  -- ... other fields
);

-- Legal questions
CREATE TABLE legal_questions (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES profiles(id),
  advocate_id UUID REFERENCES advocates(id),
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  category TEXT,
  status TEXT DEFAULT 'pending',
  is_answered BOOLEAN DEFAULT FALSE,
  -- ... other fields
);
```

## Usage Instructions

### For Users
1. Sign up with "User" role
2. Navigate to Questions page
3. Ask legal questions
4. Use AI assistant for quick help
5. Generate legal documents

### For Lawyers
1. Sign up with "Advocate" role
2. Wait for admin approval
3. Access advocate dashboard
4. Take available questions
5. Answer assigned questions

### For Admins
1. Access admin panel
2. Verify lawyer accounts
3. Monitor system statistics
4. Manage users and roles

## Deployment

The application is ready for deployment with:
- Environment variables configured
- Database schema applied
- All services integrated
- Full functionality tested

## Future Enhancements

- Advanced analytics
- Payment integration
- Document templates
- Video consultations
- Mobile app
