-- ENHANCED ADMIN DATABASE FUNCTIONS FOR LAW APP MOROCCO
-- Execute this script in Supabase SQL Editor to add comprehensive admin capabilities

-- 1. Function to create user accounts from admin panel
CREATE OR REPLACE FUNCTION public.admin_create_user(
  user_email TEXT,
  user_name TEXT,
  user_role TEXT DEFAULT 'user',
  user_phone TEXT DEFAULT NULL,
  subscription_tier TEXT DEFAULT 'free',
  is_verified BOOLEAN DEFAULT TRUE,
  advocate_specializations TEXT[] DEFAULT NULL,
  advocate_bio TEXT DEFAULT NULL,
  advocate_hourly_rate DECIMAL DEFAULT 500.00
)
RETURNS JSON AS $$
DECLARE
  new_user_id UUID;
  advocate_id UUID;
  result JSON;
BEGIN
  -- Generate a new UUID for the user
  new_user_id := gen_random_uuid();
  
  -- Insert into profiles table
  INSERT INTO public.profiles (
    id, email, full_name, phone, role, subscription_tier, is_verified, created_at, updated_at
  ) VALUES (
    new_user_id, user_email, user_name, user_phone, user_role::user_role, 
    subscription_tier::subscription_tier, is_verified, now(), now()
  );
  
  -- If role is advocate, create advocate profile
  IF user_role = 'advocate' THEN
    INSERT INTO public.advocates (
      profile_id, specializations, bio, hourly_rate, is_featured, rating, total_reviews, created_at, updated_at
    ) VALUES (
      new_user_id, 
      COALESCE(advocate_specializations, ARRAY[]::TEXT[]), 
      COALESCE(advocate_bio, ''), 
      advocate_hourly_rate, 
      false, 0.0, 0, now(), now()
    ) RETURNING id INTO advocate_id;
  END IF;
  
  -- Create usage tracking record
  INSERT INTO public.usage_tracking (user_id, questions_this_month, documents_this_month, last_reset_date)
  VALUES (new_user_id, 0, 0, now());
  
  -- Prepare result
  result := json_build_object(
    'success', true,
    'user_id', new_user_id,
    'email', user_email,
    'role', user_role,
    'advocate_id', advocate_id,
    'message', 'User created successfully'
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'success', false,
    'error', SQLERRM,
    'message', 'Failed to create user'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Function to update user profiles
CREATE OR REPLACE FUNCTION public.admin_update_user(
  user_id UUID,
  user_name TEXT DEFAULT NULL,
  user_phone TEXT DEFAULT NULL,
  user_role TEXT DEFAULT NULL,
  subscription_tier TEXT DEFAULT NULL,
  is_verified BOOLEAN DEFAULT NULL,
  advocate_specializations TEXT[] DEFAULT NULL,
  advocate_bio TEXT DEFAULT NULL,
  advocate_hourly_rate DECIMAL DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  current_role TEXT;
  advocate_exists BOOLEAN;
  result JSON;
BEGIN
  -- Get current role
  SELECT role INTO current_role FROM public.profiles WHERE id = user_id;
  
  IF current_role IS NULL THEN
    RETURN json_build_object('success', false, 'message', 'User not found');
  END IF;
  
  -- Update profiles table
  UPDATE public.profiles SET
    full_name = COALESCE(user_name, full_name),
    phone = COALESCE(user_phone, phone),
    role = COALESCE(user_role::user_role, role),
    subscription_tier = COALESCE(subscription_tier::subscription_tier, subscription_tier),
    is_verified = COALESCE(is_verified, is_verified),
    updated_at = now()
  WHERE id = user_id;
  
  -- Handle advocate profile
  IF user_role = 'advocate' OR current_role = 'advocate' THEN
    SELECT EXISTS(SELECT 1 FROM public.advocates WHERE profile_id = user_id) INTO advocate_exists;
    
    IF user_role = 'advocate' AND NOT advocate_exists THEN
      -- Create advocate profile
      INSERT INTO public.advocates (
        profile_id, specializations, bio, hourly_rate, is_featured, rating, total_reviews
      ) VALUES (
        user_id, 
        COALESCE(advocate_specializations, ARRAY[]::TEXT[]), 
        COALESCE(advocate_bio, ''), 
        COALESCE(advocate_hourly_rate, 500.00), 
        false, 0.0, 0
      );
    ELSIF advocate_exists THEN
      -- Update advocate profile
      UPDATE public.advocates SET
        specializations = COALESCE(advocate_specializations, specializations),
        bio = COALESCE(advocate_bio, bio),
        hourly_rate = COALESCE(advocate_hourly_rate, hourly_rate),
        updated_at = now()
      WHERE profile_id = user_id;
    END IF;
    
    -- If role changed from advocate to something else, handle cleanup
    IF user_role != 'advocate' AND current_role = 'advocate' THEN
      -- Unassign questions
      UPDATE public.legal_questions SET advocate_id = NULL WHERE advocate_id IN (
        SELECT id FROM public.advocates WHERE profile_id = user_id
      );
      -- Delete advocate profile
      DELETE FROM public.advocates WHERE profile_id = user_id;
    END IF;
  END IF;
  
  result := json_build_object(
    'success', true,
    'user_id', user_id,
    'message', 'User updated successfully'
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'success', false,
    'error', SQLERRM,
    'message', 'Failed to update user'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Function to delete user accounts
CREATE OR REPLACE FUNCTION public.admin_delete_user(user_id UUID)
RETURNS JSON AS $$
DECLARE
  user_email TEXT;
  result JSON;
BEGIN
  -- Get user email for logging
  SELECT email INTO user_email FROM public.profiles WHERE id = user_id;
  
  IF user_email IS NULL THEN
    RETURN json_build_object('success', false, 'message', 'User not found');
  END IF;
  
  -- Delete will cascade to all related tables due to foreign key constraints
  DELETE FROM public.profiles WHERE id = user_id;
  
  result := json_build_object(
    'success', true,
    'user_id', user_id,
    'email', user_email,
    'message', 'User deleted successfully'
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'success', false,
    'error', SQLERRM,
    'message', 'Failed to delete user'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Function to get comprehensive user analytics
CREATE OR REPLACE FUNCTION public.get_user_analytics()
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'total_users', (SELECT COUNT(*) FROM public.profiles),
    'users_by_role', (
      SELECT json_object_agg(role, count)
      FROM (
        SELECT role, COUNT(*) as count
        FROM public.profiles
        GROUP BY role
      ) role_counts
    ),
    'verification_status', (
      SELECT json_build_object(
        'verified', COUNT(*) FILTER (WHERE is_verified = true),
        'unverified', COUNT(*) FILTER (WHERE is_verified = false)
      )
      FROM public.profiles
    ),
    'subscription_tiers', (
      SELECT json_object_agg(subscription_tier, count)
      FROM (
        SELECT subscription_tier, COUNT(*) as count
        FROM public.profiles
        GROUP BY subscription_tier
      ) tier_counts
    ),
    'new_users_last_30_days', (
      SELECT COUNT(*) FROM public.profiles 
      WHERE created_at >= now() - interval '30 days'
    ),
    'active_users_last_7_days', (
      SELECT COUNT(DISTINCT user_id) FROM public.legal_questions 
      WHERE created_at >= now() - interval '7 days'
    )
  ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Function to get questions analytics
CREATE OR REPLACE FUNCTION public.get_questions_analytics()
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'total_questions', (SELECT COUNT(*) FROM public.legal_questions),
    'answered_questions', (SELECT COUNT(*) FROM public.legal_questions WHERE is_answered = true),
    'pending_questions', (SELECT COUNT(*) FROM public.legal_questions WHERE is_answered = false),
    'questions_by_category', (
      SELECT json_object_agg(category, count)
      FROM (
        SELECT category, COUNT(*) as count
        FROM public.legal_questions
        GROUP BY category
      ) category_counts
    ),
    'questions_by_status', (
      SELECT json_object_agg(status, count)
      FROM (
        SELECT status, COUNT(*) as count
        FROM public.legal_questions
        GROUP BY status
      ) status_counts
    ),
    'questions_last_30_days', (
      SELECT COUNT(*) FROM public.legal_questions 
      WHERE created_at >= now() - interval '30 days'
    ),
    'average_response_time_hours', (
      SELECT ROUND(AVG(EXTRACT(EPOCH FROM (r.created_at - lq.created_at)) / 3600), 2)
      FROM public.legal_questions lq
      JOIN public.responses r ON lq.id = r.question_id
      WHERE lq.created_at >= now() - interval '30 days'
    ),
    'top_advocates_by_responses', (
      SELECT json_agg(
        json_build_object(
          'advocate_name', p.full_name,
          'advocate_email', p.email,
          'response_count', response_count
        )
      )
      FROM (
        SELECT a.profile_id, COUNT(*) as response_count
        FROM public.responses r
        JOIN public.advocates a ON r.advocate_id = a.id
        WHERE r.created_at >= now() - interval '30 days'
        GROUP BY a.profile_id
        ORDER BY response_count DESC
        LIMIT 5
      ) top_advocates
      JOIN public.profiles p ON top_advocates.profile_id = p.id
    )
  ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Function to get advocate performance analytics
CREATE OR REPLACE FUNCTION public.get_advocate_analytics()
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'total_advocates', (SELECT COUNT(*) FROM public.advocates),
    'verified_advocates', (
      SELECT COUNT(*) FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.is_verified = true
    ),
    'average_rating', (
      SELECT ROUND(AVG(rating), 2) FROM public.advocates WHERE rating > 0
    ),
    'advocates_by_specialization', (
      SELECT json_object_agg(specialization, count)
      FROM (
        SELECT unnest(specializations) as specialization, COUNT(*) as count
        FROM public.advocates
        WHERE specializations IS NOT NULL AND array_length(specializations, 1) > 0
        GROUP BY specialization
      ) spec_counts
    ),
    'response_rates', (
      SELECT json_build_object(
        'total_responses', COUNT(*),
        'responses_last_7_days', COUNT(*) FILTER (WHERE created_at >= now() - interval '7 days'),
        'responses_last_30_days', COUNT(*) FILTER (WHERE created_at >= now() - interval '30 days')
      )
      FROM public.responses
    ),
    'advocate_performance', (
      SELECT json_agg(
        json_build_object(
          'advocate_name', p.full_name,
          'advocate_email', p.email,
          'specializations', a.specializations,
          'rating', a.rating,
          'total_reviews', a.total_reviews,
          'questions_handled', COALESCE(q.question_count, 0),
          'responses_given', COALESCE(r.response_count, 0),
          'hourly_rate', a.hourly_rate
        )
      )
      FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      LEFT JOIN (
        SELECT advocate_id, COUNT(*) as question_count
        FROM public.legal_questions
        WHERE advocate_id IS NOT NULL
        GROUP BY advocate_id
      ) q ON a.id = q.advocate_id
      LEFT JOIN (
        SELECT advocate_id, COUNT(*) as response_count
        FROM public.responses
        GROUP BY advocate_id
      ) r ON a.id = r.advocate_id
      WHERE p.is_verified = true
      ORDER BY COALESCE(r.response_count, 0) DESC
    )
  ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Function for bulk user operations
CREATE OR REPLACE FUNCTION public.admin_bulk_update_users(
  user_ids UUID[],
  operation TEXT,
  new_role TEXT DEFAULT NULL,
  new_verification_status BOOLEAN DEFAULT NULL,
  new_subscription_tier TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  affected_count INTEGER := 0;
  result JSON;
BEGIN
  IF operation = 'verify' THEN
    UPDATE public.profiles 
    SET is_verified = true, updated_at = now()
    WHERE id = ANY(user_ids);
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
  ELSIF operation = 'unverify' THEN
    UPDATE public.profiles 
    SET is_verified = false, updated_at = now()
    WHERE id = ANY(user_ids);
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
  ELSIF operation = 'change_role' AND new_role IS NOT NULL THEN
    UPDATE public.profiles 
    SET role = new_role::user_role, updated_at = now()
    WHERE id = ANY(user_ids);
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
  ELSIF operation = 'change_subscription' AND new_subscription_tier IS NOT NULL THEN
    UPDATE public.profiles 
    SET subscription_tier = new_subscription_tier::subscription_tier, updated_at = now()
    WHERE id = ANY(user_ids);
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
  ELSIF operation = 'delete' THEN
    DELETE FROM public.profiles WHERE id = ANY(user_ids);
    GET DIAGNOSTICS affected_count = ROW_COUNT;
    
  ELSE
    RETURN json_build_object('success', false, 'message', 'Invalid operation');
  END IF;
  
  result := json_build_object(
    'success', true,
    'operation', operation,
    'affected_count', affected_count,
    'message', format('%s users %s successfully', affected_count, operation)
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'success', false,
    'error', SQLERRM,
    'message', 'Bulk operation failed'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Grant permissions
GRANT EXECUTE ON FUNCTION public.admin_create_user TO authenticated;
GRANT EXECUTE ON FUNCTION public.admin_update_user TO authenticated;
GRANT EXECUTE ON FUNCTION public.admin_delete_user TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_analytics TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_questions_analytics TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_advocate_analytics TO authenticated;
GRANT EXECUTE ON FUNCTION public.admin_bulk_update_users TO authenticated;

-- Success message
SELECT 'Enhanced admin database functions created successfully!' as status;
