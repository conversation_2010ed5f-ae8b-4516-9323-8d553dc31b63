-- ENHANCED QUESTIONS DATABASE FUNCTIONS FOR LAW APP MOROCCO
-- Execute this script in Supabase SQL Editor to add enhanced question functionality

-- 1. Function to create questions with enhanced validation and auto-assignment
CREATE OR REPLACE FUNCTION public.create_question_enhanced(
  user_id UUID,
  title TEXT,
  description TEXT,
  category TEXT DEFAULT 'general',
  priority TEXT DEFAULT 'medium',
  preferred_advocate_id UUID DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  new_question_id UUID;
  assigned_advocate_id UUID;
  result JSON;
BEGIN
  -- Validate inputs
  IF user_id IS NULL THEN
    RETURN json_build_object('success', false, 'error', 'معرف المستخدم مطلوب');
  END IF;
  
  IF title IS NULL OR LENGTH(TRIM(title)) < 10 THEN
    RETURN json_build_object('success', false, 'error', 'عنوان السؤال يجب أن يكون 10 أحرف على الأقل');
  END IF;
  
  IF description IS NULL OR LENGTH(TRIM(description)) < 20 THEN
    RETURN json_build_object('success', false, 'error', 'وصف السؤال يجب أن يكون 20 حرف على الأقل');
  END IF;

  -- Create the question
  INSERT INTO public.legal_questions (
    user_id,
    title,
    description,
    category,
    priority,
    status,
    is_answered,
    created_at,
    updated_at
  ) VALUES (
    user_id,
    TRIM(title),
    TRIM(description),
    category,
    priority,
    'pending',
    false,
    now(),
    now()
  ) RETURNING id INTO new_question_id;

  -- Handle advocate assignment
  IF preferred_advocate_id IS NOT NULL THEN
    -- Check if preferred advocate exists and is verified
    IF EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE a.profile_id = preferred_advocate_id AND p.is_verified = true
    ) THEN
      -- Get the advocate's internal ID
      SELECT a.id INTO assigned_advocate_id
      FROM public.advocates a
      WHERE a.profile_id = preferred_advocate_id;
      
      -- Assign the question
      UPDATE public.legal_questions
      SET advocate_id = assigned_advocate_id, status = 'assigned', updated_at = now()
      WHERE id = new_question_id;
    END IF;
  ELSE
    -- Auto-assign based on category and availability
    SELECT a.id INTO assigned_advocate_id
    FROM public.advocates a
    JOIN public.profiles p ON a.profile_id = p.id
    WHERE p.is_verified = true
      AND category = ANY(a.specializations)
    ORDER BY a.rating DESC, a.total_reviews DESC
    LIMIT 1;
    
    IF assigned_advocate_id IS NOT NULL THEN
      UPDATE public.legal_questions
      SET advocate_id = assigned_advocate_id, status = 'assigned', updated_at = now()
      WHERE id = new_question_id;
    END IF;
  END IF;

  -- Return the created question with assignment info
  SELECT json_build_object(
    'success', true,
    'question_id', new_question_id,
    'assigned_advocate_id', assigned_advocate_id,
    'status', CASE WHEN assigned_advocate_id IS NOT NULL THEN 'assigned' ELSE 'pending' END,
    'message', CASE 
      WHEN assigned_advocate_id IS NOT NULL THEN 'تم إنشاء السؤال وتعيين محامي'
      ELSE 'تم إنشاء السؤال وسيتم تعيين محامي قريباً'
    END
  ) INTO result;

  RETURN result;

EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'success', false,
    'error', 'حدث خطأ في إنشاء السؤال: ' || SQLERRM
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Function to get user questions with enhanced details
CREATE OR REPLACE FUNCTION public.get_user_questions_enhanced(user_id UUID)
RETURNS TABLE(
  id UUID,
  title TEXT,
  description TEXT,
  category TEXT,
  priority TEXT,
  status TEXT,
  is_answered BOOLEAN,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  advocate_name TEXT,
  advocate_email TEXT,
  advocate_rating DECIMAL,
  response_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    lq.id,
    lq.title,
    lq.description,
    lq.category,
    lq.priority,
    lq.status,
    lq.is_answered,
    lq.created_at,
    lq.updated_at,
    ap.full_name as advocate_name,
    ap.email as advocate_email,
    a.rating as advocate_rating,
    COALESCE(r.response_count, 0) as response_count
  FROM public.legal_questions lq
  LEFT JOIN public.advocates a ON lq.advocate_id = a.id
  LEFT JOIN public.profiles ap ON a.profile_id = ap.id
  LEFT JOIN (
    SELECT question_id, COUNT(*) as response_count
    FROM public.responses
    GROUP BY question_id
  ) r ON lq.id = r.question_id
  WHERE lq.user_id = get_user_questions_enhanced.user_id
  ORDER BY lq.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Function to get available advocates for a category
CREATE OR REPLACE FUNCTION public.get_advocates_for_category(category_name TEXT)
RETURNS TABLE(
  profile_id UUID,
  full_name TEXT,
  email TEXT,
  specializations TEXT[],
  bio TEXT,
  hourly_rate DECIMAL,
  rating DECIMAL,
  total_reviews INTEGER,
  is_featured BOOLEAN,
  availability_status TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.profile_id,
    p.full_name,
    p.email,
    a.specializations,
    a.bio,
    a.hourly_rate,
    a.rating,
    a.total_reviews,
    a.is_featured,
    CASE 
      WHEN a.availability IS NOT NULL AND a.availability->>'status' = 'available' THEN 'متاح'
      WHEN a.availability IS NOT NULL AND a.availability->>'status' = 'busy' THEN 'مشغول'
      ELSE 'غير محدد'
    END as availability_status
  FROM public.advocates a
  JOIN public.profiles p ON a.profile_id = p.id
  WHERE p.is_verified = true
    AND (category_name = 'all' OR category_name = ANY(a.specializations))
  ORDER BY 
    a.is_featured DESC,
    a.rating DESC,
    a.total_reviews DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Function to get question statistics for user
CREATE OR REPLACE FUNCTION public.get_user_question_stats(user_id UUID)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'total_questions', (
      SELECT COUNT(*) FROM public.legal_questions WHERE user_id = get_user_question_stats.user_id
    ),
    'answered_questions', (
      SELECT COUNT(*) FROM public.legal_questions 
      WHERE user_id = get_user_question_stats.user_id AND is_answered = true
    ),
    'pending_questions', (
      SELECT COUNT(*) FROM public.legal_questions 
      WHERE user_id = get_user_question_stats.user_id AND is_answered = false
    ),
    'questions_by_category', (
      SELECT json_object_agg(category, count)
      FROM (
        SELECT category, COUNT(*) as count
        FROM public.legal_questions
        WHERE user_id = get_user_question_stats.user_id
        GROUP BY category
      ) category_counts
    ),
    'average_response_time_hours', (
      SELECT ROUND(AVG(EXTRACT(EPOCH FROM (r.created_at - lq.created_at)) / 3600), 2)
      FROM public.legal_questions lq
      JOIN public.responses r ON lq.id = r.question_id
      WHERE lq.user_id = get_user_question_stats.user_id
        AND r.created_at >= lq.created_at
    ),
    'last_question_date', (
      SELECT created_at FROM public.legal_questions 
      WHERE user_id = get_user_question_stats.user_id 
      ORDER BY created_at DESC 
      LIMIT 1
    )
  ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Function to auto-assign questions to advocates
CREATE OR REPLACE FUNCTION public.auto_assign_pending_questions()
RETURNS JSON AS $$
DECLARE
  assigned_count INTEGER := 0;
  question_record RECORD;
  best_advocate_id UUID;
BEGIN
  -- Loop through pending questions
  FOR question_record IN 
    SELECT id, category FROM public.legal_questions 
    WHERE status = 'pending' AND advocate_id IS NULL
  LOOP
    -- Find best advocate for this category
    SELECT a.id INTO best_advocate_id
    FROM public.advocates a
    JOIN public.profiles p ON a.profile_id = p.id
    WHERE p.is_verified = true
      AND question_record.category = ANY(a.specializations)
      AND (a.availability IS NULL OR a.availability->>'status' != 'unavailable')
    ORDER BY 
      a.rating DESC,
      a.total_reviews DESC,
      (
        SELECT COUNT(*) FROM public.legal_questions lq2 
        WHERE lq2.advocate_id = a.id AND lq2.status IN ('assigned', 'in_progress')
      ) ASC -- Prefer advocates with fewer active questions
    LIMIT 1;
    
    -- If no specialized advocate found, try general advocates
    IF best_advocate_id IS NULL THEN
      SELECT a.id INTO best_advocate_id
      FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.is_verified = true
        AND (a.availability IS NULL OR a.availability->>'status' != 'unavailable')
      ORDER BY 
        a.rating DESC,
        a.total_reviews DESC,
        (
          SELECT COUNT(*) FROM public.legal_questions lq2 
          WHERE lq2.advocate_id = a.id AND lq2.status IN ('assigned', 'in_progress')
        ) ASC
      LIMIT 1;
    END IF;
    
    -- Assign if advocate found
    IF best_advocate_id IS NOT NULL THEN
      UPDATE public.legal_questions
      SET advocate_id = best_advocate_id, status = 'assigned', updated_at = now()
      WHERE id = question_record.id;
      
      assigned_count := assigned_count + 1;
    END IF;
  END LOOP;
  
  RETURN json_build_object(
    'success', true,
    'assigned_count', assigned_count,
    'message', format('تم تعيين %s سؤال للمحامين', assigned_count)
  );
  
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'success', false,
    'error', 'خطأ في التعيين التلقائي: ' || SQLERRM
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Trigger to automatically update question status when response is added
CREATE OR REPLACE FUNCTION public.update_question_on_response()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the question status and mark as answered
  UPDATE public.legal_questions
  SET 
    is_answered = true,
    status = 'answered',
    updated_at = now()
  WHERE id = NEW.question_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS trigger_update_question_on_response ON public.responses;

-- Create the trigger
CREATE TRIGGER trigger_update_question_on_response
  AFTER INSERT ON public.responses
  FOR EACH ROW
  EXECUTE FUNCTION public.update_question_on_response();

-- 7. Grant permissions
GRANT EXECUTE ON FUNCTION public.create_question_enhanced TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_questions_enhanced TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_advocates_for_category TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_question_stats TO authenticated;
GRANT EXECUTE ON FUNCTION public.auto_assign_pending_questions TO service_role;

-- 8. Create indexes for better performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_legal_questions_user_created 
ON public.legal_questions(user_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_legal_questions_category_status 
ON public.legal_questions(category, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_legal_questions_advocate_status 
ON public.legal_questions(advocate_id, status) WHERE advocate_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_advocates_specializations_gin 
ON public.advocates USING gin(specializations);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_advocates_rating_reviews 
ON public.advocates(rating DESC, total_reviews DESC) WHERE rating > 0;

-- Success message
SELECT 'Enhanced questions database functions created successfully!' as status,
       'All functions, triggers, and indexes are now ready.' as details;
