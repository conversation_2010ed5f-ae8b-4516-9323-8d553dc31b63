-- CREATE INDEXES FOR <PERSON><PERSON>HANCED QUESTIONS PERFORMANCE
-- Execute these commands ONE BY ONE in Supabase SQL Editor
-- Do NOT run them all at once - execute each CREATE INDEX command separately

-- Index 1: Legal questions by user and creation date
CREATE INDEX IF NOT EXISTS idx_legal_questions_user_created 
ON public.legal_questions(user_id, created_at DESC);

-- Index 2: Legal questions by category and status  
CREATE INDEX IF NOT EXISTS idx_legal_questions_category_status 
ON public.legal_questions(category, status);

-- Index 3: Legal questions by advocate and status
CREATE INDEX IF NOT EXISTS idx_legal_questions_advocate_status 
ON public.legal_questions(advocate_id, status) WHERE advocate_id IS NOT NULL;

-- Index 4: Advocates specializations (GIN index for array operations)
CREATE INDEX IF NOT EXISTS idx_advocates_specializations_gin 
ON public.advocates USING gin(specializations);

-- Index 5: Advocates by rating and reviews
CREATE INDEX IF NOT EXISTS idx_advocates_rating_reviews 
ON public.advocates(rating DESC, total_reviews DESC) WHERE rating > 0;

-- Index 6: Responses by question ID
CREATE INDEX IF NOT EXISTS idx_responses_question_id 
ON public.responses(question_id);

-- Index 7: Responses by advocate ID and creation date
CREATE INDEX IF NOT EXISTS idx_responses_advocate_created 
ON public.responses(advocate_id, created_at DESC);

-- Index 8: Profiles by role and verification status
CREATE INDEX IF NOT EXISTS idx_profiles_role_verified 
ON public.profiles(role, is_verified) WHERE is_verified = true;

-- Success message
SELECT 'Database indexes created successfully!' as status,
       'Performance should be significantly improved.' as details;
