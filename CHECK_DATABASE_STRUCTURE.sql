-- CHECK DATABASE STRUCTURE FOR LAW APP MOROCCO
-- Execute this script in Supabase SQL Editor to check the current database structure

-- 1. Check if tables exist
SELECT 
  'Table Check' as check_type,
  table_name,
  CASE 
    WHEN table_name IS NOT NULL THEN 'EXISTS'
    ELSE 'MISSING'
  END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN ('profiles', 'advocates', 'legal_questions', 'legal_documents', 'subscription_plans', 'user_subscriptions')
ORDER BY table_name;

-- 2. Check profiles table structure
SELECT 
  'profiles' as table_name,
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- 3. Check advocates table structure
SELECT 
  'advocates' as table_name,
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'advocates' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- 4. Check if admin functions exist
SELECT 
  'Function Check' as check_type,
  routine_name as function_name,
  CASE 
    WHEN routine_name IS NOT NULL THEN 'EXISTS'
    ELSE 'MISSING'
  END as status
FROM information_schema.routines 
WHERE routine_schema = 'public' 
  AND routine_name IN ('admin_update_user', 'admin_create_user', 'admin_delete_user')
ORDER BY routine_name;

-- 5. Check RLS policies
SELECT 
  'RLS Policy Check' as check_type,
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('profiles', 'advocates')
ORDER BY tablename, policyname;

-- 6. Check if RLS is enabled
SELECT 
  'RLS Status' as check_type,
  schemaname,
  tablename,
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN ('profiles', 'advocates')
ORDER BY tablename;

-- 7. Sample data check
SELECT 
  'Data Check' as check_type,
  'profiles' as table_name,
  COUNT(*) as record_count
FROM public.profiles
UNION ALL
SELECT 
  'Data Check' as check_type,
  'advocates' as table_name,
  COUNT(*) as record_count
FROM public.advocates;

-- 8. Check for missing columns that might be needed
SELECT 
  'Missing Column Check' as check_type,
  'profiles.is_verified' as column_check,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name = 'profiles' 
        AND column_name = 'is_verified'
        AND table_schema = 'public'
    ) THEN 'EXISTS'
    ELSE 'MISSING - NEEDS TO BE ADDED'
  END as status
UNION ALL
SELECT 
  'Missing Column Check' as check_type,
  'advocates.is_verified' as column_check,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name = 'advocates' 
        AND column_name = 'is_verified'
        AND table_schema = 'public'
    ) THEN 'EXISTS'
    ELSE 'MISSING - NEEDS TO BE ADDED'
  END as status;
