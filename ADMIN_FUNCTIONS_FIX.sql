-- ADMIN FUNCTIONS FIX FOR LAW APP MOROCCO
-- Execute this script in Supabase SQL Editor to fix admin user management functions

-- 1. Drop existing functions if they exist (to avoid conflicts)
DROP FUNCTION IF EXISTS public.admin_update_user(UUID, TEXT, TEXT, TEXT, TEXT, BOOLEAN, TEXT[], TEXT, DECIMAL);
DROP FUNCTION IF EXISTS public.admin_delete_user(UUID);
DROP FUNCTION IF EXISTS public.admin_create_user(TEXT, TEXT, TEXT, TEXT, TEXT, BOOLEAN, TEXT[], TEXT, DECIMAL);

-- 2. Create the admin_update_user function
CREATE OR REPLACE FUNCTION public.admin_update_user(
  user_id UUID,
  user_name TEXT DEFAULT NULL,
  user_phone TEXT DEFAULT NULL,
  user_role TEXT DEFAULT NULL,
  subscription_tier TEXT DEFAULT NULL,
  is_verified BOOLEAN DEFAULT NULL,
  advocate_specializations TEXT[] DEFAULT NULL,
  advocate_bio TEXT DEFAULT NULL,
  advocate_hourly_rate DECIMAL DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  result JSON;
  advocate_record RECORD;
BEGIN
  -- Check if user exists
  IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = user_id) THEN
    RETURN json_build_object('success', false, 'message', 'User not found');
  END IF;

  -- Update profile table
  UPDATE public.profiles 
  SET 
    full_name = COALESCE(user_name, full_name),
    phone = COALESCE(user_phone, phone),
    role = COALESCE(user_role, role),
    subscription_tier = COALESCE(subscription_tier, subscription_tier),
    is_verified = COALESCE(is_verified, is_verified),
    updated_at = NOW()
  WHERE id = user_id;

  -- Handle advocate-specific updates
  IF user_role = 'advocate' OR (user_role IS NULL AND EXISTS (SELECT 1 FROM public.profiles WHERE id = user_id AND role = 'advocate')) THEN
    -- Check if advocate record exists
    SELECT * INTO advocate_record FROM public.advocates WHERE profile_id = user_id;
    
    IF advocate_record IS NULL THEN
      -- Create advocate record if it doesn't exist
      INSERT INTO public.advocates (
        profile_id,
        specializations,
        bio,
        hourly_rate,
        is_verified,
        created_at,
        updated_at
      ) VALUES (
        user_id,
        COALESCE(advocate_specializations, ARRAY[]::TEXT[]),
        COALESCE(advocate_bio, ''),
        COALESCE(advocate_hourly_rate, 500.00),
        COALESCE(is_verified, false),
        NOW(),
        NOW()
      );
    ELSE
      -- Update existing advocate record
      UPDATE public.advocates 
      SET 
        specializations = COALESCE(advocate_specializations, specializations),
        bio = COALESCE(advocate_bio, bio),
        hourly_rate = COALESCE(advocate_hourly_rate, hourly_rate),
        is_verified = COALESCE(is_verified, is_verified),
        updated_at = NOW()
      WHERE profile_id = user_id;
    END IF;
  END IF;

  result := json_build_object(
    'success', true,
    'user_id', user_id,
    'message', 'User updated successfully'
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'success', false,
    'error', SQLERRM,
    'message', 'Failed to update user'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Create the admin_delete_user function
CREATE OR REPLACE FUNCTION public.admin_delete_user(user_id UUID)
RETURNS JSON AS $$
DECLARE
  user_email TEXT;
  result JSON;
BEGIN
  -- Get user email for logging
  SELECT email INTO user_email FROM public.profiles WHERE id = user_id;
  
  IF user_email IS NULL THEN
    RETURN json_build_object('success', false, 'message', 'User not found');
  END IF;
  
  -- Delete advocate record first (if exists)
  DELETE FROM public.advocates WHERE profile_id = user_id;
  
  -- Delete user responses
  DELETE FROM public.responses WHERE user_id = user_id;
  
  -- Update questions to remove advocate assignment
  UPDATE public.legal_questions SET advocate_id = NULL WHERE advocate_id = user_id;
  
  -- Delete user questions
  DELETE FROM public.legal_questions WHERE user_id = user_id;
  
  -- Delete user documents
  DELETE FROM public.legal_documents WHERE user_id = user_id;
  
  -- Finally delete the profile
  DELETE FROM public.profiles WHERE id = user_id;
  
  result := json_build_object(
    'success', true,
    'user_id', user_id,
    'email', user_email,
    'message', 'User deleted successfully'
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'success', false,
    'error', SQLERRM,
    'message', 'Failed to delete user'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Create the admin_create_user function (if missing)
CREATE OR REPLACE FUNCTION public.admin_create_user(
  user_email TEXT,
  user_name TEXT,
  user_role TEXT DEFAULT 'user',
  user_phone TEXT DEFAULT NULL,
  subscription_tier TEXT DEFAULT 'free',
  is_verified BOOLEAN DEFAULT TRUE,
  advocate_specializations TEXT[] DEFAULT NULL,
  advocate_bio TEXT DEFAULT NULL,
  advocate_hourly_rate DECIMAL DEFAULT 500.00
)
RETURNS JSON AS $$
DECLARE
  new_user_id UUID;
  result JSON;
BEGIN
  -- Check if user already exists
  IF EXISTS (SELECT 1 FROM public.profiles WHERE email = user_email) THEN
    RETURN json_build_object('success', false, 'message', 'User with this email already exists');
  END IF;

  -- Generate new UUID
  new_user_id := gen_random_uuid();
  
  -- Insert into profiles table
  INSERT INTO public.profiles (
    id,
    email,
    full_name,
    phone,
    role,
    subscription_tier,
    is_verified,
    created_at,
    updated_at
  ) VALUES (
    new_user_id,
    user_email,
    user_name,
    user_phone,
    user_role,
    subscription_tier,
    is_verified,
    NOW(),
    NOW()
  );
  
  -- If role is advocate, create advocate record
  IF user_role = 'advocate' THEN
    INSERT INTO public.advocates (
      profile_id,
      specializations,
      bio,
      hourly_rate,
      is_verified,
      created_at,
      updated_at
    ) VALUES (
      new_user_id,
      COALESCE(advocate_specializations, ARRAY[]::TEXT[]),
      COALESCE(advocate_bio, ''),
      advocate_hourly_rate,
      is_verified,
      NOW(),
      NOW()
    );
  END IF;
  
  result := json_build_object(
    'success', true,
    'user_id', new_user_id,
    'email', user_email,
    'message', 'User created successfully'
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'success', false,
    'error', SQLERRM,
    'message', 'Failed to create user'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Grant permissions to authenticated users
GRANT EXECUTE ON FUNCTION public.admin_create_user TO authenticated;
GRANT EXECUTE ON FUNCTION public.admin_update_user TO authenticated;
GRANT EXECUTE ON FUNCTION public.admin_delete_user TO authenticated;

-- 6. Create RLS policies for admin functions (if not exists)
-- Enable RLS on profiles table
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Create policy for admin access
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'profiles' 
    AND policyname = 'Admin can manage all profiles'
  ) THEN
    CREATE POLICY "Admin can manage all profiles" ON public.profiles
      FOR ALL USING (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND role = 'admin'
        )
      );
  END IF;
END $$;

-- Create policy for users to view their own profile
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'profiles' 
    AND policyname = 'Users can view own profile'
  ) THEN
    CREATE POLICY "Users can view own profile" ON public.profiles
      FOR SELECT USING (id = auth.uid());
  END IF;
END $$;

-- Create policy for users to update their own profile
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'profiles' 
    AND policyname = 'Users can update own profile'
  ) THEN
    CREATE POLICY "Users can update own profile" ON public.profiles
      FOR UPDATE USING (id = auth.uid());
  END IF;
END $$;
