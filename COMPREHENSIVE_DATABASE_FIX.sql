-- COMPREHENSIVE DATABASE FIX FOR LAW APP MOROCCO
-- This script fixes all authentication, RLS, and account management issues
-- Execute this in your Supabase SQL Editor

-- 1. First, disable <PERSON><PERSON> temporarily to avoid recursion issues
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.advocates DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.legal_questions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.legal_documents DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.usage_tracking DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscribers DISABLE ROW LEVEL SECURITY;

-- 2. Drop all existing problematic policies
DROP POLICY IF EXISTS "profiles_select_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_admin_all" ON public.profiles;
DROP POLICY IF EXISTS "profiles_permissive" ON public.profiles;

DROP POLICY IF EXISTS "advocates_select_all" ON public.advocates;
DROP POLICY IF EXISTS "advocates_insert_own" ON public.advocates;
DROP POLICY IF EXISTS "advocates_update_own" ON public.advocates;
DROP POLICY IF EXISTS "advocates_delete_own" ON public.advocates;
DROP POLICY IF EXISTS "advocates_admin_all" ON public.advocates;
DROP POLICY IF EXISTS "advocates_permissive" ON public.advocates;

DROP POLICY IF EXISTS "questions_select_own" ON public.legal_questions;
DROP POLICY IF EXISTS "questions_insert_own" ON public.legal_questions;
DROP POLICY IF EXISTS "questions_update_own" ON public.legal_questions;
DROP POLICY IF EXISTS "questions_admin_all" ON public.legal_questions;
DROP POLICY IF EXISTS "questions_advocates_view" ON public.legal_questions;
DROP POLICY IF EXISTS "questions_advocates_update" ON public.legal_questions;
DROP POLICY IF EXISTS "questions_permissive" ON public.legal_questions;

DROP POLICY IF EXISTS "responses_select_all" ON public.responses;
DROP POLICY IF EXISTS "responses_insert_all" ON public.responses;
DROP POLICY IF EXISTS "responses_update_all" ON public.responses;
DROP POLICY IF EXISTS "responses_admin_all" ON public.responses;
DROP POLICY IF EXISTS "responses_permissive" ON public.responses;

-- 3. Create simple, non-recursive RLS policies

-- Enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.advocates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.legal_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.legal_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.usage_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscribers ENABLE ROW LEVEL SECURITY;

-- Profiles policies (simple and safe)
CREATE POLICY "profiles_own_access" ON public.profiles
  FOR ALL USING (auth.uid() = id);

CREATE POLICY "profiles_public_read" ON public.profiles
  FOR SELECT USING (true);

-- Advocates policies (simple and safe)
CREATE POLICY "advocates_own_access" ON public.advocates
  FOR ALL USING (auth.uid() = profile_id);

CREATE POLICY "advocates_public_read" ON public.advocates
  FOR SELECT USING (true);

-- Legal questions policies (simple and safe)
CREATE POLICY "questions_own_access" ON public.legal_questions
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "questions_public_read" ON public.legal_questions
  FOR SELECT USING (true);

-- Responses policies (simple and safe)
CREATE POLICY "responses_public_access" ON public.responses
  FOR ALL USING (true);

-- Other tables policies (simple and safe)
CREATE POLICY "documents_own_access" ON public.legal_documents
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "subscriptions_own_access" ON public.subscriptions
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "usage_own_access" ON public.usage_tracking
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "subscribers_public_access" ON public.subscribers
  FOR ALL USING (true);

-- 4. Create helper functions for account management

-- Function to create admin account
CREATE OR REPLACE FUNCTION public.create_admin_account(
  admin_email TEXT,
  admin_name TEXT DEFAULT NULL
)
RETURNS TEXT AS $$
DECLARE
  user_exists BOOLEAN;
  result_message TEXT;
BEGIN
  -- Check if user exists in auth.users
  SELECT EXISTS(
    SELECT 1 FROM auth.users WHERE email = admin_email
  ) INTO user_exists;
  
  IF user_exists THEN
    -- Update existing profile to admin
    UPDATE public.profiles 
    SET 
      role = 'admin', 
      is_verified = true,
      full_name = COALESCE(admin_name, full_name, admin_email),
      updated_at = now()
    WHERE email = admin_email;
    
    result_message := 'Admin account created/updated successfully for: ' || admin_email;
  ELSE
    result_message := 'User not found. Please create account first at: ' || admin_email;
  END IF;
  
  RETURN result_message;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create advocate account
CREATE OR REPLACE FUNCTION public.create_advocate_account(
  advocate_email TEXT,
  advocate_name TEXT DEFAULT NULL,
  specializations TEXT[] DEFAULT ARRAY[]::TEXT[],
  bio TEXT DEFAULT '',
  hourly_rate DECIMAL DEFAULT 500.00,
  auto_verify BOOLEAN DEFAULT FALSE
)
RETURNS TEXT AS $$
DECLARE
  user_exists BOOLEAN;
  profile_id UUID;
  advocate_exists BOOLEAN;
  result_message TEXT;
BEGIN
  -- Check if user exists in auth.users
  SELECT EXISTS(
    SELECT 1 FROM auth.users WHERE email = advocate_email
  ) INTO user_exists;
  
  IF user_exists THEN
    -- Get profile ID
    SELECT id INTO profile_id FROM public.profiles WHERE email = advocate_email;
    
    -- Update profile to advocate
    UPDATE public.profiles 
    SET 
      role = 'advocate', 
      is_verified = auto_verify,
      full_name = COALESCE(advocate_name, full_name, advocate_email),
      updated_at = now()
    WHERE email = advocate_email;
    
    -- Check if advocate profile exists
    SELECT EXISTS(
      SELECT 1 FROM public.advocates WHERE profile_id = profile_id
    ) INTO advocate_exists;
    
    -- Create or update advocate profile
    IF NOT advocate_exists THEN
      INSERT INTO public.advocates (
        profile_id,
        specializations,
        bio,
        hourly_rate,
        is_featured,
        rating,
        total_reviews,
        availability
      ) VALUES (
        profile_id,
        specializations,
        bio,
        hourly_rate,
        false,
        0.0,
        0,
        '{}'::jsonb
      );
    ELSE
      UPDATE public.advocates 
      SET 
        specializations = create_advocate_account.specializations,
        bio = create_advocate_account.bio,
        hourly_rate = create_advocate_account.hourly_rate,
        updated_at = now()
      WHERE profile_id = create_advocate_account.profile_id;
    END IF;
    
    result_message := 'Advocate account created/updated successfully for: ' || advocate_email;
    
    IF auto_verify THEN
      result_message := result_message || ' (Auto-verified)';
    ELSE
      result_message := result_message || ' (Pending verification)';
    END IF;
  ELSE
    result_message := 'User not found. Please create account first at: ' || advocate_email;
  END IF;
  
  RETURN result_message;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to verify advocate
CREATE OR REPLACE FUNCTION public.verify_advocate(
  advocate_email TEXT
)
RETURNS TEXT AS $$
BEGIN
  UPDATE public.profiles 
  SET is_verified = true, updated_at = now()
  WHERE email = advocate_email AND role = 'advocate';
  
  IF FOUND THEN
    RETURN 'Advocate verified successfully: ' || advocate_email;
  ELSE
    RETURN 'Advocate not found or not an advocate: ' || advocate_email;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user statistics
CREATE OR REPLACE FUNCTION public.get_user_statistics()
RETURNS TABLE(
  total_users BIGINT,
  total_advocates BIGINT,
  verified_advocates BIGINT,
  total_questions BIGINT,
  total_responses BIGINT,
  total_documents BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    (SELECT COUNT(*) FROM public.profiles) as total_users,
    (SELECT COUNT(*) FROM public.advocates) as total_advocates,
    (SELECT COUNT(*) FROM public.profiles WHERE role = 'advocate' AND is_verified = true) as verified_advocates,
    (SELECT COUNT(*) FROM public.legal_questions) as total_questions,
    (SELECT COUNT(*) FROM public.responses) as total_responses,
    (SELECT COUNT(*) FROM public.legal_documents) as total_documents;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.create_admin_account TO authenticated;
GRANT EXECUTE ON FUNCTION public.create_advocate_account TO authenticated;
GRANT EXECUTE ON FUNCTION public.verify_advocate TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_statistics TO authenticated;

-- 6. Create sample admin account (replace with your email)
-- SELECT public.create_admin_account('<EMAIL>', 'Administrator');

-- 7. Create sample advocate accounts (replace with real emails)
-- SELECT public.create_advocate_account('<EMAIL>', 'Ahmed El Mansouri', ARRAY['family', 'civil'], 'Experienced family law attorney', 600.00, true);
-- SELECT public.create_advocate_account('<EMAIL>', 'Fatima Benali', ARRAY['commercial', 'labor'], 'Commercial law specialist', 700.00, true);

-- 8. Display current statistics
SELECT 'Database setup completed successfully!' as status;
SELECT * FROM public.get_user_statistics();

-- 9. Show all users and their roles
SELECT 
  email,
  full_name,
  role,
  is_verified,
  created_at
FROM public.profiles 
ORDER BY created_at DESC;

-- 10. Show all advocates
SELECT 
  p.email,
  p.full_name,
  p.is_verified,
  a.specializations,
  a.hourly_rate,
  a.rating
FROM public.profiles p
JOIN public.advocates a ON p.id = a.profile_id
ORDER BY p.created_at DESC;
