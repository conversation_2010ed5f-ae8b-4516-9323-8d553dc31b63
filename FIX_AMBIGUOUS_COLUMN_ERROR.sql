-- FIX AMBIGUOUS COLUMN ERROR FOR ADMIN FUNCTIONS
-- Execute this script in Supabase SQL Editor to fix the column ambiguity error

-- 1. Drop the existing function with ambiguous column references
DROP FUNCTION IF EXISTS public.admin_update_user(UUID, TEXT, TEXT, TEXT, TEXT, BOOLEAN, TEXT[], TEXT, DECIMAL);

-- 2. Create the corrected admin_update_user function with explicit column references
CREATE OR REPLACE FUNCTION public.admin_update_user(
  user_id UUID,
  user_name TEXT DEFAULT NULL,
  user_phone TEXT DEFAULT NULL,
  user_role TEXT DEFAULT NULL,
  new_subscription_tier TEXT DEFAULT NULL,  -- Renamed parameter to avoid ambiguity
  is_verified BOOLEAN DEFAULT NULL,
  advocate_specializations TEXT[] DEFAULT NULL,
  advocate_bio TEXT DEFAULT NULL,
  advocate_hourly_rate DECIMAL DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  result JSON;
  current_profile RECORD;
BEGIN
  -- Check if user exists and get current values
  SELECT * INTO current_profile FROM public.profiles WHERE id = user_id;
  
  IF NOT FOUND THEN
    RETURN json_build_object('success', false, 'message', 'User not found');
  END IF;

  -- Update profile table with explicit column references and no ambiguity
  UPDATE public.profiles 
  SET 
    full_name = CASE 
      WHEN user_name IS NOT NULL THEN user_name 
      ELSE public.profiles.full_name 
    END,
    phone = CASE 
      WHEN user_phone IS NOT NULL THEN user_phone 
      ELSE public.profiles.phone 
    END,
    role = CASE 
      WHEN user_role IS NOT NULL THEN user_role::user_role 
      ELSE public.profiles.role 
    END,
    subscription_tier = CASE 
      WHEN new_subscription_tier IS NOT NULL THEN new_subscription_tier::subscription_tier 
      ELSE public.profiles.subscription_tier 
    END,
    is_verified = CASE 
      WHEN is_verified IS NOT NULL THEN is_verified 
      ELSE public.profiles.is_verified 
    END,
    updated_at = NOW()
  WHERE public.profiles.id = user_id;

  -- Handle advocate-specific updates
  IF user_role = 'advocate' OR advocate_specializations IS NOT NULL OR advocate_bio IS NOT NULL OR advocate_hourly_rate IS NOT NULL THEN
    -- Check if advocate record exists
    IF EXISTS (SELECT 1 FROM public.advocates WHERE profile_id = user_id) THEN
      -- Update existing advocate record
      UPDATE public.advocates 
      SET 
        specializations = CASE 
          WHEN advocate_specializations IS NOT NULL THEN advocate_specializations 
          ELSE public.advocates.specializations 
        END,
        bio = CASE 
          WHEN advocate_bio IS NOT NULL THEN advocate_bio 
          ELSE public.advocates.bio 
        END,
        hourly_rate = CASE 
          WHEN advocate_hourly_rate IS NOT NULL THEN advocate_hourly_rate 
          ELSE public.advocates.hourly_rate 
        END,
        is_verified = CASE 
          WHEN is_verified IS NOT NULL THEN is_verified 
          ELSE public.advocates.is_verified 
        END,
        updated_at = NOW()
      WHERE public.advocates.profile_id = user_id;
    ELSIF user_role = 'advocate' THEN
      -- Create new advocate record
      INSERT INTO public.advocates (
        profile_id,
        specializations,
        bio,
        hourly_rate,
        is_verified,
        created_at,
        updated_at
      ) VALUES (
        user_id,
        COALESCE(advocate_specializations, ARRAY[]::TEXT[]),
        COALESCE(advocate_bio, ''),
        COALESCE(advocate_hourly_rate, 500.00),
        COALESCE(is_verified, false),
        NOW(),
        NOW()
      );
    END IF;
  END IF;

  result := json_build_object(
    'success', true,
    'user_id', user_id,
    'message', 'User updated successfully'
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'success', false,
    'error', SQLERRM,
    'message', 'Failed to update user: ' || SQLERRM
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Grant permissions
GRANT EXECUTE ON FUNCTION public.admin_update_user TO authenticated;

-- 4. Test the function
SELECT 'Function updated successfully! Ambiguous column error fixed.' as status;

-- 5. Show table structures to verify column names
SELECT 
  'Column Check' as check_type,
  table_name,
  column_name,
  data_type
FROM information_schema.columns 
WHERE table_name IN ('profiles', 'advocates') 
  AND table_schema = 'public'
  AND column_name IN ('subscription_tier', 'is_verified', 'role')
ORDER BY table_name, column_name;
