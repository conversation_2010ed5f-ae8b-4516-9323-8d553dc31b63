import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { User } from '@supabase/supabase-js';

interface Profile {
  id: string;
  email: string;
  full_name?: string;
  phone?: string;
  role: 'admin' | 'user' | 'advocate';
  subscription_tier: 'free' | 'pro_user' | 'pro_advocate';
  subscription_end?: string;
  trial_end?: string;
  stripe_customer_id?: string;
  is_verified?: boolean;
  created_at?: string;
  updated_at?: string;
}

interface AuthContextType {
  user: User | null;
  profile: Profile | null;
  loading: boolean;
  initialized: boolean;
  signUp: (email: string, password: string, fullName: string, role?: 'user' | 'advocate') => Promise<any>;
  signIn: (email: string, password: string) => Promise<any>;
  signOut: () => Promise<void>;
  signInWithGoogle: () => Promise<any>;
  refreshProfile: () => Promise<void>;
  checkSubscription: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      try {
        // Get initial session
        const { data: { session }, error } = await supabase.auth.getSession();

        if (!mounted) return;

        console.log('Initial session:', session);

        if (error) {
          console.error('Session error:', error);
          setLoading(false);
          setInitialized(true);
          return;
        }

        setUser(session?.user ?? null);

        if (session?.user) {
          await fetchProfile(session.user.id);
        } else {
          setLoading(false);
          setInitialized(true);
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        if (mounted) {
          setLoading(false);
          setInitialized(true);
        }
      }
    };

    // Initialize auth
    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!mounted) return;

      console.log('Auth state changed:', event, session);
      setUser(session?.user ?? null);

      if (session?.user) {
        await fetchProfile(session.user.id);
      } else {
        setProfile(null);
        setLoading(false);
        setInitialized(true);
      }
    });

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  const fetchProfile = async (userId: string) => {
    try {
      console.log('Fetching profile for user:', userId);

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        
        // If profile doesn't exist, try to create one
        if (error.code === 'PGRST116') {
          console.log('Profile not found, attempting to create...');
          await createProfileFromAuth(userId);
        } else {
          // Set a basic profile to prevent blocking
          setProfile({
            id: userId,
            email: '',
            role: 'user',
            subscription_tier: 'free',
            is_verified: true,
          });
        }
      } else {
        console.log('Profile fetched successfully:', data);
        setProfile(data);
      }
    } catch (error) {
      console.error('Error in fetchProfile:', error);
      // Set a basic profile to prevent blocking
      setProfile({
        id: userId,
        email: '',
        role: 'user',
        subscription_tier: 'free',
        is_verified: true,
      });
    } finally {
      setLoading(false);
      setInitialized(true);
    }
  };

  const createProfileFromAuth = async (userId: string) => {
    try {
      const { data: userData } = await supabase.auth.getUser();
      
      if (userData.user) {
        const role = userData.user.user_metadata?.role || 'user';
        
        const { data: newProfile, error: createError } = await supabase
          .from('profiles')
          .insert({
            id: userId,
            email: userData.user.email || '',
            full_name: userData.user.user_metadata?.full_name || userData.user.email || '',
            role: role,
            subscription_tier: 'free',
            is_verified: role === 'user' // Auto-verify users, not advocates
          })
          .select()
          .single();

        if (createError) {
          console.error('Error creating profile:', createError);
          // Set basic profile anyway
          setProfile({
            id: userId,
            email: userData.user.email || '',
            role: role,
            subscription_tier: 'free',
            is_verified: role === 'user',
          });
        } else {
          console.log('Profile created successfully:', newProfile);
          setProfile(newProfile);
          
          // If role is advocate, create advocate profile
          if (role === 'advocate') {
            await createAdvocateProfile(userId);
          }
        }
      }
    } catch (error) {
      console.error('Error creating profile from auth:', error);
    }
  };

  const createAdvocateProfile = async (userId: string) => {
    try {
      const { error } = await supabase
        .from('advocates')
        .insert({
          profile_id: userId,
          specializations: [],
          bio: '',
          hourly_rate: 0,
          is_featured: false,
          rating: 0,
          total_reviews: 0,
          availability: {}
        });

      if (error) {
        console.error('Error creating advocate profile:', error);
      } else {
        console.log('Advocate profile created successfully');
      }
    } catch (error) {
      console.error('Error creating advocate profile:', error);
    }
  };

  const refreshProfile = async () => {
    if (user) {
      await fetchProfile(user.id);
    }
  };

  const checkSubscription = async () => {
    try {
      console.log('Checking subscription status...');
      // Implementation for subscription check
      console.log('Subscription check completed');
    } catch (error) {
      console.error('Error checking subscription:', error);
    }
  };

  const signUp = async (email: string, password: string, fullName: string, role: 'user' | 'advocate' = 'user') => {
    console.log('Signing up user:', email, 'with role:', role);

    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            role: role,
          },
          emailRedirectTo: `${window.location.origin}/`,
        },
      });

      if (error) {
        console.error('Signup error:', error);
        return { data, error };
      }

      console.log('Signup successful:', data);
      return { data, error: null };
    } catch (error) {
      console.error('Signup error:', error);
      return { data: null, error };
    }
  };

  const signIn = async (email: string, password: string) => {
    console.log('Signing in user:', email);
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) {
      console.error('Signin error:', error);
    } else {
      console.log('Signin successful:', data);
    }
    
    return { data, error };
  };

  const signOut = async () => {
    console.log('Signing out user');
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Signout error:', error);
        throw error;
      }
      // Clear local state
      setUser(null);
      setProfile(null);
    } catch (error) {
      console.error('Error during signout:', error);
      throw error;
    }
  };

  const signInWithGoogle = async () => {
    console.log('Signing in with Google');
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/`,
      },
    });
    
    if (error) {
      console.error('Google signin error:', error);
    } else {
      console.log('Google signin initiated:', data);
    }
    
    return { data, error };
  };

  const value = {
    user,
    profile,
    loading,
    initialized,
    signUp,
    signIn,
    signOut,
    signInWithGoogle,
    refreshProfile,
    checkSubscription,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
