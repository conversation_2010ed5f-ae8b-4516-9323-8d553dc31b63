// Subscription-related TypeScript types

export interface SubscriptionPlan {
  id: string;
  name: string;
  name_ar: string;
  description: string;
  description_ar: string;
  price: number;
  currency: string;
  billing_period: 'monthly' | 'yearly';
  features: string[];
  features_ar: string[];
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface UserSubscription {
  id: string;
  user_id: string;
  plan_id: string;
  status: 'active' | 'inactive' | 'cancelled' | 'expired';
  start_date: string;
  end_date?: string;
  auto_renew: boolean;
  payment_method?: string;
  last_payment_date?: string;
  next_payment_date?: string;
  created_at: string;
  updated_at: string;
}

export interface SubscriptionPreferences {
  id: string;
  user_id: string;
  email_notifications: boolean;
  sms_notifications: boolean;
  in_app_notifications: boolean;
  legal_updates: boolean;
  question_responses: boolean;
  system_announcements: boolean;
  marketing_emails: boolean;
  weekly_digest: boolean;
  phone_number?: string;
  preferred_language: string;
  created_at: string;
  updated_at: string;
}

export interface SubscriptionAnalytics {
  id: string;
  date: string;
  total_subscribers: number;
  new_subscribers: number;
  cancelled_subscribers: number;
  active_subscribers: number;
  revenue: number;
  plan_breakdown: Record<string, number>;
  created_at: string;
}

export interface SubscriptionDetails {
  subscription: UserSubscription | null;
  plan: SubscriptionPlan | null;
  preferences: SubscriptionPreferences | null;
}

export interface SubscriptionStats {
  totalSubscribers: number;
  totalUsers: number;
  conversionRate: string;
  planBreakdown: Record<string, number>;
  totalRevenue: number;
}

// Feature access types
export type SubscriptionFeature = 
  | 'basic_questions'
  | 'unlimited_questions'
  | 'basic_documents'
  | 'unlimited_documents'
  | 'priority_support'
  | 'lawyer_access'
  | 'advanced_analytics'
  | 'lawyer_profile'
  | 'specialized_tools'
  | 'priority_profile'
  | 'trial';

// Plan types
export type PlanType = 'free' | 'premium' | 'professional';

// Subscription status types
export type SubscriptionStatus = 'active' | 'inactive' | 'cancelled' | 'expired';

// Billing period types
export type BillingPeriod = 'monthly' | 'yearly';

// Payment method types
export type PaymentMethod = 'free' | 'credit_card' | 'bank_transfer' | 'mobile_payment';

// Notification preference types
export interface NotificationPreferences {
  email_notifications: boolean;
  sms_notifications: boolean;
  in_app_notifications: boolean;
  legal_updates: boolean;
  question_responses: boolean;
  system_announcements: boolean;
  marketing_emails: boolean;
  weekly_digest: boolean;
}

// Usage quota types
export interface UsageQuota {
  feature: string;
  used: number;
  limit: number;
  reset_date: string;
}

// Subscription limits for different plans
export interface PlanLimits {
  questions_per_month: number | null; // null means unlimited
  documents_per_month: number | null;
  lawyer_consultations_per_month: number | null;
  storage_mb: number | null;
  support_priority: 'low' | 'medium' | 'high';
  features: SubscriptionFeature[];
}

// Default plan limits
export const PLAN_LIMITS: Record<PlanType, PlanLimits> = {
  free: {
    questions_per_month: 1,
    documents_per_month: 3,
    lawyer_consultations_per_month: 0,
    storage_mb: 100,
    support_priority: 'low',
    features: ['basic_questions', 'basic_documents', 'trial']
  },
  premium: {
    questions_per_month: null, // unlimited
    documents_per_month: null, // unlimited
    lawyer_consultations_per_month: 10,
    storage_mb: 1000,
    support_priority: 'medium',
    features: [
      'basic_questions',
      'unlimited_questions',
      'basic_documents',
      'unlimited_documents',
      'priority_support',
      'lawyer_access'
    ]
  },
  professional: {
    questions_per_month: null, // unlimited
    documents_per_month: null, // unlimited
    lawyer_consultations_per_month: null, // unlimited
    storage_mb: null, // unlimited
    support_priority: 'high',
    features: [
      'basic_questions',
      'unlimited_questions',
      'basic_documents',
      'unlimited_documents',
      'priority_support',
      'lawyer_access',
      'advanced_analytics',
      'lawyer_profile',
      'specialized_tools',
      'priority_profile'
    ]
  }
};

// Subscription event types for analytics
export interface SubscriptionEvent {
  id: string;
  user_id: string;
  event_type: 'subscription_created' | 'subscription_cancelled' | 'subscription_renewed' | 'plan_changed';
  old_plan_id?: string;
  new_plan_id?: string;
  metadata?: Record<string, any>;
  created_at: string;
}

// API response types
export interface SubscriptionApiResponse<T = any> {
  data: T | null;
  error: any;
}

// Subscription form data
export interface SubscriptionFormData {
  plan_id: string;
  payment_method: PaymentMethod;
  billing_period: BillingPeriod;
  auto_renew: boolean;
}

// Subscription preferences form data
export interface PreferencesFormData extends Partial<NotificationPreferences> {
  phone_number?: string;
  preferred_language: string;
}
