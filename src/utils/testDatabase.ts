// Test script to verify database functionality
// Run this in browser console or create a test page

import { supabase } from '@/integrations/supabase/client';
import { QuestionsService } from '@/services/questionsService';
import { AuthService } from '@/services/authService';

export class DatabaseTester {
  static async testDatabaseConnection() {
    console.log('🔍 Testing database connection...');
    
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('count')
        .limit(1);
      
      if (error) {
        console.error('❌ Database connection failed:', error);
        return false;
      }
      
      console.log('✅ Database connection successful');
      return true;
    } catch (error) {
      console.error('❌ Database connection error:', error);
      return false;
    }
  }

  static async testResponsesTable() {
    console.log('🔍 Testing responses table...');
    
    try {
      const { data, error } = await supabase
        .from('responses')
        .select('*')
        .limit(1);
      
      if (error) {
        console.error('❌ Responses table test failed:', error);
        return false;
      }
      
      console.log('✅ Responses table accessible');
      return true;
    } catch (error) {
      console.error('❌ Responses table error:', error);
      return false;
    }
  }

  static async testAdvocateAccess(advocateProfileId: string) {
    console.log('🔍 Testing advocate access...');
    
    try {
      // Test getting advocate questions
      const { data: questions, error: questionsError } = await QuestionsService.getAdvocateQuestions(advocateProfileId);
      
      if (questionsError) {
        console.error('❌ Advocate questions access failed:', questionsError);
        return false;
      }
      
      console.log('✅ Advocate can access questions:', questions?.length || 0);
      
      // Test getting advocate profile
      const advocateProfile = await AuthService.getAdvocateProfile(advocateProfileId);
      
      if (!advocateProfile) {
        console.error('❌ Advocate profile access failed');
        return false;
      }
      
      console.log('✅ Advocate profile accessible');
      return true;
    } catch (error) {
      console.error('❌ Advocate access error:', error);
      return false;
    }
  }

  static async testQuestionCreation(userId: string) {
    console.log('🔍 Testing question creation...');
    
    try {
      const { data, error } = await QuestionsService.createQuestion(
        userId,
        'Test Question',
        'This is a test question for database verification',
        'general'
      );
      
      if (error) {
        console.error('❌ Question creation failed:', error);
        return false;
      }
      
      console.log('✅ Question created successfully:', data?.id);
      
      // Clean up - delete the test question
      if (data?.id) {
        await QuestionsService.deleteQuestion(data.id, userId);
        console.log('✅ Test question cleaned up');
      }
      
      return true;
    } catch (error) {
      console.error('❌ Question creation error:', error);
      return false;
    }
  }

  static async testResponseCreation(questionId: string, advocateProfileId: string) {
    console.log('🔍 Testing response creation...');
    
    try {
      const { data, error } = await QuestionsService.createResponse(
        questionId,
        'This is a test response from an advocate',
        advocateProfileId
      );
      
      if (error) {
        console.error('❌ Response creation failed:', error);
        return false;
      }
      
      console.log('✅ Response created successfully:', data?.id);
      return true;
    } catch (error) {
      console.error('❌ Response creation error:', error);
      return false;
    }
  }

  static async testAdminAccess() {
    console.log('🔍 Testing admin access...');
    
    try {
      const stats = await AuthService.getAppStatistics();
      
      console.log('✅ Admin statistics accessible:', {
        totalUsers: stats.totalUsers,
        totalAdvocates: stats.totalAdvocates,
        totalQuestions: stats.totalQuestions
      });
      
      return true;
    } catch (error) {
      console.error('❌ Admin access error:', error);
      return false;
    }
  }

  static async runAllTests() {
    console.log('🚀 Starting comprehensive database tests...\n');
    
    const results = {
      connection: await this.testDatabaseConnection(),
      responsesTable: await this.testResponsesTable(),
      adminAccess: await this.testAdminAccess(),
    };
    
    console.log('\n📊 Test Results Summary:');
    console.log('Connection:', results.connection ? '✅' : '❌');
    console.log('Responses Table:', results.responsesTable ? '✅' : '❌');
    console.log('Admin Access:', results.adminAccess ? '✅' : '❌');
    
    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
      console.log('\n🎉 All tests passed! Database is ready for production.');
    } else {
      console.log('\n⚠️ Some tests failed. Please check the database setup.');
    }
    
    return results;
  }

  // Helper method to test with specific user IDs
  static async testWithUserIds(userId: string, advocateProfileId: string) {
    console.log('🚀 Starting user-specific tests...\n');
    
    const results = {
      ...await this.runAllTests(),
      advocateAccess: await this.testAdvocateAccess(advocateProfileId),
      questionCreation: await this.testQuestionCreation(userId),
    };
    
    console.log('\n📊 Extended Test Results:');
    Object.entries(results).forEach(([test, passed]) => {
      console.log(`${test}:`, passed ? '✅' : '❌');
    });
    
    return results;
  }
}

// Usage examples:
// DatabaseTester.runAllTests();
// DatabaseTester.testWithUserIds('user-uuid', 'advocate-profile-uuid');

export default DatabaseTester;
