// Test utility for admin functions
import { supabase } from '@/integrations/supabase/client';

export const testAdminFunctions = async () => {
  console.log('🧪 Testing Admin Functions...');
  
  try {
    // Test 1: Check if admin_update_user function exists
    console.log('1. Testing admin_update_user function...');
    const testUserId = '00000000-0000-0000-0000-000000000000'; // Dummy UUID for testing
    
    const { data: updateResult, error: updateError } = await supabase.rpc('admin_update_user', {
      user_id: testUserId,
      user_name: 'Test User',
      user_phone: '+************',
      user_role: 'user',
      subscription_tier: 'free',
      is_verified: true,
      advocate_specializations: null,
      advocate_bio: null,
      advocate_hourly_rate: null
    });
    
    if (updateError) {
      if (updateError.message?.includes('function') && updateError.message?.includes('does not exist')) {
        console.error('❌ admin_update_user function does not exist');
        return { success: false, error: 'admin_update_user function missing' };
      } else if (updateError.message?.includes('violates foreign key constraint') || 
                 updateError.message?.includes('does not exist') ||
                 updateError.code === 'PGRST116') {
        console.log('✅ admin_update_user function exists (expected error for dummy UUID)');
      } else {
        console.error('❌ Unexpected error in admin_update_user:', updateError);
        return { success: false, error: updateError.message };
      }
    } else {
      console.log('✅ admin_update_user function exists and working');
    }
    
    // Test 2: Check if admin_delete_user function exists
    console.log('2. Testing admin_delete_user function...');
    
    const { data: deleteResult, error: deleteError } = await supabase.rpc('admin_delete_user', {
      user_id: testUserId
    });
    
    if (deleteError) {
      if (deleteError.message?.includes('function') && deleteError.message?.includes('does not exist')) {
        console.error('❌ admin_delete_user function does not exist');
        return { success: false, error: 'admin_delete_user function missing' };
      } else {
        console.log('✅ admin_delete_user function exists (expected error for dummy UUID)');
      }
    } else {
      console.log('✅ admin_delete_user function exists and working');
    }
    
    // Test 3: Check if admin_create_user function exists
    console.log('3. Testing admin_create_user function...');
    
    const { data: createResult, error: createError } = await supabase.rpc('admin_create_user', {
      user_email: '<EMAIL>',
      user_name: 'Test User',
      user_role: 'user',
      user_phone: '+************',
      subscription_tier: 'free',
      is_verified: true,
      advocate_specializations: null,
      advocate_bio: null,
      advocate_hourly_rate: 500.00
    });
    
    if (createError) {
      if (createError.message?.includes('function') && createError.message?.includes('does not exist')) {
        console.error('❌ admin_create_user function does not exist');
        return { success: false, error: 'admin_create_user function missing' };
      } else {
        console.log('✅ admin_create_user function exists (may have validation error)');
      }
    } else {
      console.log('✅ admin_create_user function exists and working');
    }
    
    // Test 4: Check database permissions
    console.log('4. Testing database permissions...');
    
    const { data: profilesData, error: profilesError } = await supabase
      .from('profiles')
      .select('id, email, role')
      .limit(1);
    
    if (profilesError) {
      console.error('❌ Cannot access profiles table:', profilesError);
      return { success: false, error: 'Database access error' };
    } else {
      console.log('✅ Database access working');
    }
    
    // Test 5: Check current user permissions
    console.log('5. Testing current user permissions...');
    
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.error('❌ User not authenticated');
      return { success: false, error: 'User not authenticated' };
    }
    
    const { data: currentUserProfile, error: currentUserError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (currentUserError) {
      console.error('❌ Cannot get current user profile:', currentUserError);
      return { success: false, error: 'Cannot get user profile' };
    }
    
    if (currentUserProfile?.role !== 'admin') {
      console.error('❌ Current user is not admin:', currentUserProfile?.role);
      return { success: false, error: 'User is not admin' };
    }
    
    console.log('✅ Current user is admin');
    
    console.log('🎉 All admin function tests passed!');
    return { success: true, message: 'All tests passed' };
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return { success: false, error: error };
  }
};

// Function to test a real user update
export const testRealUserUpdate = async (userId: string, updateData: any) => {
  console.log('🧪 Testing real user update...');
  console.log('User ID:', userId);
  console.log('Update data:', updateData);
  
  try {
    const { data, error } = await supabase.rpc('admin_update_user', {
      user_id: userId,
      user_name: updateData.name || null,
      user_phone: updateData.phone || null,
      user_role: updateData.role || null,
      subscription_tier: updateData.subscription_tier || null,
      is_verified: updateData.is_verified ?? null,
      advocate_specializations: updateData.advocate_specializations || null,
      advocate_bio: updateData.advocate_bio || null,
      advocate_hourly_rate: updateData.advocate_hourly_rate || null
    });
    
    console.log('Update result:', { data, error });
    
    if (error) {
      console.error('❌ Real user update failed:', error);
      return { success: false, error };
    }
    
    console.log('✅ Real user update successful');
    return { success: true, data };
    
  } catch (error) {
    console.error('❌ Real user update failed with exception:', error);
    return { success: false, error };
  }
};
