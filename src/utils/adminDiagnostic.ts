// Admin diagnostic utility to check database functions and permissions
import { supabase } from '@/integrations/supabase/client';

export interface DiagnosticResult {
  success: boolean;
  message: string;
  details?: any;
  error?: any;
}

export const checkAdminFunctions = async (): Promise<DiagnosticResult> => {
  console.log('🔍 Checking admin functions...');
  
  try {
    // Test if admin_update_user function exists
    const testUserId = '00000000-0000-0000-0000-000000000000'; // Dummy UUID
    
    const { data, error } = await supabase.rpc('admin_update_user', {
      user_id: testUserId,
      user_name: 'Test',
      user_phone: null,
      user_role: null,
      subscription_tier: null,
      is_verified: null,
      advocate_specializations: null,
      advocate_bio: null,
      advocate_hourly_rate: null
    });

    if (error) {
      if (error.message.includes('function admin_update_user') || error.message.includes('does not exist')) {
        return {
          success: false,
          message: 'Function admin_update_user does not exist in database',
          error: error
        };
      } else if (error.message.includes('User not found')) {
        // This is expected for dummy UUID - function exists
        return {
          success: true,
          message: 'Function admin_update_user exists and is working',
          details: { functionExists: true, testResult: 'User not found (expected)' }
        };
      } else {
        return {
          success: false,
          message: 'Function exists but returned unexpected error',
          error: error
        };
      }
    }

    return {
      success: true,
      message: 'Function admin_update_user exists and is working',
      details: data
    };

  } catch (error: any) {
    return {
      success: false,
      message: 'Failed to test admin functions',
      error: error
    };
  }
};

export const checkUserPermissions = async (userId: string): Promise<DiagnosticResult> => {
  console.log('🔍 Checking user permissions...');
  
  try {
    // Check if user exists in profiles table
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (profileError) {
      return {
        success: false,
        message: 'Failed to fetch user profile',
        error: profileError
      };
    }

    if (!profile) {
      return {
        success: false,
        message: 'User profile not found',
        details: { userId }
      };
    }

    // Check if current user has admin role
    const { data: currentUser } = await supabase.auth.getUser();
    if (!currentUser.user) {
      return {
        success: false,
        message: 'No authenticated user found'
      };
    }

    const { data: currentProfile, error: currentProfileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', currentUser.user.id)
      .single();

    if (currentProfileError || !currentProfile) {
      return {
        success: false,
        message: 'Failed to fetch current user profile',
        error: currentProfileError
      };
    }

    if (currentProfile.role !== 'admin') {
      return {
        success: false,
        message: 'Current user does not have admin role',
        details: { currentRole: currentProfile.role }
      };
    }

    return {
      success: true,
      message: 'User permissions are valid',
      details: {
        targetUser: profile,
        currentUserRole: currentProfile.role
      }
    };

  } catch (error: any) {
    return {
      success: false,
      message: 'Failed to check user permissions',
      error: error
    };
  }
};

export const testDirectUpdate = async (userId: string, updateData: any): Promise<DiagnosticResult> => {
  console.log('🔍 Testing direct database update...');
  
  try {
    // Try direct update without RPC function
    const { data, error } = await supabase
      .from('profiles')
      .update({
        full_name: updateData.name,
        phone: updateData.phone,
        role: updateData.role,
        subscription_tier: updateData.subscription_tier,
        is_verified: updateData.is_verified,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      return {
        success: false,
        message: 'Direct update failed',
        error: error
      };
    }

    return {
      success: true,
      message: 'Direct update successful',
      details: data
    };

  } catch (error: any) {
    return {
      success: false,
      message: 'Direct update failed with exception',
      error: error
    };
  }
};

export const runFullDiagnostic = async (userId: string, updateData: any) => {
  console.log('🚀 Running full admin diagnostic...');
  
  const results = {
    functionsCheck: await checkAdminFunctions(),
    permissionsCheck: await checkUserPermissions(userId),
    directUpdateTest: await testDirectUpdate(userId, updateData)
  };

  console.log('📊 Diagnostic Results:', results);
  
  return results;
};
