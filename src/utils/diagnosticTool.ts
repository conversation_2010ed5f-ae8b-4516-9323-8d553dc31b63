// 🔧 Outil de Diagnostic Complet - Law App Morocco
// Utilisez cet outil pour diagnostiquer tous les problèmes

import { supabase } from '@/integrations/supabase/client';

export class DiagnosticTool {
  
  // 🔍 Test 1: Vérification de la connexion Supabase
  static async testSupabaseConnection() {
    console.log('🔍 Test 1: Connexion Supabase...');
    
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('count')
        .limit(1);
      
      if (error) {
        console.error('❌ Erreur connexion Supabase:', error);
        return { success: false, error: error.message };
      }
      
      console.log('✅ Connexion Supabase OK');
      return { success: true };
    } catch (error) {
      console.error('❌ Erreur critique Supabase:', error);
      return { success: false, error: 'Erreur critique de connexion' };
    }
  }

  // 🔍 Test 2: Vérification de l'authentification
  static async testAuthentication() {
    console.log('🔍 Test 2: État d\'authentification...');
    
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error) {
        console.error('❌ Erreur auth:', error);
        return { success: false, error: error.message, user: null };
      }
      
      if (!user) {
        console.log('⚠️ Aucun utilisateur connecté');
        return { success: true, user: null, message: 'Non connecté' };
      }
      
      console.log('✅ Utilisateur connecté:', user.email);
      
      // Vérifier le profil utilisateur
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();
      
      if (profileError) {
        console.error('❌ Erreur profil:', profileError);
        return { success: false, error: 'Profil non trouvé', user };
      }
      
      console.log('✅ Profil utilisateur:', profile);
      return { success: true, user, profile };
      
    } catch (error) {
      console.error('❌ Erreur critique auth:', error);
      return { success: false, error: 'Erreur critique authentification' };
    }
  }

  // 🔍 Test 3: Vérification des questions
  static async testQuestionsAccess(userId?: string) {
    console.log('🔍 Test 3: Accès aux questions...');
    
    try {
      // Test 1: Accès général aux questions
      const { data: allQuestions, error: allError } = await supabase
        .from('legal_questions')
        .select('*')
        .limit(5);
      
      if (allError) {
        console.error('❌ Erreur accès questions (général):', allError);
        return { success: false, error: allError.message };
      }
      
      console.log('✅ Questions trouvées (général):', allQuestions?.length || 0);
      
      // Test 2: Accès aux questions utilisateur spécifique
      if (userId) {
        const { data: userQuestions, error: userError } = await supabase
          .from('legal_questions')
          .select('*')
          .eq('user_id', userId);
        
        if (userError) {
          console.error('❌ Erreur questions utilisateur:', userError);
          return { success: false, error: userError.message };
        }
        
        console.log('✅ Questions utilisateur:', userQuestions?.length || 0);
        return { success: true, allQuestions, userQuestions };
      }
      
      return { success: true, allQuestions };
      
    } catch (error) {
      console.error('❌ Erreur critique questions:', error);
      return { success: false, error: 'Erreur critique accès questions' };
    }
  }

  // 🔍 Test 4: Vérification des politiques RLS
  static async testRLSPolicies() {
    console.log('🔍 Test 4: Politiques RLS...');
    
    try {
      // Vérifier les politiques sur legal_questions
      const { data: policies, error } = await supabase
        .rpc('get_table_policies', { table_name: 'legal_questions' })
        .catch(() => ({ data: null, error: 'RPC non disponible' }));
      
      if (error) {
        console.log('⚠️ Impossible de vérifier les politiques RLS automatiquement');
        console.log('📝 Vérifiez manuellement dans Supabase Dashboard → Authentication → Policies');
      } else {
        console.log('✅ Politiques RLS:', policies);
      }
      
      return { success: true, policies };
      
    } catch (error) {
      console.error('❌ Erreur vérification RLS:', error);
      return { success: false, error: 'Erreur vérification RLS' };
    }
  }

  // 🔍 Test 5: Vérification des rôles admin
  static async testAdminAccess(userId?: string) {
    console.log('🔍 Test 5: Accès admin...');
    
    if (!userId) {
      console.log('⚠️ Aucun utilisateur pour tester l\'accès admin');
      return { success: false, error: 'Utilisateur requis' };
    }
    
    try {
      // Vérifier le rôle de l'utilisateur
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('role, is_verified')
        .eq('id', userId)
        .single();
      
      if (error) {
        console.error('❌ Erreur récupération profil admin:', error);
        return { success: false, error: error.message };
      }
      
      console.log('📋 Rôle utilisateur:', profile.role);
      console.log('✅ Vérifié:', profile.is_verified);
      
      if (profile.role === 'admin') {
        // Tester l'accès aux données admin
        const { data: allProfiles, error: adminError } = await supabase
          .from('profiles')
          .select('*')
          .limit(5);
        
        if (adminError) {
          console.error('❌ Erreur accès données admin:', adminError);
          return { success: false, error: 'Accès admin bloqué', profile };
        }
        
        console.log('✅ Accès admin fonctionnel, profils trouvés:', allProfiles?.length);
        return { success: true, profile, adminAccess: true, profilesCount: allProfiles?.length };
      }
      
      return { success: true, profile, adminAccess: false };
      
    } catch (error) {
      console.error('❌ Erreur critique admin:', error);
      return { success: false, error: 'Erreur critique accès admin' };
    }
  }

  // 🔍 Test 6: Vérification des avocats
  static async testAdvocatesAccess() {
    console.log('🔍 Test 6: Accès aux avocats...');
    
    try {
      const { data: advocates, error } = await supabase
        .from('advocates')
        .select(`
          *,
          profiles!advocates_profile_id_fkey(
            full_name,
            email,
            is_verified
          )
        `)
        .limit(10);
      
      if (error) {
        console.error('❌ Erreur accès avocats:', error);
        return { success: false, error: error.message };
      }
      
      console.log('✅ Avocats trouvés:', advocates?.length || 0);
      console.log('📋 Avocats vérifiés:', advocates?.filter(a => a.profiles?.is_verified).length || 0);
      
      return { success: true, advocates };
      
    } catch (error) {
      console.error('❌ Erreur critique avocats:', error);
      return { success: false, error: 'Erreur critique accès avocats' };
    }
  }

  // 🚀 Diagnostic complet
  static async runFullDiagnostic() {
    console.log('🚀 === DIAGNOSTIC COMPLET LAW APP MOROCCO ===\n');
    
    const results = {
      supabase: await this.testSupabaseConnection(),
      auth: await this.testAuthentication(),
      questions: null as any,
      rls: await this.testRLSPolicies(),
      admin: null as any,
      advocates: await this.testAdvocatesAccess()
    };
    
    // Tests dépendants de l'auth
    if (results.auth.success && results.auth.user) {
      results.questions = await this.testQuestionsAccess(results.auth.user.id);
      results.admin = await this.testAdminAccess(results.auth.user.id);
    } else {
      results.questions = await this.testQuestionsAccess();
    }
    
    console.log('\n📊 === RÉSUMÉ DU DIAGNOSTIC ===');
    console.log('Connexion Supabase:', results.supabase.success ? '✅' : '❌');
    console.log('Authentification:', results.auth.success ? '✅' : '❌');
    console.log('Accès Questions:', results.questions?.success ? '✅' : '❌');
    console.log('Politiques RLS:', results.rls.success ? '✅' : '❌');
    console.log('Accès Admin:', results.admin?.success ? '✅' : '❌');
    console.log('Accès Avocats:', results.advocates.success ? '✅' : '❌');
    
    // Recommandations
    console.log('\n🔧 === RECOMMANDATIONS ===');
    
    if (!results.supabase.success) {
      console.log('🚨 CRITIQUE: Vérifiez la configuration Supabase');
    }
    
    if (!results.auth.success || !results.auth.user) {
      console.log('🔑 Connectez-vous d\'abord pour tester les fonctionnalités');
    }
    
    if (!results.questions?.success) {
      console.log('📝 Problème d\'accès aux questions - Vérifiez les politiques RLS');
    }
    
    if (results.auth.profile?.role === 'admin' && !results.admin?.adminAccess) {
      console.log('👨‍💼 Problème d\'accès admin - Vérifiez les politiques RLS pour les admins');
    }
    
    return results;
  }
}

// Utilisation dans la console du navigateur :
// DiagnosticTool.runFullDiagnostic();
