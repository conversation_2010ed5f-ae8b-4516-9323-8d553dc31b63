// Quick diagnostic utility for admin user update issues
import { supabase } from '@/integrations/supabase/client';

export const runQuickDiagnostic = async () => {
  console.log('🔍 Running Quick Diagnostic for Admin User Update Issues...');
  
  const results = {
    authStatus: false,
    userRole: null,
    functionsExist: false,
    databaseAccess: false,
    permissions: false,
    errors: [] as string[]
  };

  try {
    // 1. Check authentication
    console.log('1. Checking authentication...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      results.errors.push('User not authenticated');
      console.error('❌ Authentication failed:', authError);
      return results;
    }
    
    results.authStatus = true;
    console.log('✅ User authenticated:', user.email);

    // 2. Check user role
    console.log('2. Checking user role...');
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role, email')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      results.errors.push('Cannot access user profile');
      console.error('❌ Profile access failed:', profileError);
      return results;
    }
    
    results.userRole = profile.role;
    console.log('✅ User role:', profile.role);
    
    if (profile.role !== 'admin') {
      results.errors.push('User is not admin');
      console.error('❌ User is not admin, current role:', profile.role);
      return results;
    }

    // 3. Check database access
    console.log('3. Checking database access...');
    const { data: testProfiles, error: dbError } = await supabase
      .from('profiles')
      .select('id, email')
      .limit(1);
    
    if (dbError) {
      results.errors.push('Database access failed');
      console.error('❌ Database access failed:', dbError);
      return results;
    }
    
    results.databaseAccess = true;
    console.log('✅ Database access working');

    // 4. Check if admin functions exist
    console.log('4. Checking admin functions...');
    const testUserId = '00000000-0000-0000-0000-000000000000';
    
    const { data: updateResult, error: updateError } = await supabase.rpc('admin_update_user', {
      user_id: testUserId,
      user_name: 'Test',
      user_phone: null,
      user_role: null,
      subscription_tier: null,
      is_verified: null,
      advocate_specializations: null,
      advocate_bio: null,
      advocate_hourly_rate: null
    });
    
    if (updateError) {
      if (updateError.message?.includes('function') && updateError.message?.includes('does not exist')) {
        results.errors.push('admin_update_user function does not exist');
        console.error('❌ admin_update_user function missing');
        return results;
      } else {
        // Function exists but failed due to invalid UUID (expected)
        results.functionsExist = true;
        console.log('✅ admin_update_user function exists');
      }
    } else {
      results.functionsExist = true;
      console.log('✅ admin_update_user function exists and working');
    }

    // 5. Check permissions with a real user
    console.log('5. Checking update permissions...');
    if (testProfiles && testProfiles.length > 0) {
      const realUserId = testProfiles[0].id;
      
      const { data: permissionTest, error: permissionError } = await supabase.rpc('admin_update_user', {
        user_id: realUserId,
        user_name: null, // No actual changes
        user_phone: null,
        user_role: null,
        subscription_tier: null,
        is_verified: null,
        advocate_specializations: null,
        advocate_bio: null,
        advocate_hourly_rate: null
      });
      
      if (permissionError) {
        results.errors.push('Permission denied for user updates');
        console.error('❌ Permission test failed:', permissionError);
        return results;
      }
      
      results.permissions = true;
      console.log('✅ Update permissions working');
    }

    console.log('🎉 All diagnostic checks passed!');
    return results;

  } catch (error) {
    results.errors.push(`Unexpected error: ${error}`);
    console.error('❌ Diagnostic failed with error:', error);
    return results;
  }
};

export const generateDiagnosticReport = (results: any) => {
  const report = {
    status: results.errors.length === 0 ? 'SUCCESS' : 'FAILED',
    summary: {
      authenticated: results.authStatus,
      isAdmin: results.userRole === 'admin',
      databaseAccess: results.databaseAccess,
      functionsExist: results.functionsExist,
      permissions: results.permissions
    },
    errors: results.errors,
    recommendations: [] as string[]
  };

  // Generate recommendations based on failures
  if (!results.authStatus) {
    report.recommendations.push('Please log in to the application');
  }
  
  if (results.userRole !== 'admin') {
    report.recommendations.push('Contact an administrator to grant admin privileges');
  }
  
  if (!results.databaseAccess) {
    report.recommendations.push('Check Supabase connection and RLS policies');
  }
  
  if (!results.functionsExist) {
    report.recommendations.push('Deploy admin functions using DEPLOY_ADMIN_FUNCTIONS.sql');
  }
  
  if (!results.permissions) {
    report.recommendations.push('Check RLS policies for admin user management');
  }

  return report;
};

// Function to run diagnostic and display results
export const runDiagnosticWithReport = async () => {
  const results = await runQuickDiagnostic();
  const report = generateDiagnosticReport(results);
  
  console.log('📊 DIAGNOSTIC REPORT:');
  console.log('Status:', report.status);
  console.log('Summary:', report.summary);
  
  if (report.errors.length > 0) {
    console.log('❌ Errors:', report.errors);
    console.log('💡 Recommendations:', report.recommendations);
  }
  
  return report;
};
