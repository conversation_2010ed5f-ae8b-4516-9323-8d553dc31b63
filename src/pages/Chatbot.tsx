import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import { Navigate } from 'react-router-dom';
import { ChatInterface } from '@/components/chatbot/ChatInterface';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { 
  ChatbotService, 
  ChatConversation 
} from '@/services/chatbotService';
import { 
  MessageCircle, 
  Plus, 
  Archive, 
  Trash2,
  Clock
} from 'lucide-react';

const Chatbot = () => {
  const { user, profile, loading } = useAuth();
  const { t, i18n } = useTranslation();
  const [conversations, setConversations] = useState<ChatConversation[]>([]);
  const [selectedConversationId, setSelectedConversationId] = useState<string | undefined>();
  const [showSidebar, setShowSidebar] = useState(true);
  const currentLanguage = i18n.language as 'ar' | 'fr';

  useEffect(() => {
    if (user) {
      loadConversations();
    }
  }, [user]);

  const loadConversations = async () => {
    if (!user) return;
    
    try {
      const userConversations = await ChatbotService.getUserConversations(user.id);
      setConversations(userConversations);
      
      // Select the most recent conversation if none is selected
      if (!selectedConversationId && userConversations.length > 0) {
        setSelectedConversationId(userConversations[0].id);
      }
    } catch (error) {
      console.error('Failed to load conversations:', error);
    }
  };

  const handleNewConversation = async () => {
    if (!user) return;
    
    try {
      const newConversationId = await ChatbotService.createConversation(
        user.id, 
        currentLanguage
      );
      setSelectedConversationId(newConversationId);
      await loadConversations();
    } catch (error) {
      console.error('Failed to create conversation:', error);
    }
  };

  const handleArchiveConversation = async (conversationId: string) => {
    try {
      await ChatbotService.archiveConversation(conversationId);
      await loadConversations();
      
      if (selectedConversationId === conversationId) {
        const activeConversations = conversations.filter(c => c.status === 'active' && c.id !== conversationId);
        setSelectedConversationId(activeConversations.length > 0 ? activeConversations[0].id : undefined);
      }
    } catch (error) {
      console.error('Failed to archive conversation:', error);
    }
  };

  const handleDeleteConversation = async (conversationId: string) => {
    try {
      await ChatbotService.deleteConversation(conversationId);
      await loadConversations();
      
      if (selectedConversationId === conversationId) {
        const remainingConversations = conversations.filter(c => c.id !== conversationId);
        setSelectedConversationId(remainingConversations.length > 0 ? remainingConversations[0].id : undefined);
      }
    } catch (error) {
      console.error('Failed to delete conversation:', error);
    }
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString(currentLanguage === 'ar' ? 'ar-MA' : 'fr-MA', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString(currentLanguage === 'ar' ? 'ar-MA' : 'fr-MA', {
        weekday: 'short'
      });
    } else {
      return date.toLocaleDateString(currentLanguage === 'ar' ? 'ar-MA' : 'fr-MA', {
        month: 'short',
        day: 'numeric'
      });
    }
  };

  if (loading && !user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/" replace />;
  }

  const activeConversations = conversations.filter(c => c.status === 'active');
  const archivedConversations = conversations.filter(c => c.status === 'archived');

  return (
    <div className="w-full min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-6 min-h-[calc(100vh-200px)]">
          {/* Sidebar */}
          {showSidebar && (
            <div className="w-full lg:w-80 flex-shrink-0">
              <Card className="h-full flex flex-col">
              <CardHeader className="border-b">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <MessageCircle className="h-5 w-5" />
                    {currentLanguage === 'ar' ? 'المحادثات' : 'Conversations'}
                  </CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleNewConversation}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent className="flex-1 p-0">
                <ScrollArea className="h-full">
                  <div className="p-4 space-y-4">
                    {/* Active Conversations */}
                    {activeConversations.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-500 mb-2">
                          {currentLanguage === 'ar' ? 'المحادثات النشطة' : 'Conversations actives'}
                        </h4>
                        <div className="space-y-2">
                          {activeConversations.map((conversation) => (
                            <div
                              key={conversation.id}
                              className={`p-3 rounded-lg cursor-pointer transition-colors group ${
                                selectedConversationId === conversation.id
                                  ? 'bg-blue-50 border border-blue-200'
                                  : 'hover:bg-gray-50'
                              }`}
                              onClick={() => setSelectedConversationId(conversation.id)}
                            >
                              <div className="flex items-start justify-between">
                                <div className="flex-1 min-w-0">
                                  <p className="text-sm font-medium truncate">
                                    {conversation.title}
                                  </p>
                                  <p className="text-xs text-gray-500 flex items-center gap-1 mt-1">
                                    <Clock className="h-3 w-3" />
                                    {formatDate(conversation.updatedAt)}
                                  </p>
                                  {conversation.messages.length > 0 && (
                                    <p className="text-xs text-gray-400 truncate mt-1">
                                      {conversation.messages[conversation.messages.length - 1].content}
                                    </p>
                                  )}
                                </div>
                                <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleArchiveConversation(conversation.id);
                                    }}
                                  >
                                    <Archive className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDeleteConversation(conversation.id);
                                    }}
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Archived Conversations */}
                    {archivedConversations.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-500 mb-2">
                          {currentLanguage === 'ar' ? 'المحادثات المؤرشفة' : 'Conversations archivées'}
                        </h4>
                        <div className="space-y-2">
                          {archivedConversations.map((conversation) => (
                            <div
                              key={conversation.id}
                              className="p-3 rounded-lg cursor-pointer transition-colors group hover:bg-gray-50 opacity-60"
                              onClick={() => setSelectedConversationId(conversation.id)}
                            >
                              <div className="flex items-start justify-between">
                                <div className="flex-1 min-w-0">
                                  <p className="text-sm font-medium truncate">
                                    {conversation.title}
                                  </p>
                                  <div className="flex items-center gap-2 mt-1">
                                    <Badge variant="secondary" className="text-xs">
                                      {currentLanguage === 'ar' ? 'مؤرشف' : 'Archivé'}
                                    </Badge>
                                    <p className="text-xs text-gray-500">
                                      {formatDate(conversation.updatedAt)}
                                    </p>
                                  </div>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="opacity-0 group-hover:opacity-100 transition-opacity"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteConversation(conversation.id);
                                  }}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {conversations.length === 0 && (
                      <div className="text-center py-8">
                        <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500 text-sm">
                          {currentLanguage === 'ar' 
                            ? 'لا توجد محادثات بعد' 
                            : 'Aucune conversation pour le moment'
                          }
                        </p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2"
                          onClick={handleNewConversation}
                        >
                          {currentLanguage === 'ar' ? 'ابدأ محادثة' : 'Commencer une conversation'}
                        </Button>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        )}

          {/* Main Chat Area */}
          <div className="flex-1 min-w-0">
            <ChatInterface
              conversationId={selectedConversationId}
              onConversationChange={(newConversationId) => {
                setSelectedConversationId(newConversationId);
                loadConversations();
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Chatbot;
