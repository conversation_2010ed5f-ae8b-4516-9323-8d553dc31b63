import React from 'react';
import { AuthDebug } from '@/components/debug/AuthDebug';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const Debug = () => {
  const { user, profile, loading } = useAuth();

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-bold mb-8">Debug Page</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Auth Context Status */}
          <Card>
            <CardHeader>
              <CardTitle>Auth Context Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Loading State</h3>
                <Badge variant={loading ? "destructive" : "default"}>
                  {loading ? "Loading..." : "Ready"}
                </Badge>
              </div>

              <div>
                <h3 className="font-medium mb-2">User</h3>
                <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
                  {JSON.stringify(user, null, 2)}
                </pre>
              </div>

              <div>
                <h3 className="font-medium mb-2">Profile</h3>
                <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
                  {JSON.stringify(profile, null, 2)}
                </pre>
              </div>
            </CardContent>
          </Card>

          {/* Supabase Connection */}
          <div>
            <AuthDebug />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Debug;
