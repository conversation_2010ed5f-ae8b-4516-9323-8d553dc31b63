import React from 'react';
import { useAuth } from '@/hooks/useAuth';
import { SubscriptionManagement } from '@/components/subscription';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Crown, 
  ArrowRight,
  Shield,
  Zap,
  Users
} from 'lucide-react';

const SubscriptionPage: React.FC = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto">
            {/* Hero Section */}
            <div className="text-center mb-16">
              <div className="flex justify-center mb-6">
                <div className="bg-blue-100 p-4 rounded-full">
                  <Crown className="h-12 w-12 text-blue-600" />
                </div>
              </div>
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                اشتراكات المساعدة القانونية
              </h1>
              <p className="text-xl text-gray-600 mb-8">
                احصل على أفضل الخدمات القانونية مع باقاتنا المتنوعة
              </p>
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                <ArrowRight className="h-5 w-5 ml-2" />
                سجل دخولك للمتابعة
              </Button>
            </div>

            {/* Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="bg-green-100 p-3 rounded-full w-fit mx-auto mb-4">
                    <Shield className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">حماية قانونية شاملة</h3>
                  <p className="text-gray-600">
                    احصل على استشارات قانونية موثوقة من خبراء متخصصين
                  </p>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="bg-blue-100 p-3 rounded-full w-fit mx-auto mb-4">
                    <Zap className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">استجابة سريعة</h3>
                  <p className="text-gray-600">
                    احصل على إجابات لأسئلتك القانونية في أسرع وقت ممكن
                  </p>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="bg-purple-100 p-3 rounded-full w-fit mx-auto mb-4">
                    <Users className="h-8 w-8 text-purple-600" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">شبكة محامين متخصصين</h3>
                  <p className="text-gray-600">
                    تواصل مع أفضل المحامين في مختلف التخصصات القانونية
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Pricing Preview */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h2 className="text-2xl font-bold text-center mb-8">باقات الاشتراك</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="border rounded-lg p-6 text-center">
                  <h3 className="text-lg font-semibold mb-2">مجاني</h3>
                  <div className="text-3xl font-bold mb-4">0 درهم</div>
                  <ul className="text-sm text-gray-600 space-y-2">
                    <li>سؤال قانوني واحد شهرياً</li>
                    <li>إنشاء وثائق محدود</li>
                    <li>تجربة مجانية 7 أيام</li>
                  </ul>
                </div>

                <div className="border-2 border-blue-500 rounded-lg p-6 text-center relative">
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm">
                      الأكثر شعبية
                    </span>
                  </div>
                  <h3 className="text-lg font-semibold mb-2">المستخدم المتميز</h3>
                  <div className="text-3xl font-bold mb-4">100 درهم</div>
                  <ul className="text-sm text-gray-600 space-y-2">
                    <li>أسئلة قانونية غير محدودة</li>
                    <li>إنشاء وثائق غير محدود</li>
                    <li>دعم ذو أولوية</li>
                    <li>وصول كامل للمحامين</li>
                  </ul>
                </div>

                <div className="border rounded-lg p-6 text-center">
                  <h3 className="text-lg font-semibold mb-2">المحامي المتميز</h3>
                  <div className="text-3xl font-bold mb-4">500 درهم</div>
                  <ul className="text-sm text-gray-600 space-y-2">
                    <li>ملف شخصي مميز</li>
                    <li>أدوات الحجز والتحليلات</li>
                    <li>تحديد الأوقات والأسعار</li>
                    <li>أولوية في عرض الملف</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <SubscriptionManagement userId={user.id} />
        </div>
      </div>
    </div>
  );
};

export default SubscriptionPage;
