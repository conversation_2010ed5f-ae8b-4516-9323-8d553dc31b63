import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import { Navigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { UserCreationForm } from '@/components/admin/UserCreationForm';
import { UserManagementTable } from '@/components/admin/UserManagementTable';
import { UserEditForm } from '@/components/admin/UserEditForm';
import { AnalyticsDashboard } from '@/components/admin/AnalyticsDashboard';
import { AdminNavbar } from '@/components/admin/AdminNavbar';
import { AdminService, UserProfile } from '@/services/adminService';
import { Loading } from '@/components/ui/loading';
import {
  Users,
  UserPlus,
  BarChart3,
  Settings,
  Shield,
  TrendingUp,
  DollarSign,
  MessageCircle,
  FileText,
  Activity,
  Crown
} from 'lucide-react';

const AdminEnhanced = () => {
  const { user, profile, loading } = useAuth();
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const currentLanguage = i18n.language as 'ar' | 'fr';

  // State management
  const [activeTab, setActiveTab] = useState('overview');
  const [showUserCreationForm, setShowUserCreationForm] = useState(false);
  const [showUserEditForm, setShowUserEditForm] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [quickStats, setQuickStats] = useState({
    totalUsers: 0,
    totalAdvocates: 0,
    verifiedAdvocates: 0,
    totalQuestions: 0,
    monthlyRevenue: 0
  });
  const [statsLoading, setStatsLoading] = useState(true);

  useEffect(() => {
    if (user && profile?.role === 'admin') {
      loadQuickStats();
    }
  }, [user, profile, refreshTrigger]);

  const loadQuickStats = async () => {
    setStatsLoading(true);
    try {
      const { data: userAnalytics } = await AdminService.getUserAnalytics();
      const { data: questionsAnalytics } = await AdminService.getQuestionsAnalytics();
      const { data: advocateAnalytics } = await AdminService.getAdvocateAnalytics();

      if (userAnalytics && questionsAnalytics && advocateAnalytics) {
        const proUsers = (userAnalytics.subscription_tiers?.pro_user || 0) * 99;
        const proAdvocates = (userAnalytics.subscription_tiers?.pro_advocate || 0) * 199;
        
        setQuickStats({
          totalUsers: userAnalytics.total_users,
          totalAdvocates: advocateAnalytics.total_advocates,
          verifiedAdvocates: advocateAnalytics.verified_advocates,
          totalQuestions: questionsAnalytics.total_questions,
          monthlyRevenue: proUsers + proAdvocates
        });
      }
    } catch (error) {
      console.error('Error loading quick stats:', error);
    } finally {
      setStatsLoading(false);
    }
  };

  const handleUserCreated = () => {
    setRefreshTrigger(prev => prev + 1);
    loadQuickStats();
  };

  const handleUserUpdated = () => {
    setRefreshTrigger(prev => prev + 1);
    loadQuickStats();
  };

  const handleEditUser = (user: UserProfile) => {
    setSelectedUser(user);
    setShowUserEditForm(true);
  };

  if (loading && !user) {
    return <Loading fullScreen text={t('common.loading')} />;
  }

  if (!user || profile?.role !== 'admin') {
    return <Navigate to="/" replace />;
  }

  const quickStatsCards = [
    {
      title: currentLanguage === 'ar' ? 'إجمالي المستخدمين' : 'Total Utilisateurs',
      value: quickStats.totalUsers,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: currentLanguage === 'ar' ? 'المحامون المفعلون' : 'Avocats Vérifiés',
      value: `${quickStats.verifiedAdvocates}/${quickStats.totalAdvocates}`,
      icon: Crown,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: currentLanguage === 'ar' ? 'إجمالي الأسئلة' : 'Total Questions',
      value: quickStats.totalQuestions,
      icon: MessageCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: currentLanguage === 'ar' ? 'الإيرادات الشهرية' : 'Revenus Mensuels',
      value: `${quickStats.monthlyRevenue} MAD`,
      icon: DollarSign,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Admin Navbar */}
      <AdminNavbar
        userEmail={profile?.email}
        stats={quickStats}
        statsLoading={statsLoading}
      />

      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Page Title */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            لوحة التحكم الرئيسية
          </h1>
          <p className="text-gray-600">
            إدارة شاملة للمستخدمين والنظام مع إحصائيات متقدمة
          </p>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              {currentLanguage === 'ar' ? 'نظرة عامة' : 'Aperçu'}
            </TabsTrigger>
            <TabsTrigger value="users" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              {currentLanguage === 'ar' ? 'إدارة المستخدمين' : 'Gestion Utilisateurs'}
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              {currentLanguage === 'ar' ? 'التحليلات المتقدمة' : 'Analytics Avancées'}
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              {currentLanguage === 'ar' ? 'الإعدادات' : 'Paramètres'}
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    {currentLanguage === 'ar' ? 'إجراءات سريعة' : 'Actions Rapides'}
                  </CardTitle>
                  <CardDescription>
                    {currentLanguage === 'ar' 
                      ? 'العمليات الأكثر استخداماً' 
                      : 'Opérations les plus utilisées'
                    }
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button 
                    onClick={() => setShowUserCreationForm(true)}
                    className="w-full justify-start"
                  >
                    <UserPlus className="mr-2 h-4 w-4" />
                    {currentLanguage === 'ar' ? 'إنشاء مستخدم جديد' : 'Créer un utilisateur'}
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => setActiveTab('users')}
                    className="w-full justify-start"
                  >
                    <Users className="mr-2 h-4 w-4" />
                    {currentLanguage === 'ar' ? 'إدارة المستخدمين' : 'Gérer les utilisateurs'}
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => setActiveTab('analytics')}
                    className="w-full justify-start"
                  >
                    <BarChart3 className="mr-2 h-4 w-4" />
                    {currentLanguage === 'ar' ? 'عرض التحليلات' : 'Voir les analytics'}
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    {currentLanguage === 'ar' ? 'ملخص النظام' : 'Résumé du Système'}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>{currentLanguage === 'ar' ? 'معدل نمو المستخدمين' : 'Taux de croissance'}</span>
                    <Badge variant="default">+12%</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>{currentLanguage === 'ar' ? 'معدل الاستجابة' : 'Taux de réponse'}</span>
                    <Badge variant="secondary">85%</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>{currentLanguage === 'ar' ? 'رضا المستخدمين' : 'Satisfaction utilisateurs'}</span>
                    <Badge variant="outline">4.8/5</Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Users Management Tab */}
          <TabsContent value="users" className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold">
                  {currentLanguage === 'ar' ? 'إدارة المستخدمين' : 'Gestion des Utilisateurs'}
                </h2>
                <p className="text-gray-600">
                  {currentLanguage === 'ar' 
                    ? 'إنشاء وتعديل وإدارة جميع المستخدمين' 
                    : 'Créer, modifier et gérer tous les utilisateurs'
                  }
                </p>
              </div>
              <Button onClick={() => setShowUserCreationForm(true)}>
                <UserPlus className="h-4 w-4 mr-2" />
                {currentLanguage === 'ar' ? 'إنشاء مستخدم' : 'Créer Utilisateur'}
              </Button>
            </div>

            <UserManagementTable 
              onEditUser={handleEditUser}
              refreshTrigger={refreshTrigger}
            />
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <AnalyticsDashboard />
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  {currentLanguage === 'ar' ? 'إعدادات النظام' : 'Paramètres Système'}
                </CardTitle>
                <CardDescription>
                  {currentLanguage === 'ar' 
                    ? 'إعدادات وتكوين النظام' 
                    : 'Configuration et paramètres du système'
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <Settings className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {currentLanguage === 'ar' ? 'قريباً' : 'Bientôt disponible'}
                  </h3>
                  <p className="text-gray-600">
                    {currentLanguage === 'ar' 
                      ? 'ستكون إعدادات النظام متاحة قريباً' 
                      : 'Les paramètres système seront bientôt disponibles'
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Modals */}
        {showUserCreationForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center p-4 z-50 overflow-y-auto">
            <div className="w-full max-w-2xl my-8">
              <UserCreationForm
                onUserCreated={handleUserCreated}
                onClose={() => setShowUserCreationForm(false)}
              />
            </div>
          </div>
        )}

        {showUserEditForm && selectedUser && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center p-4 z-50 overflow-y-auto">
            <div className="w-full max-w-2xl my-8">
              <UserEditForm
                user={selectedUser}
                onUserUpdated={handleUserUpdated}
                onClose={() => {
                  setShowUserEditForm(false);
                  setSelectedUser(null);
                }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminEnhanced;
