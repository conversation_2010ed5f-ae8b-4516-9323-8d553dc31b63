import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Navigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { QuestionDetailTest } from '@/components/client/QuestionDetailTest';
import { QuestionsList } from '@/components/client/QuestionsList';
import { 
  Bug, 
  TestTube, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  Code
} from 'lucide-react';

const QuestionDetailDebugPage = () => {
  const { user, profile, loading } = useAuth();
  const { toast } = useToast();
  const [debugInfo, setDebugInfo] = useState<any>({});

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Redirect if not authenticated
  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  const runDiagnostics = () => {
    const diagnostics = {
      timestamp: new Date().toISOString(),
      user: {
        id: user.id,
        email: user.email,
        role: profile?.role
      },
      browser: {
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform
      },
      react: {
        version: React.version,
        strictMode: false // We can't easily detect this
      },
      imports: {
        lucideReact: typeof import('lucide-react'),
        questionDetailView: typeof import('@/components/client/QuestionDetailView'),
        questionsList: typeof import('@/components/client/QuestionsList')
      },
      errors: []
    };

    // Test icon imports
    try {
      const { X } = require('lucide-react');
      diagnostics.imports.xIcon = typeof X;
    } catch (error) {
      diagnostics.errors.push(`X icon import error: ${error}`);
    }

    setDebugInfo(diagnostics);
    
    toast({
      title: 'تم تشغيل التشخيص',
      description: 'تم جمع معلومات التشخيص بنجاح',
    });
  };

  const testSteps = [
    {
      id: 1,
      title: 'اختبار استيراد الأيقونات',
      description: 'التحقق من استيراد أيقونة X من lucide-react',
      status: 'pending'
    },
    {
      id: 2,
      title: 'اختبار تحميل المكون',
      description: 'التحقق من تحميل QuestionDetailView بدون أخطاء',
      status: 'pending'
    },
    {
      id: 3,
      title: 'اختبار فتح النافذة',
      description: 'اختبار فتح نافذة تفاصيل السؤال',
      status: 'pending'
    },
    {
      id: 4,
      title: 'اختبار إغلاق النافذة',
      description: 'اختبار إغلاق النافذة بالضغط على X',
      status: 'pending'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center gap-3">
            <Bug className="h-8 w-8 text-red-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                تشخيص مشكلة "عرض التفاصيل"
              </h1>
              <p className="text-gray-600">
                صفحة تشخيص لحل مشكلة "X is not defined"
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
        <Tabs defaultValue="diagnostics" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4">
            <TabsTrigger value="diagnostics" className="flex items-center gap-2">
              <Bug className="h-4 w-4" />
              <span className="hidden sm:inline">التشخيص</span>
            </TabsTrigger>
            <TabsTrigger value="test" className="flex items-center gap-2">
              <TestTube className="h-4 w-4" />
              <span className="hidden sm:inline">الاختبار</span>
            </TabsTrigger>
            <TabsTrigger value="live-test" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              <span className="hidden sm:inline">اختبار مباشر</span>
            </TabsTrigger>
            <TabsTrigger value="code" className="flex items-center gap-2">
              <Code className="h-4 w-4" />
              <span className="hidden sm:inline">الكود</span>
            </TabsTrigger>
          </TabsList>

          {/* Diagnostics Tab */}
          <TabsContent value="diagnostics" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bug className="h-5 w-5 text-red-600" />
                  معلومات التشخيص
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button onClick={runDiagnostics} className="w-full">
                  تشغيل التشخيص
                </Button>
                
                {Object.keys(debugInfo).length > 0 && (
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-medium mb-2">نتائج التشخيص:</h3>
                    <pre className="text-xs overflow-auto max-h-96 bg-white p-3 rounded border">
                      {JSON.stringify(debugInfo, null, 2)}
                    </pre>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>خطوات الاختبار</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {testSteps.map((step) => (
                    <div key={step.id} className="flex items-center gap-3 p-3 border rounded">
                      <div className="flex-shrink-0">
                        {step.status === 'success' ? (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        ) : step.status === 'error' ? (
                          <XCircle className="h-5 w-5 text-red-600" />
                        ) : (
                          <div className="h-5 w-5 border-2 border-gray-300 rounded-full" />
                        )}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium">{step.title}</h4>
                        <p className="text-sm text-gray-600">{step.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Test Tab */}
          <TabsContent value="test">
            <QuestionDetailTest userId={user.id} />
          </TabsContent>

          {/* Live Test Tab */}
          <TabsContent value="live-test">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5 text-blue-600" />
                  اختبار مباشر مع البيانات الحقيقية
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="bg-yellow-50 border border-yellow-200 rounded p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      <span className="font-medium text-yellow-800">تحذير</span>
                    </div>
                    <p className="text-yellow-700 text-sm">
                      هذا الاختبار يستخدم البيانات الحقيقية من قاعدة البيانات. 
                      إذا لم تكن لديك أسئلة، قم بإنشاء سؤال تجريبي أولاً.
                    </p>
                  </div>
                  
                  <QuestionsList 
                    userId={user.id}
                    onCreateQuestion={() => {
                      toast({
                        title: 'إنشاء سؤال جديد',
                        description: 'يمكنك إنشاء سؤال جديد من الصفحة الرئيسية',
                      });
                    }}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Code Tab */}
          <TabsContent value="code">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Code className="h-5 w-5 text-purple-600" />
                  الحلول المطبقة
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="bg-green-50 border border-green-200 rounded p-4">
                    <h3 className="font-medium text-green-800 mb-2">✅ الحل 1: إعادة تسمية الاستيراد</h3>
                    <code className="text-sm bg-white p-2 rounded block">
                      {`import { X as CloseIcon } from 'lucide-react';`}
                    </code>
                  </div>
                  
                  <div className="bg-blue-50 border border-blue-200 rounded p-4">
                    <h3 className="font-medium text-blue-800 mb-2">✅ الحل 2: Fallback للأيقونة</h3>
                    <code className="text-sm bg-white p-2 rounded block">
                      {`{CloseIcon ? <CloseIcon className="h-4 w-4" /> : <span>×</span>}`}
                    </code>
                  </div>
                  
                  <div className="bg-purple-50 border border-purple-200 rounded p-4">
                    <h3 className="font-medium text-purple-800 mb-2">✅ الحل 3: تحسين معالجة الأخطاء</h3>
                    <code className="text-sm bg-white p-2 rounded block">
                      {`console.log('🚀 QuestionDetailView mounted');`}
                    </code>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default QuestionDetailDebugPage;
