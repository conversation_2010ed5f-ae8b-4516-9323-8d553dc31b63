import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { SubscriptionForm } from '@/components/subscription/SubscriptionForm';
import { SubscriptionDebugPanel } from '@/components/subscription/SubscriptionDebugPanel';
import { useAuth } from '@/contexts/AuthContext';
import { 
  TestTube, 
  CreditCard, 
  Bug, 
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';

export const SubscriptionTestPage: React.FC = () => {
  const { user, profile } = useAuth();

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <TestTube className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">
            صفحة اختبار نظام الاشتراكات
          </h1>
        </div>
        
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-yellow-900 mb-2">تحذير - صفحة اختبار</h3>
              <p className="text-sm text-yellow-800">
                هذه صفحة مخصصة لاختبار وتشخيص نظام الاشتراكات. 
                يجب حذفها قبل النشر في الإنتاج.
              </p>
            </div>
          </div>
        </div>

        {user && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-start gap-3">
              <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-blue-900 mb-2">معلومات المستخدم الحالي</h3>
                <div className="text-sm text-blue-800 space-y-1">
                  <p><strong>البريد الإلكتروني:</strong> {user.email}</p>
                  <p><strong>معرف المستخدم:</strong> {user.id}</p>
                  {profile && (
                    <>
                      <p><strong>الاسم:</strong> {profile.full_name || 'غير محدد'}</p>
                      <p><strong>الدور:</strong> {profile.role}</p>
                      <p><strong>مستوى الاشتراك:</strong> {profile.subscription_tier}</p>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <Tabs defaultValue="subscription" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="subscription" className="flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            نموذج الاشتراك
          </TabsTrigger>
          <TabsTrigger value="debug" className="flex items-center gap-2">
            <Bug className="h-4 w-4" />
            لوحة التشخيص
          </TabsTrigger>
        </TabsList>

        <TabsContent value="subscription" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                اختبار نموذج الاشتراك
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="font-medium text-green-900 mb-2">تعليمات الاختبار</h3>
                      <ul className="text-sm text-green-800 space-y-1">
                        <li>• جرب الاشتراك في الباقة المجانية أولاً</li>
                        <li>• تحقق من ظهور رسائل النجاح/الخطأ</li>
                        <li>• لاحظ تغيير حالة الأزرار أثناء التحميل</li>
                        <li>• تأكد من تحديث الباقة الحالية بعد الاشتراك</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {user ? (
                  <SubscriptionForm 
                    userId={user.id}
                    onSubscriptionChange={(planId) => {
                      console.log('Subscription changed to plan:', planId);
                    }}
                  />
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-600">يجب تسجيل الدخول لاختبار نموذج الاشتراك</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="debug" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bug className="h-5 w-5" />
                تشخيص النظام
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="font-medium text-blue-900 mb-2">حول لوحة التشخيص</h3>
                      <ul className="text-sm text-blue-800 space-y-1">
                        <li>• تختبر جميع وظائف نظام الاشتراكات</li>
                        <li>• تعرض تفاصيل الأخطاء إن وجدت</li>
                        <li>• تتيح اختبار الاشتراك التلقائي</li>
                        <li>• تساعد في تشخيص مشاكل قاعدة البيانات</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <SubscriptionDebugPanel />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Instructions for developers */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>تعليمات للمطورين</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium mb-2">خطوات التشخيص:</h3>
              <ol className="list-decimal list-inside space-y-1 text-sm">
                <li>تأكد من تشغيل قاعدة البيانات وتطبيق المخططات</li>
                <li>نفذ سكريبت التشخيص: <code className="bg-gray-100 px-1 rounded">SUBSCRIPTION_SYSTEM_DEBUG_FIX.sql</code></li>
                <li>استخدم لوحة التشخيص لفحص جميع الوظائف</li>
                <li>اختبر نموذج الاشتراك مع مستخدم حقيقي</li>
                <li>تحقق من سجلات وحدة التحكم للأخطاء</li>
              </ol>
            </div>

            <div>
              <h3 className="font-medium mb-2">ملفات مهمة:</h3>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li><code className="bg-gray-100 px-1 rounded">src/services/subscriptionService.ts</code> - خدمة الاشتراكات</li>
                <li><code className="bg-gray-100 px-1 rounded">src/components/subscription/SubscriptionForm.tsx</code> - نموذج الاشتراك</li>
                <li><code className="bg-gray-100 px-1 rounded">SUBSCRIPTION_SYSTEM_DEBUG_FIX.sql</code> - سكريبت إصلاح قاعدة البيانات</li>
                <li><code className="bg-gray-100 px-1 rounded">SUBSCRIPTION_TESTING_GUIDE.md</code> - دليل الاختبار الشامل</li>
              </ul>
            </div>

            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h3 className="font-medium text-red-900 mb-2">تذكير مهم</h3>
              <p className="text-sm text-red-800">
                احذف هذه الصفحة (<code>SubscriptionTestPage.tsx</code>) قبل النشر في الإنتاج!
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
