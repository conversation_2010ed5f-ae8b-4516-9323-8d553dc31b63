import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Database, Users, MessageCircle } from 'lucide-react';

interface TestResult {
  name: string;
  status: 'success' | 'error' | 'pending';
  message: string;
  details?: any;
}

const TestConnection = () => {
  const [tests, setTests] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const updateTest = (name: string, status: 'success' | 'error' | 'pending', message: string, details?: any) => {
    setTests(prev => {
      const existing = prev.find(t => t.name === name);
      if (existing) {
        return prev.map(t => t.name === name ? { name, status, message, details } : t);
      }
      return [...prev, { name, status, message, details }];
    });
  };

  const runTests = async () => {
    setIsRunning(true);
    setTests([]);

    // Test 1: Connexion de base
    updateTest('Connexion Supabase', 'pending', 'Test en cours...');
    try {
      const { data, error } = await supabase.from('profiles').select('count').limit(1);
      if (error) throw error;
      updateTest('Connexion Supabase', 'success', 'Connexion établie avec succès');
    } catch (error: any) {
      updateTest('Connexion Supabase', 'error', `Erreur: ${error.message}`);
    }

    // Test 2: Vérification des tables
    updateTest('Tables de base', 'pending', 'Vérification des tables...');
    try {
      const tables = ['profiles', 'advocates', 'legal_questions', 'responses'];
      const results = [];
      
      for (const table of tables) {
        try {
          const { data, error } = await supabase.from(table).select('*').limit(1);
          if (error) throw error;
          results.push(`✅ ${table}`);
        } catch (error: any) {
          results.push(`❌ ${table}: ${error.message}`);
        }
      }
      
      updateTest('Tables de base', 'success', 'Tables vérifiées', results);
    } catch (error: any) {
      updateTest('Tables de base', 'error', `Erreur: ${error.message}`);
    }

    // Test 3: Authentification
    updateTest('Authentification', 'pending', 'Test de l\'auth...');
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        updateTest('Authentification', 'success', `Utilisateur connecté: ${user.email}`);
      } else {
        updateTest('Authentification', 'success', 'Aucun utilisateur connecté (normal)');
      }
    } catch (error: any) {
      updateTest('Authentification', 'error', `Erreur: ${error.message}`);
    }

    // Test 4: Fonctions de base
    updateTest('Fonctions', 'pending', 'Test des fonctions...');
    try {
      // Test de la fonction check_usage_limits
      const { data, error } = await supabase.rpc('check_usage_limits', {
        user_uuid: '00000000-0000-0000-0000-000000000000',
        action_type: 'question'
      });
      
      updateTest('Fonctions', 'success', 'Fonctions accessibles');
    } catch (error: any) {
      updateTest('Fonctions', 'error', `Erreur: ${error.message}`);
    }

    // Test 5: Statistiques
    updateTest('Statistiques', 'pending', 'Récupération des stats...');
    try {
      const { count: totalUsers } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      const { count: totalAdvocates } = await supabase
        .from('advocates')
        .select('*', { count: 'exact', head: true });

      const { count: totalQuestions } = await supabase
        .from('legal_questions')
        .select('*', { count: 'exact', head: true });

      const stats = {
        users: totalUsers || 0,
        advocates: totalAdvocates || 0,
        questions: totalQuestions || 0
      };

      updateTest('Statistiques', 'success', 'Statistiques récupérées', stats);
    } catch (error: any) {
      updateTest('Statistiques', 'error', `Erreur: ${error.message}`);
    }

    setIsRunning(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <div className="h-5 w-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-100 text-green-800">Succès</Badge>;
      case 'error':
        return <Badge variant="destructive">Erreur</Badge>;
      default:
        return <Badge variant="secondary">En cours</Badge>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Test de Connexion Supabase
          </h1>
          <p className="text-lg text-gray-600">
            Vérification de la connexion et des fonctionnalités de base
          </p>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Configuration Actuelle
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>URL Supabase:</strong> https://ebwyerwbifowpyukefqv.supabase.co</p>
              <p><strong>Projet ID:</strong> ebwyerwbifowpyukefqv</p>
              <p><strong>Status:</strong> <Badge className="bg-blue-100 text-blue-800">Configuré</Badge></p>
            </div>
          </CardContent>
        </Card>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Tests de Connexion</CardTitle>
            <CardDescription>
              Cliquez sur "Lancer les Tests" pour vérifier la connexion
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={runTests} 
              disabled={isRunning}
              className="mb-4"
            >
              {isRunning ? 'Tests en cours...' : 'Lancer les Tests'}
            </Button>

            <div className="space-y-4">
              {tests.map((test, index) => (
                <div key={index} className="flex items-start justify-between p-4 border rounded-lg">
                  <div className="flex items-start gap-3">
                    {getStatusIcon(test.status)}
                    <div>
                      <h3 className="font-medium">{test.name}</h3>
                      <p className="text-sm text-gray-600">{test.message}</p>
                      {test.details && (
                        <div className="mt-2 text-xs bg-gray-100 p-2 rounded">
                          {Array.isArray(test.details) ? (
                            test.details.map((detail, i) => (
                              <div key={i}>{detail}</div>
                            ))
                          ) : (
                            <pre>{JSON.stringify(test.details, null, 2)}</pre>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                  {getStatusBadge(test.status)}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {tests.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Résumé</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {tests.filter(t => t.status === 'success').length}
                  </div>
                  <div className="text-sm text-gray-600">Succès</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {tests.filter(t => t.status === 'error').length}
                  </div>
                  <div className="text-sm text-gray-600">Erreurs</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {tests.filter(t => t.status === 'pending').length}
                  </div>
                  <div className="text-sm text-gray-600">En cours</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default TestConnection;
