import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import { Navigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { QuestionsList } from '@/components/client/QuestionsList';
import { QuestionCreationForm } from '@/components/questions/QuestionCreationForm';
import { LegalQuestion } from '@/services/questionsService';
import { 
  MessageCircle, 
  Plus, 
  HelpCircle,
  CheckCircle,
  Clock,
  Star,
  Award
} from 'lucide-react';

const ClientQuestionsEnhanced = () => {
  const { user, profile, loading } = useAuth();
  const { t } = useTranslation();
  const { toast } = useToast();
  const [showCreateForm, setShowCreateForm] = useState(false);

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Redirect if not authenticated
  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  // Redirect if not a regular user
  if (profile?.role !== 'user') {
    return <Navigate to="/" replace />;
  }

  const handleQuestionCreated = (newQuestion: LegalQuestion) => {
    setShowCreateForm(false);
    toast({
      title: 'تم إرسال السؤال بنجاح',
      description: 'سيتم الرد على سؤالك من قبل محامين مختصين قريباً',
    });
    // The QuestionsList component will automatically refresh
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              الأسئلة القانونية
            </h1>
            <p className="text-lg text-gray-600 mb-6">
              اطرح أسئلتك القانونية واحصل على إجابات من محامين مختصين
            </p>
            
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
              <Card>
                <CardContent className="p-4 text-center">
                  <MessageCircle className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">24/7</div>
                  <div className="text-sm text-gray-600">خدمة متاحة</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4 text-center">
                  <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">محامين</div>
                  <div className="text-sm text-gray-600">مختصين ومعتمدين</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4 text-center">
                  <Clock className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">سريع</div>
                  <div className="text-sm text-gray-600">رد خلال 24 ساعة</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4 text-center">
                  <Star className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">جودة</div>
                  <div className="text-sm text-gray-600">إجابات موثوقة</div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <QuestionsList 
          userId={user.id} 
          onCreateQuestion={() => setShowCreateForm(true)}
        />
      </div>

      {/* Question Creation Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <QuestionCreationForm
            onQuestionCreated={handleQuestionCreated}
            onClose={() => setShowCreateForm(false)}
            userId={user.id}
          />
        </div>
      )}

      {/* Help Section */}
      <div className="bg-blue-50 border-t">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <HelpCircle className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              كيف يعمل النظام؟
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="bg-blue-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  <span className="text-blue-600 font-bold">1</span>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">اطرح سؤالك</h3>
                <p className="text-gray-600 text-sm">
                  اكتب سؤالك القانوني بوضوح واختر التصنيف المناسب
                </p>
              </div>
              
              <div className="text-center">
                <div className="bg-blue-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  <span className="text-blue-600 font-bold">2</span>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">احصل على إجابة</h3>
                <p className="text-gray-600 text-sm">
                  سيقوم محامي مختص بالرد على سؤالك خلال 24 ساعة
                </p>
              </div>
              
              <div className="text-center">
                <div className="bg-blue-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  <span className="text-blue-600 font-bold">3</span>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">قيم الإجابة</h3>
                <p className="text-gray-600 text-sm">
                  قيم جودة الإجابة لمساعدة المحامين الآخرين
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tips Section */}
      <div className="bg-white border-t">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-8">
            <Award className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              نصائح للحصول على أفضل إجابة
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-2">كن واضحاً ومحدداً</h3>
                <p className="text-gray-600 text-sm">
                  اشرح مشكلتك بوضوح واذكر جميع التفاصيل المهمة
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-2">اختر التصنيف الصحيح</h3>
                <p className="text-gray-600 text-sm">
                  اختر التصنيف المناسب لسؤالك للحصول على محامي مختص
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-2">اذكر الموقع الجغرافي</h3>
                <p className="text-gray-600 text-sm">
                  القوانين تختلف حسب المنطقة، لذا اذكر موقعك إذا كان مهماً
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-2">تجنب المعلومات الشخصية</h3>
                <p className="text-gray-600 text-sm">
                  لا تذكر أسماء أو معلومات شخصية حساسة في سؤالك
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-2">كن صبوراً</h3>
                <p className="text-gray-600 text-sm">
                  الإجابات الجيدة تحتاج وقت، انتظر حتى 24 ساعة للحصول على رد
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-2">قيم الإجابات</h3>
                <p className="text-gray-600 text-sm">
                  تقييمك يساعد المحامين على تحسين خدماتهم ويساعد المستخدمين الآخرين
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientQuestionsEnhanced;
