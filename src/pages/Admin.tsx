import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import { Navigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { Loading, LoadingGrid } from '@/components/ui/loading';
import { AdminFunctionAlert } from '@/components/admin/AdminFunctionAlert';
import { 
  Users, 
  FileText, 
  MessageCircle, 
  TrendingUp,
  UserCheck,
  UserX,
  Crown,
  Shield,
  DollarSign,
  Calendar,
  BarChart3
} from 'lucide-react';

interface AdminStats {
  totalUsers: number;
  totalAdvocates: number;
  totalDocuments: number;
  totalQuestions: number;
  activeSubscriptions: number;
  monthlyRevenue: number;
  newUsersThisMonth: number;
  documentsThisMonth: number;
}

interface User {
  id: string;
  email: string;
  full_name: string | null;
  role: 'admin' | 'user' | 'advocate';
  subscription_tier: 'free' | 'pro_user' | 'pro_advocate';
  is_verified: boolean | null;
  created_at: string;
  subscription_end: string | null;
  trial_end: string | null;
}

const Admin = () => {
  const { user, profile, loading } = useAuth();
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    totalAdvocates: 0,
    totalDocuments: 0,
    totalQuestions: 0,
    activeSubscriptions: 0,
    monthlyRevenue: 0,
    newUsersThisMonth: 0,
    documentsThisMonth: 0
  });
  const [users, setUsers] = useState<User[]>([]);
  const [isLoadingStats, setIsLoadingStats] = useState(true);
  const [isLoadingUsers, setIsLoadingUsers] = useState(true);
  const currentLanguage = i18n.language as 'ar' | 'fr';

  useEffect(() => {
    if (user && profile?.role === 'admin') {
      loadStats();
      loadUsers();
    }
  }, [user, profile]);

  const loadStats = async () => {
    try {
      setIsLoadingStats(true);
      
      // Get total users
      const { count: totalUsers } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      // Get total advocates
      const { count: totalAdvocates } = await supabase
        .from('advocates')
        .select('*', { count: 'exact', head: true });

      // Get total documents
      const { count: totalDocuments } = await supabase
        .from('legal_documents')
        .select('*', { count: 'exact', head: true });

      // Get total questions
      const { count: totalQuestions } = await supabase
        .from('legal_questions')
        .select('*', { count: 'exact', head: true });

      // Get active subscriptions
      const { count: activeSubscriptions } = await supabase
        .from('subscriptions')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'active');

      // Get new users this month
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      const { count: newUsersThisMonth } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', startOfMonth.toISOString());

      // Get documents this month
      const { count: documentsThisMonth } = await supabase
        .from('legal_documents')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', startOfMonth.toISOString());

      setStats({
        totalUsers: totalUsers || 0,
        totalAdvocates: totalAdvocates || 0,
        totalDocuments: totalDocuments || 0,
        totalQuestions: totalQuestions || 0,
        activeSubscriptions: activeSubscriptions || 0,
        monthlyRevenue: (activeSubscriptions || 0) * 100, // Simplified calculation
        newUsersThisMonth: newUsersThisMonth || 0,
        documentsThisMonth: documentsThisMonth || 0
      });
    } catch (error) {
      console.error('Error loading stats:', error);
      toast({
        title: currentLanguage === 'ar' ? 'خطأ' : 'Erreur',
        description: currentLanguage === 'ar' 
          ? 'فشل في تحميل الإحصائيات' 
          : 'Échec du chargement des statistiques',
        variant: 'destructive'
      });
    } finally {
      setIsLoadingStats(false);
    }
  };

  const loadUsers = async () => {
    try {
      setIsLoadingUsers(true);
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error('Error loading users:', error);
      toast({
        title: currentLanguage === 'ar' ? 'خطأ' : 'Erreur',
        description: currentLanguage === 'ar' 
          ? 'فشل في تحميل المستخدمين' 
          : 'Échec du chargement des utilisateurs',
        variant: 'destructive'
      });
    } finally {
      setIsLoadingUsers(false);
    }
  };

  const updateUserRole = async (userId: string, newRole: 'admin' | 'user' | 'advocate') => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ role: newRole })
        .eq('id', userId);

      if (error) throw error;

      setUsers(users.map(user => 
        user.id === userId ? { ...user, role: newRole } : user
      ));

      toast({
        title: currentLanguage === 'ar' ? 'تم بنجاح' : 'Succès',
        description: currentLanguage === 'ar' 
          ? 'تم تحديث دور المستخدم' 
          : 'Rôle utilisateur mis à jour'
      });
    } catch (error) {
      console.error('Error updating user role:', error);
      toast({
        title: currentLanguage === 'ar' ? 'خطأ' : 'Erreur',
        description: currentLanguage === 'ar' 
          ? 'فشل في تحديث دور المستخدم' 
          : 'Échec de la mise à jour du rôle',
        variant: 'destructive'
      });
    }
  };

  const toggleUserVerification = async (userId: string, isVerified: boolean) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ is_verified: !isVerified })
        .eq('id', userId);

      if (error) throw error;

      setUsers(users.map(user => 
        user.id === userId ? { ...user, is_verified: !isVerified } : user
      ));

      toast({
        title: currentLanguage === 'ar' ? 'تم بنجاح' : 'Succès',
        description: currentLanguage === 'ar' 
          ? 'تم تحديث حالة التحقق' 
          : 'Statut de vérification mis à jour'
      });
    } catch (error) {
      console.error('Error updating verification:', error);
      toast({
        title: currentLanguage === 'ar' ? 'خطأ' : 'Erreur',
        description: currentLanguage === 'ar' 
          ? 'فشل في تحديث حالة التحقق' 
          : 'Échec de la mise à jour de la vérification',
        variant: 'destructive'
      });
    }
  };

  const getRoleBadge = (role: string) => {
    const roleConfig = {
      admin: { 
        label: currentLanguage === 'ar' ? 'مدير' : 'Admin', 
        variant: 'destructive' as const,
        icon: Shield
      },
      advocate: { 
        label: currentLanguage === 'ar' ? 'محامي' : 'Avocat', 
        variant: 'default' as const,
        icon: Crown
      },
      user: { 
        label: currentLanguage === 'ar' ? 'مستخدم' : 'Utilisateur', 
        variant: 'secondary' as const,
        icon: Users
      }
    };

    const config = roleConfig[role as keyof typeof roleConfig] || roleConfig.user;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getSubscriptionBadge = (tier: string) => {
    const tierConfig = {
      free: { 
        label: currentLanguage === 'ar' ? 'مجاني' : 'Gratuit', 
        variant: 'outline' as const 
      },
      pro_user: { 
        label: currentLanguage === 'ar' ? 'مستخدم متميز' : 'Pro Utilisateur', 
        variant: 'default' as const 
      },
      pro_advocate: { 
        label: currentLanguage === 'ar' ? 'محامي متميز' : 'Pro Avocat', 
        variant: 'secondary' as const 
      }
    };

    const config = tierConfig[tier as keyof typeof tierConfig] || tierConfig.free;

    return (
      <Badge variant={config.variant}>
        {config.label}
      </Badge>
    );
  };

  if (loading && !user) {
    return <Loading fullScreen text={t('common.loading')} />;
  }

  if (!user || profile?.role !== 'admin') {
    return <Navigate to="/" replace />;
  }

  const statsCards = [
    {
      title: currentLanguage === 'ar' ? 'إجمالي المستخدمين' : 'Total Utilisateurs',
      value: stats.totalUsers,
      icon: Users,
      color: 'text-blue-600'
    },
    {
      title: currentLanguage === 'ar' ? 'المحامون' : 'Avocats',
      value: stats.totalAdvocates,
      icon: Crown,
      color: 'text-purple-600'
    },
    {
      title: currentLanguage === 'ar' ? 'الوثائق' : 'Documents',
      value: stats.totalDocuments,
      icon: FileText,
      color: 'text-green-600'
    },
    {
      title: currentLanguage === 'ar' ? 'الأسئلة' : 'Questions',
      value: stats.totalQuestions,
      icon: MessageCircle,
      color: 'text-orange-600'
    },
    {
      title: currentLanguage === 'ar' ? 'الاشتراكات النشطة' : 'Abonnements Actifs',
      value: stats.activeSubscriptions,
      icon: TrendingUp,
      color: 'text-indigo-600'
    },
    {
      title: currentLanguage === 'ar' ? 'الإيرادات الشهرية' : 'Revenus Mensuels',
      value: `${stats.monthlyRevenue} MAD`,
      icon: DollarSign,
      color: 'text-emerald-600'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {currentLanguage === 'ar' ? 'لوحة تحكم المدير' : 'Tableau de Bord Admin'}
          </h1>
          <p className="text-lg text-gray-600">
            {currentLanguage === 'ar'
              ? 'إدارة المستخدمين والنظام'
              : 'Gestion des utilisateurs et du système'
            }
          </p>
        </div>

        {/* Admin Function Status Alert */}
        <AdminFunctionAlert />

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">
            {currentLanguage === 'ar' ? 'نظرة عامة' : 'Aperçu'}
          </TabsTrigger>
          <TabsTrigger value="users">
            {currentLanguage === 'ar' ? 'المستخدمون' : 'Utilisateurs'}
          </TabsTrigger>
          <TabsTrigger value="analytics">
            {currentLanguage === 'ar' ? 'التحليلات' : 'Analytics'}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
            {statsCards.map((card, index) => {
              const Icon = card.icon;
              return (
                <Card key={index}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      {card.title}
                    </CardTitle>
                    <Icon className={`h-4 w-4 ${card.color}`} />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {isLoadingStats ? '...' : card.value}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  {currentLanguage === 'ar' ? 'إحصائيات هذا الشهر' : 'Statistiques du mois'}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>{currentLanguage === 'ar' ? 'مستخدمون جدد' : 'Nouveaux utilisateurs'}</span>
                  <Badge variant="outline">{stats.newUsersThisMonth}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>{currentLanguage === 'ar' ? 'وثائق جديدة' : 'Nouveaux documents'}</span>
                  <Badge variant="outline">{stats.documentsThisMonth}</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  {currentLanguage === 'ar' ? 'إجراءات سريعة' : 'Actions rapides'}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" className="w-full justify-start" onClick={loadStats}>
                  <TrendingUp className="mr-2 h-4 w-4" />
                  {currentLanguage === 'ar' ? 'تحديث الإحصائيات' : 'Actualiser les stats'}
                </Button>
                <Button variant="outline" className="w-full justify-start" onClick={loadUsers}>
                  <Users className="mr-2 h-4 w-4" />
                  {currentLanguage === 'ar' ? 'تحديث المستخدمين' : 'Actualiser les utilisateurs'}
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="users" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>
                {currentLanguage === 'ar' ? 'إدارة المستخدمين' : 'Gestion des utilisateurs'}
              </CardTitle>
              <CardDescription>
                {currentLanguage === 'ar' 
                  ? 'عرض وإدارة جميع المستخدمين في النظام' 
                  : 'Voir et gérer tous les utilisateurs du système'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingUsers ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p>{currentLanguage === 'ar' ? 'جاري التحميل...' : 'Chargement...'}</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {users.map((user) => (
                    <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-medium">{user.full_name || user.email}</h3>
                          {user.is_verified ? (
                            <UserCheck className="h-4 w-4 text-green-600" />
                          ) : (
                            <UserX className="h-4 w-4 text-red-600" />
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{user.email}</p>
                        <div className="flex gap-2">
                          {getRoleBadge(user.role)}
                          {getSubscriptionBadge(user.subscription_tier)}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toggleUserVerification(user.id, user.is_verified || false)}
                        >
                          {user.is_verified 
                            ? (currentLanguage === 'ar' ? 'إلغاء التحقق' : 'Déverifier')
                            : (currentLanguage === 'ar' ? 'تحقق' : 'Vérifier')
                          }
                        </Button>
                        <select
                          value={user.role}
                          onChange={(e) => updateUserRole(user.id, e.target.value as any)}
                          className="px-3 py-1 border rounded text-sm"
                        >
                          <option value="user">
                            {currentLanguage === 'ar' ? 'مستخدم' : 'Utilisateur'}
                          </option>
                          <option value="advocate">
                            {currentLanguage === 'ar' ? 'محامي' : 'Avocat'}
                          </option>
                          <option value="admin">
                            {currentLanguage === 'ar' ? 'مدير' : 'Admin'}
                          </option>
                        </select>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>
                {currentLanguage === 'ar' ? 'التحليلات المتقدمة' : 'Analytics Avancées'}
              </CardTitle>
              <CardDescription>
                {currentLanguage === 'ar' 
                  ? 'تحليلات مفصلة لاستخدام النظام' 
                  : 'Analyses détaillées de l\'utilisation du système'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-3">
                    {currentLanguage === 'ar' ? 'إحصائيات المحامين' : 'Statistiques Avocats'}
                  </h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                      <span>{currentLanguage === 'ar' ? 'محامون مفعلون' : 'Avocats vérifiés'}</span>
                      <Badge variant="default">
                        {users.filter(u => u.role === 'advocate' && u.is_verified).length}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                      <span>{currentLanguage === 'ar' ? 'في انتظار التفعيل' : 'En attente'}</span>
                      <Badge variant="secondary">
                        {users.filter(u => u.role === 'advocate' && !u.is_verified).length}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                      <span>{currentLanguage === 'ar' ? 'معدل الاستجابة' : 'Taux de réponse'}</span>
                      <Badge variant="outline">85%</Badge>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-3">
                    {currentLanguage === 'ar' ? 'الاشتراكات والإيرادات' : 'Abonnements & Revenus'}
                  </h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                      <span>{currentLanguage === 'ar' ? 'اشتراكات مدفوعة' : 'Abonnements payants'}</span>
                      <Badge variant="default">
                        {users.filter(u => u.subscription_tier !== 'free').length}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                      <span>{currentLanguage === 'ar' ? 'الإيرادات الشهرية' : 'Revenus mensuels'}</span>
                      <Badge variant="secondary">{stats.monthlyRevenue} MAD</Badge>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                      <span>{currentLanguage === 'ar' ? 'نمو هذا الشهر' : 'Croissance ce mois'}</span>
                      <Badge variant="outline">+{stats.newUsersThisMonth}</Badge>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{stats.totalQuestions}</div>
                  <div className="text-sm text-gray-600">
                    {currentLanguage === 'ar' ? 'إجمالي الأسئلة' : 'Total Questions'}
                  </div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{stats.totalDocuments}</div>
                  <div className="text-sm text-gray-600">
                    {currentLanguage === 'ar' ? 'الوثائق المنشأة' : 'Documents créés'}
                  </div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">{stats.activeSubscriptions}</div>
                  <div className="text-sm text-gray-600">
                    {currentLanguage === 'ar' ? 'اشتراكات نشطة' : 'Abonnements actifs'}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      </div>
    </div>
  );
};

export default Admin;
