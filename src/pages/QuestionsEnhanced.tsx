import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import { Navigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { QuestionCreationForm } from '@/components/questions/QuestionCreationForm';
import { QuestionsService, LegalQuestion, QuestionResponse } from '@/services/questionsService';
import { Loading } from '@/components/ui/loading';
import {
  MessageCircle,
  Plus,
  Clock,
  CheckCircle,
  User,
  Search,
  Filter,
  AlertCircle,
  Star,
  Calendar,
  FileText,
  Loader2,
  Eye,
  RefreshCw
} from 'lucide-react';

const QuestionsEnhanced = () => {
  const { user, profile, loading } = useAuth();
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const currentLanguage = i18n.language as 'ar' | 'fr';

  // State management
  const [questions, setQuestions] = useState<LegalQuestion[]>([]);
  const [responses, setResponses] = useState<{ [key: string]: QuestionResponse[] }>({});
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState<LegalQuestion | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');

  useEffect(() => {
    if (user) {
      fetchQuestions();
    }
  }, [user]);

  const fetchQuestions = async () => {
    if (!user) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      console.log('🔍 Fetching questions for user:', user.id);

      const { data, error } = await QuestionsService.getUserQuestions(user.id);

      if (error) {
        throw error;
      }

      console.log('✅ Questions fetched successfully:', data?.length || 0);
      setQuestions(data || []);

      // Fetch responses for each question
      if (data && data.length > 0) {
        console.log('🔍 Fetching responses for questions...');
        const responsesPromises = data.map(async (question) => {
          try {
            const { data: responsesData } = await QuestionsService.getQuestionResponses(question.id);
            return { questionId: question.id, responses: responsesData || [] };
          } catch (error) {
            console.error('⚠️ Error fetching responses for question:', question.id, error);
            return { questionId: question.id, responses: [] };
          }
        });

        const responsesResults = await Promise.all(responsesPromises);
        const responsesMap: { [key: string]: QuestionResponse[] } = {};
        responsesResults.forEach(({ questionId, responses }) => {
          responsesMap[questionId] = responses;
        });
        setResponses(responsesMap);
      }
    } catch (error: any) {
      console.error('❌ Error in fetchQuestions:', error);
      toast({
        title: 'خطأ في تحميل الأسئلة',
        description: error.message || 'فشل في تحميل الأسئلة',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchQuestions();
    setRefreshing(false);
    toast({
      title: 'تم تحديث الأسئلة',
      description: 'تم تحديث قائمة الأسئلة بنجاح',
    });
  };

  const handleQuestionCreated = (newQuestion: LegalQuestion) => {
    setQuestions(prev => [newQuestion, ...prev]);
    toast({
      title: 'تم إرسال السؤال بنجاح',
      description: 'سيتم الرد على سؤالك قريباً',
    });
  };

  const getFilteredQuestions = () => {
    return questions.filter(question => {
      const matchesSearch = question.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           question.description.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = statusFilter === 'all' || 
                           (statusFilter === 'answered' && question.is_answered) ||
                           (statusFilter === 'pending' && !question.is_answered);
      
      const matchesCategory = categoryFilter === 'all' || question.category === categoryFilter;
      
      return matchesSearch && matchesStatus && matchesCategory;
    });
  };

  const getStatusBadge = (question: LegalQuestion) => {
    if (question.is_answered) {
      return (
        <Badge variant="default" className="flex items-center gap-1">
          <CheckCircle className="h-3 w-3" />
          تم الرد
        </Badge>
      );
    } else {
      return (
        <Badge variant="secondary" className="flex items-center gap-1">
          <Clock className="h-3 w-3" />
          في الانتظار
        </Badge>
      );
    }
  };

  const getCategoryLabel = (category: string) => {
    const categories: Record<string, string> = {
      family: 'قانون الأسرة',
      commercial: 'القانون التجاري',
      criminal: 'القانون الجنائي',
      civil: 'القانون المدني',
      labor: 'قانون العمل',
      real_estate: 'قانون العقارات',
      tax: 'القانون الضريبي',
      administrative: 'القانون الإداري',
      intellectual_property: 'الملكية الفكرية',
      general: 'عام'
    };
    return categories[category] || category;
  };

  if (loading && !user) {
    return <Loading fullScreen text="جاري التحميل..." />;
  }

  if (!user) {
    return <Navigate to="/" replace />;
  }

  const filteredQuestions = getFilteredQuestions();

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                الأسئلة القانونية
              </h1>
              <p className="text-lg text-gray-600">
                اطرح أسئلتك القانونية واحصل على إجابات من محامين مختصين
              </p>
            </div>
            <div className="flex gap-2">
              <Button 
                variant="outline"
                onClick={handleRefresh}
                disabled={refreshing}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                تحديث
              </Button>
              <Button 
                onClick={() => setShowCreateForm(true)}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                سؤال جديد
              </Button>
            </div>
          </div>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="البحث في الأسئلة..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="answered">تم الرد</SelectItem>
                  <SelectItem value="pending">في الانتظار</SelectItem>
                </SelectContent>
              </Select>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="التصنيف" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع التصنيفات</SelectItem>
                  <SelectItem value="family">قانون الأسرة</SelectItem>
                  <SelectItem value="commercial">القانون التجاري</SelectItem>
                  <SelectItem value="criminal">القانون الجنائي</SelectItem>
                  <SelectItem value="civil">القانون المدني</SelectItem>
                  <SelectItem value="labor">قانون العمل</SelectItem>
                  <SelectItem value="real_estate">قانون العقارات</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Questions List */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="mr-2">جاري تحميل الأسئلة...</span>
          </div>
        ) : filteredQuestions.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <MessageCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {questions.length === 0 ? 'لا توجد أسئلة بعد' : 'لا توجد أسئلة مطابقة للفلاتر'}
              </h3>
              <p className="text-gray-600 mb-4">
                {questions.length === 0 ? 'ابدأ بطرح سؤالك الأول' : 'جرب تغيير الفلاتر أو البحث'}
              </p>
              {questions.length === 0 && (
                <Button onClick={() => setShowCreateForm(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  طرح سؤال جديد
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {filteredQuestions.map((question) => (
              <Card key={question.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <CardTitle className="text-xl mb-2">{question.title}</CardTitle>
                      <CardDescription className="text-base leading-relaxed">
                        {question.description.length > 200 
                          ? `${question.description.substring(0, 200)}...` 
                          : question.description
                        }
                      </CardDescription>
                    </div>
                    <div className="flex flex-col gap-2 ml-4">
                      {getStatusBadge(question)}
                      <Badge variant="outline">
                        {getCategoryLabel(question.category || 'general')}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center text-sm text-gray-500 mb-4">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>{new Date(question.created_at).toLocaleDateString('ar-EG')}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <FileText className="h-3 w-3" />
                        <span>الحالة: {question.status}</span>
                      </div>
                    </div>
                    {responses[question.id] && responses[question.id].length > 0 && (
                      <div className="flex items-center gap-1">
                        <MessageCircle className="h-3 w-3" />
                        <span>{responses[question.id].length} رد</span>
                      </div>
                    )}
                  </div>

                  {/* Display responses */}
                  {responses[question.id] && responses[question.id].length > 0 && (
                    <div className="mt-4 border-t pt-4">
                      <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                        <MessageCircle className="h-4 w-4" />
                        الردود ({responses[question.id].length})
                      </h4>
                      <div className="space-y-3">
                        {responses[question.id].slice(0, 2).map((response) => (
                          <div key={response.id} className="bg-gray-50 p-4 rounded-lg">
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <User className="h-4 w-4 text-gray-500" />
                                <span className="text-sm font-medium text-gray-700">
                                  {response.advocate_profile?.full_name || 'محامي'}
                                </span>
                                <div className="flex items-center gap-1">
                                  <Star className="h-3 w-3 text-yellow-500" />
                                  <span className="text-xs text-gray-500">4.8</span>
                                </div>
                              </div>
                              <span className="text-xs text-gray-500">
                                {new Date(response.created_at).toLocaleDateString('ar-EG')}
                              </span>
                            </div>
                            <p className="text-gray-800 leading-relaxed">
                              {response.response_text.length > 150 
                                ? `${response.response_text.substring(0, 150)}...` 
                                : response.response_text
                              }
                            </p>
                          </div>
                        ))}
                        {responses[question.id].length > 2 && (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setSelectedQuestion(question)}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            عرض جميع الردود ({responses[question.id].length})
                          </Button>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Action buttons */}
                  <div className="mt-4 flex justify-end gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedQuestion(question)}
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      عرض التفاصيل
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Question Creation Modal */}
        {showCreateForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <QuestionCreationForm
              onQuestionCreated={handleQuestionCreated}
              onClose={() => setShowCreateForm(false)}
              userId={user.id}
            />
          </div>
        )}

        {/* Question Details Modal */}
        {selectedQuestion && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>{selectedQuestion.title}</CardTitle>
                  <Button variant="ghost" size="sm" onClick={() => setSelectedQuestion(null)}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <CardDescription className="text-base">
                  {selectedQuestion.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex gap-2">
                    {getStatusBadge(selectedQuestion)}
                    <Badge variant="outline">
                      {getCategoryLabel(selectedQuestion.category || 'general')}
                    </Badge>
                  </div>
                  
                  {responses[selectedQuestion.id] && responses[selectedQuestion.id].length > 0 ? (
                    <div>
                      <h4 className="font-medium mb-3">الردود:</h4>
                      <div className="space-y-4">
                        {responses[selectedQuestion.id].map((response) => (
                          <div key={response.id} className="border p-4 rounded-lg">
                            <div className="flex justify-between items-start mb-2">
                              <div className="flex items-center gap-2">
                                <User className="h-4 w-4 text-gray-500" />
                                <span className="font-medium">
                                  {response.advocate_profile?.full_name || 'محامي'}
                                </span>
                                <div className="flex items-center gap-1">
                                  <Star className="h-3 w-3 text-yellow-500" />
                                  <span className="text-sm text-gray-500">4.8</span>
                                </div>
                              </div>
                              <span className="text-sm text-gray-500">
                                {new Date(response.created_at).toLocaleDateString('ar-EG')}
                              </span>
                            </div>
                            <p className="text-gray-800 leading-relaxed">{response.response_text}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <AlertCircle className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                      <p>لم يتم الرد على هذا السؤال بعد</p>
                      <p className="text-sm">سيتم إشعارك عند وصول رد من المحامي</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default QuestionsEnhanced;
