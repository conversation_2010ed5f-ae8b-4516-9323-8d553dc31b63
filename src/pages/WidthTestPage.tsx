import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Navigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { WidthTestComponent } from '@/components/client/WidthTestComponent';
import { QuestionsList } from '@/components/client/QuestionsList';
import ClientQuestionsEnhanced from './ClientQuestionsEnhanced';
import { 
  TestTube, 
  Layout, 
  Monitor,
  Smartphone,
  Tablet,
  Eye,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';

const WidthTestPage = () => {
  const { user, profile, loading } = useAuth();
  const [activeTest, setActiveTest] = useState('overview');

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Redirect if not authenticated
  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  const testResults = [
    {
      component: 'QuestionDetailView',
      status: 'fixed',
      issue: 'Modal limited to max-w-4xl',
      solution: 'Responsive margins with max-w-none',
      improvement: '+40% width usage on large screens'
    },
    {
      component: 'ClientQuestionsEnhanced',
      status: 'fixed', 
      issue: 'Container limited to max-w-7xl',
      solution: 'Full width with w-full',
      improvement: '+25% content area'
    },
    {
      component: 'QuestionsList',
      status: 'fixed',
      issue: 'Single column layout only',
      solution: 'Responsive grid: 1→2→3 columns',
      improvement: '3x more content visible'
    },
    {
      component: 'Filters Grid',
      status: 'optimized',
      issue: 'Underutilized space on large screens',
      solution: 'Better responsive breakpoints',
      improvement: 'Improved filter accessibility'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center gap-3">
            <TestTube className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                اختبار تحسينات العرض والاستجابة
              </h1>
              <p className="text-gray-600">
                صفحة اختبار للتحقق من استخدام العرض الكامل للشاشة
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTest} onValueChange={setActiveTest} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <Layout className="h-4 w-4" />
              <span className="hidden sm:inline">نظرة عامة</span>
              <span className="sm:hidden">عامة</span>
            </TabsTrigger>
            <TabsTrigger value="width-test" className="flex items-center gap-2">
              <Monitor className="h-4 w-4" />
              <span className="hidden sm:inline">اختبار العرض</span>
              <span className="sm:hidden">عرض</span>
            </TabsTrigger>
            <TabsTrigger value="questions-list" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              <span className="hidden sm:inline">قائمة الأسئلة</span>
              <span className="sm:hidden">أسئلة</span>
            </TabsTrigger>
            <TabsTrigger value="full-page" className="flex items-center gap-2">
              <Smartphone className="h-4 w-4" />
              <span className="hidden sm:inline">الصفحة الكاملة</span>
              <span className="sm:hidden">كاملة</span>
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  نتائج التحسينات
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {testResults.map((result, index) => (
                    <Card key={index} className="border-l-4 border-l-green-500">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-medium">{result.component}</h3>
                          <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                            {result.status === 'fixed' ? 'تم الإصلاح' : 'محسن'}
                          </span>
                        </div>
                        <div className="space-y-2 text-sm">
                          <div>
                            <span className="text-red-600">المشكلة:</span>
                            <span className="text-gray-700 mr-2">{result.issue}</span>
                          </div>
                          <div>
                            <span className="text-blue-600">الحل:</span>
                            <span className="text-gray-700 mr-2">{result.solution}</span>
                          </div>
                          <div>
                            <span className="text-green-600">التحسن:</span>
                            <span className="text-gray-700 mr-2">{result.improvement}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Screen Size Indicator */}
            <Card>
              <CardHeader>
                <CardTitle>معلومات الشاشة الحالية</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded">
                    <Monitor className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                    <div className="text-lg font-bold">{window.innerWidth}px</div>
                    <div className="text-sm text-gray-600">العرض الحالي</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded">
                    <Tablet className="h-8 w-8 mx-auto mb-2 text-green-600" />
                    <div className="text-lg font-bold">
                      {window.innerWidth < 640 ? 'Mobile' : 
                       window.innerWidth < 768 ? 'Tablet' :
                       window.innerWidth < 1024 ? 'Desktop S' :
                       window.innerWidth < 1280 ? 'Desktop M' :
                       window.innerWidth < 1536 ? 'Desktop L' : 'Desktop XL'}
                    </div>
                    <div className="text-sm text-gray-600">نوع الجهاز</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded">
                    <Layout className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                    <div className="text-lg font-bold">
                      {window.innerWidth >= 1536 ? '3' :
                       window.innerWidth >= 1280 ? '2' : '1'}
                    </div>
                    <div className="text-sm text-gray-600">أعمدة الأسئلة</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Width Test Tab */}
          <TabsContent value="width-test">
            <WidthTestComponent userId={user.id} />
          </TabsContent>

          {/* Questions List Tab */}
          <TabsContent value="questions-list">
            <Card>
              <CardHeader>
                <CardTitle>اختبار قائمة الأسئلة المحسنة</CardTitle>
              </CardHeader>
              <CardContent>
                <QuestionsList 
                  userId={user.id}
                  onCreateQuestion={() => alert('سيتم فتح نموذج إنشاء سؤال جديد')}
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Full Page Tab */}
          <TabsContent value="full-page">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-orange-600" />
                  معاينة الصفحة الكاملة
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-gray-600">
                    هذا القسم يعرض الصفحة الكاملة للأسئلة القانونية مع جميع التحسينات المطبقة.
                  </p>
                  
                  <div className="bg-yellow-50 border border-yellow-200 rounded p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      <span className="font-medium text-yellow-800">ملاحظة</span>
                    </div>
                    <p className="text-yellow-700 text-sm">
                      الصفحة الكاملة ستفتح في نافذة جديدة للحصول على تجربة كاملة
                    </p>
                  </div>
                  
                  <Button 
                    onClick={() => window.open('/questions', '_blank')}
                    className="w-full"
                  >
                    فتح الصفحة الكاملة في نافذة جديدة
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default WidthTestPage;
