import { supabase } from '@/lib/supabase';

export interface SubscriptionPlan {
  id: string;
  name: string;
  name_ar: string;
  description: string;
  description_ar: string;
  price: number;
  currency: string;
  billing_period: 'monthly' | 'yearly';
  features: string[];
  features_ar: string[];
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface UserSubscription {
  id: string;
  user_id: string;
  plan_id: string;
  status: 'active' | 'inactive' | 'cancelled' | 'expired';
  start_date: string;
  end_date?: string;
  auto_renew: boolean;
  payment_method?: string;
  last_payment_date?: string;
  next_payment_date?: string;
  created_at: string;
  updated_at: string;
}

export interface SubscriptionPreferences {
  id: string;
  user_id: string;
  email_notifications: boolean;
  sms_notifications: boolean;
  in_app_notifications: boolean;
  legal_updates: boolean;
  question_responses: boolean;
  system_announcements: boolean;
  marketing_emails: boolean;
  weekly_digest: boolean;
  phone_number?: string;
  preferred_language: string;
  created_at: string;
  updated_at: string;
}

export interface SubscriptionAnalytics {
  id: string;
  date: string;
  total_subscribers: number;
  new_subscribers: number;
  cancelled_subscribers: number;
  active_subscribers: number;
  revenue: number;
  plan_breakdown: Record<string, number>;
  created_at: string;
}

export interface SubscriptionDetails {
  subscription: UserSubscription | null;
  plan: SubscriptionPlan | null;
  preferences: SubscriptionPreferences | null;
}

export class SubscriptionService {
  // Get all available subscription plans
  static async getSubscriptionPlans() {
    try {
      const { data, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching subscription plans:', error);
      return { data: null, error };
    }
  }

  // Get user's current subscription details
  static async getUserSubscriptionDetails(userId: string) {
    try {
      console.log('🔍 Getting subscription details for user:', userId);

      const { data, error } = await supabase.rpc(
        'get_user_subscription_details',
        { user_uuid: userId }
      );

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error getting user subscription details:', error);
      return { data: null, error };
    }
  }

  // Subscribe user to a plan
  static async subscribeUser(userId: string, planId: string, paymentMethod?: string) {
    try {
      console.log('📝 Subscribing user to plan:', { userId, planId, paymentMethod });

      // First, cancel any existing active subscription
      await this.cancelUserSubscription(userId);

      // Calculate end date (30 days for monthly, 365 days for yearly)
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + 30); // Default to monthly

      const { data, error } = await supabase
        .from('user_subscriptions')
        .insert({
          user_id: userId,
          plan_id: planId,
          status: 'active',
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
          payment_method: paymentMethod || 'free',
          next_payment_date: endDate.toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Create default preferences if they don't exist
      await this.createDefaultPreferences(userId);

      console.log('✅ User subscribed successfully:', data);
      return { data, error: null };
    } catch (error) {
      console.error('Error subscribing user:', error);
      return { data: null, error };
    }
  }

  // Cancel user subscription
  static async cancelUserSubscription(userId: string) {
    try {
      console.log('❌ Cancelling subscription for user:', userId);

      const { data, error } = await supabase
        .from('user_subscriptions')
        .update({
          status: 'cancelled',
          auto_renew: false,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('status', 'active')
        .select();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      return { data: null, error };
    }
  }

  // Update subscription preferences
  static async updateSubscriptionPreferences(
    userId: string, 
    preferences: Partial<SubscriptionPreferences>
  ) {
    try {
      console.log('⚙️ Updating subscription preferences:', { userId, preferences });

      const { data, error } = await supabase
        .from('subscription_preferences')
        .upsert({
          user_id: userId,
          ...preferences,
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error updating subscription preferences:', error);
      return { data: null, error };
    }
  }

  // Get subscription preferences
  static async getSubscriptionPreferences(userId: string) {
    try {
      const { data, error } = await supabase
        .from('subscription_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      // If no preferences exist, create default ones
      if (!data) {
        return await this.createDefaultPreferences(userId);
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error getting subscription preferences:', error);
      return { data: null, error };
    }
  }

  // Create default preferences for new user
  static async createDefaultPreferences(userId: string) {
    try {
      const defaultPreferences = {
        user_id: userId,
        email_notifications: true,
        sms_notifications: false,
        in_app_notifications: true,
        legal_updates: true,
        question_responses: true,
        system_announcements: true,
        marketing_emails: false,
        weekly_digest: true,
        preferred_language: 'ar'
      };

      const { data, error } = await supabase
        .from('subscription_preferences')
        .insert(defaultPreferences)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error creating default preferences:', error);
      return { data: null, error };
    }
  }

  // Get subscription analytics (admin only)
  static async getSubscriptionAnalytics(days: number = 30) {
    try {
      const { data, error } = await supabase
        .from('subscription_analytics')
        .select('*')
        .order('date', { ascending: false })
        .limit(days);

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching subscription analytics:', error);
      return { data: null, error };
    }
  }

  // Update subscription analytics (admin only)
  static async updateSubscriptionAnalytics() {
    try {
      const { data, error } = await supabase.rpc('update_subscription_analytics');

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error updating subscription analytics:', error);
      return { data: null, error };
    }
  }

  // Get subscription statistics summary
  static async getSubscriptionStats() {
    try {
      // Get current active subscriptions by plan
      const { data: subscriptions, error: subsError } = await supabase
        .from('user_subscriptions')
        .select(`
          status,
          subscription_plans!inner(name, name_ar, price)
        `)
        .eq('status', 'active');

      if (subsError) throw subsError;

      // Get total users for conversion rate
      const { count: totalUsers, error: usersError } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      if (usersError) throw usersError;

      // Calculate statistics
      const stats = {
        totalSubscribers: subscriptions?.length || 0,
        totalUsers: totalUsers || 0,
        conversionRate: totalUsers ? ((subscriptions?.length || 0) / totalUsers * 100).toFixed(1) : '0',
        planBreakdown: subscriptions?.reduce((acc: Record<string, number>, sub: any) => {
          const planName = sub.subscription_plans.name_ar;
          acc[planName] = (acc[planName] || 0) + 1;
          return acc;
        }, {}) || {},
        totalRevenue: subscriptions?.reduce((sum: number, sub: any) => {
          return sum + (sub.subscription_plans.price || 0);
        }, 0) || 0
      };

      return { data: stats, error: null };
    } catch (error) {
      console.error('Error getting subscription stats:', error);
      return { data: null, error };
    }
  }

  // Check if user has active subscription
  static async hasActiveSubscription(userId: string) {
    try {
      const { data, error } = await supabase
        .from('user_subscriptions')
        .select('id, plan_id, status')
        .eq('user_id', userId)
        .eq('status', 'active')
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return { data: !!data, error: null };
    } catch (error) {
      console.error('Error checking active subscription:', error);
      return { data: false, error };
    }
  }

  // Get user's subscription plan name
  static async getUserPlanName(userId: string) {
    try {
      const { data, error } = await supabase
        .from('user_subscriptions')
        .select(`
          subscription_plans!inner(name, name_ar)
        `)
        .eq('user_id', userId)
        .eq('status', 'active')
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return { 
        data: data?.subscription_plans?.name_ar || 'مجاني', 
        error: null 
      };
    } catch (error) {
      console.error('Error getting user plan name:', error);
      return { data: 'مجاني', error };
    }
  }
}
