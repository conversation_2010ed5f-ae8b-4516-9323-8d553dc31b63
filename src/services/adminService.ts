import { supabase } from '@/integrations/supabase/client';

export interface UserProfile {
  id: string;
  email: string;
  full_name?: string;
  phone?: string;
  role: 'admin' | 'user' | 'advocate';
  subscription_tier: 'free' | 'pro_user' | 'pro_advocate';
  is_verified?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface AdvocateProfile {
  id: string;
  profile_id: string;
  specializations: string[];
  bio: string;
  hourly_rate: number;
  is_featured: boolean;
  rating: number;
  total_reviews: number;
  availability?: any;
}

export interface CreateUserData {
  email: string;
  name: string;
  role: 'user' | 'advocate';
  phone?: string;
  subscription_tier?: 'free' | 'pro_user' | 'pro_advocate';
  is_verified?: boolean;
  advocate_specializations?: string[];
  advocate_bio?: string;
  advocate_hourly_rate?: number;
}

export interface UpdateUserData {
  name?: string;
  phone?: string;
  role?: 'user' | 'advocate' | 'admin';
  subscription_tier?: 'free' | 'pro_user' | 'pro_advocate';
  is_verified?: boolean;
  advocate_specializations?: string[];
  advocate_bio?: string;
  advocate_hourly_rate?: number;
}

export interface UserAnalytics {
  total_users: number;
  users_by_role: Record<string, number>;
  verification_status: {
    verified: number;
    unverified: number;
  };
  subscription_tiers: Record<string, number>;
  new_users_last_30_days: number;
  active_users_last_7_days: number;
}

export interface QuestionsAnalytics {
  total_questions: number;
  answered_questions: number;
  pending_questions: number;
  questions_by_category: Record<string, number>;
  questions_by_status: Record<string, number>;
  questions_last_30_days: number;
  average_response_time_hours: number;
  top_advocates_by_responses: Array<{
    advocate_name: string;
    advocate_email: string;
    response_count: number;
  }>;
}

export interface AdvocateAnalytics {
  total_advocates: number;
  verified_advocates: number;
  average_rating: number;
  advocates_by_specialization: Record<string, number>;
  response_rates: {
    total_responses: number;
    responses_last_7_days: number;
    responses_last_30_days: number;
  };
  advocate_performance: Array<{
    advocate_name: string;
    advocate_email: string;
    specializations: string[];
    rating: number;
    total_reviews: number;
    questions_handled: number;
    responses_given: number;
    hourly_rate: number;
  }>;
}

export class AdminService {
  // User Management
  static async getAllUsers(filters?: {
    role?: string;
    is_verified?: boolean;
    subscription_tier?: string;
    search?: string;
  }) {
    try {
      let query = supabase
        .from('profiles')
        .select(`
          *,
          advocates (
            id,
            specializations,
            bio,
            hourly_rate,
            rating,
            total_reviews
          )
        `)
        .order('created_at', { ascending: false });

      if (filters?.role) {
        query = query.eq('role', filters.role);
      }
      if (filters?.is_verified !== undefined) {
        query = query.eq('is_verified', filters.is_verified);
      }
      if (filters?.subscription_tier) {
        query = query.eq('subscription_tier', filters.subscription_tier);
      }
      if (filters?.search) {
        query = query.or(`email.ilike.%${filters.search}%,full_name.ilike.%${filters.search}%`);
      }

      const { data, error } = await query;

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching users:', error);
      return { data: null, error };
    }
  }

  static async createUser(userData: CreateUserData) {
    try {
      const { data, error } = await supabase.rpc('admin_create_user', {
        user_email: userData.email,
        user_name: userData.name,
        user_role: userData.role,
        user_phone: userData.phone || null,
        subscription_tier: userData.subscription_tier || 'free',
        is_verified: userData.is_verified ?? true,
        advocate_specializations: userData.advocate_specializations || null,
        advocate_bio: userData.advocate_bio || null,
        advocate_hourly_rate: userData.advocate_hourly_rate || 500.00
      });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error creating user:', error);
      return { data: null, error };
    }
  }

  static async updateUser(userId: string, userData: UpdateUserData) {
    try {
      // First check if the function exists by trying to call it
      const { data, error } = await supabase.rpc('admin_update_user', {
        user_id: userId,
        user_name: userData.name || null,
        user_phone: userData.phone || null,
        user_role: userData.role || null,
        subscription_tier: userData.subscription_tier || null,
        is_verified: userData.is_verified ?? null,
        advocate_specializations: userData.advocate_specializations || null,
        advocate_bio: userData.advocate_bio || null,
        advocate_hourly_rate: userData.advocate_hourly_rate || null
      });

      if (error) {
        // If function doesn't exist, fall back to direct table update
        if (error.message?.includes('function') && error.message?.includes('does not exist')) {
          return await this.updateUserFallback(userId, userData);
        }
        throw error;
      }
      return { data, error: null };
    } catch (error) {
      console.error('Error updating user:', error);
      // Try fallback method if RPC fails
      try {
        return await this.updateUserFallback(userId, userData);
      } catch (fallbackError) {
        console.error('Fallback update also failed:', fallbackError);
        return { data: null, error: fallbackError };
      }
    }
  }

  // Fallback method for updating users without RPC
  static async updateUserFallback(userId: string, userData: UpdateUserData) {
    try {
      // Update profile table
      const profileUpdate: any = {};
      if (userData.name !== undefined) profileUpdate.full_name = userData.name;
      if (userData.phone !== undefined) profileUpdate.phone = userData.phone;
      if (userData.role !== undefined) profileUpdate.role = userData.role;
      if (userData.subscription_tier !== undefined) profileUpdate.subscription_tier = userData.subscription_tier;
      if (userData.is_verified !== undefined) profileUpdate.is_verified = userData.is_verified;
      profileUpdate.updated_at = new Date().toISOString();

      const { error: profileError } = await supabase
        .from('profiles')
        .update(profileUpdate)
        .eq('id', userId);

      if (profileError) throw profileError;

      // Handle advocate-specific updates
      if (userData.role === 'advocate' ||
          userData.advocate_specializations ||
          userData.advocate_bio ||
          userData.advocate_hourly_rate) {

        const advocateUpdate: any = {};
        if (userData.advocate_specializations !== undefined) advocateUpdate.specializations = userData.advocate_specializations;
        if (userData.advocate_bio !== undefined) advocateUpdate.bio = userData.advocate_bio;
        if (userData.advocate_hourly_rate !== undefined) advocateUpdate.hourly_rate = userData.advocate_hourly_rate;
        if (userData.is_verified !== undefined) advocateUpdate.is_verified = userData.is_verified;
        advocateUpdate.updated_at = new Date().toISOString();

        // Check if advocate record exists
        const { data: existingAdvocate } = await supabase
          .from('advocates')
          .select('id')
          .eq('profile_id', userId)
          .single();

        if (existingAdvocate) {
          // Update existing advocate
          const { error: advocateError } = await supabase
            .from('advocates')
            .update(advocateUpdate)
            .eq('profile_id', userId);

          if (advocateError) throw advocateError;
        } else if (userData.role === 'advocate') {
          // Create new advocate record
          const { error: advocateError } = await supabase
            .from('advocates')
            .insert({
              profile_id: userId,
              specializations: userData.advocate_specializations || [],
              bio: userData.advocate_bio || '',
              hourly_rate: userData.advocate_hourly_rate || 500,
              is_verified: userData.is_verified || false,
              ...advocateUpdate
            });

          if (advocateError) throw advocateError;
        }
      }

      return {
        data: {
          success: true,
          user_id: userId,
          message: 'User updated successfully'
        },
        error: null
      };
    } catch (error) {
      console.error('Fallback update error:', error);
      return { data: null, error };
    }
  }

  static async deleteUser(userId: string) {
    try {
      const { data, error } = await supabase.rpc('admin_delete_user', {
        user_id: userId
      });

      if (error) {
        // If function doesn't exist, fall back to direct table deletion
        if (error.message?.includes('function') && error.message?.includes('does not exist')) {
          return await this.deleteUserFallback(userId);
        }
        throw error;
      }
      return { data, error: null };
    } catch (error) {
      console.error('Error deleting user:', error);
      // Try fallback method if RPC fails
      try {
        return await this.deleteUserFallback(userId);
      } catch (fallbackError) {
        console.error('Fallback delete also failed:', fallbackError);
        return { data: null, error: fallbackError };
      }
    }
  }

  // Fallback method for deleting users without RPC
  static async deleteUserFallback(userId: string) {
    try {
      // Get user email for logging
      const { data: userProfile } = await supabase
        .from('profiles')
        .select('email')
        .eq('id', userId)
        .single();

      if (!userProfile) {
        throw new Error('User not found');
      }

      // Delete in order to respect foreign key constraints

      // 1. Delete advocate record (if exists)
      await supabase
        .from('advocates')
        .delete()
        .eq('profile_id', userId);

      // 2. Delete user responses
      await supabase
        .from('responses')
        .delete()
        .eq('user_id', userId);

      // 3. Update questions to remove advocate assignment
      await supabase
        .from('legal_questions')
        .update({ advocate_id: null })
        .eq('advocate_id', userId);

      // 4. Delete user questions
      await supabase
        .from('legal_questions')
        .delete()
        .eq('user_id', userId);

      // 5. Delete user documents
      await supabase
        .from('legal_documents')
        .delete()
        .eq('user_id', userId);

      // 6. Finally delete the profile
      const { error: deleteError } = await supabase
        .from('profiles')
        .delete()
        .eq('id', userId);

      if (deleteError) throw deleteError;

      return {
        data: {
          success: true,
          user_id: userId,
          email: userProfile.email,
          message: 'User deleted successfully'
        },
        error: null
      };
    } catch (error) {
      console.error('Fallback delete error:', error);
      return { data: null, error };
    }
  }

  static async bulkUpdateUsers(
    userIds: string[],
    operation: 'verify' | 'unverify' | 'change_role' | 'change_subscription' | 'delete',
    options?: {
      new_role?: string;
      new_subscription_tier?: string;
    }
  ) {
    try {
      const { data, error } = await supabase.rpc('admin_bulk_update_users', {
        user_ids: userIds,
        operation: operation,
        new_role: options?.new_role || null,
        new_subscription_tier: options?.new_subscription_tier || null
      });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error in bulk operation:', error);
      return { data: null, error };
    }
  }

  // Analytics
  static async getUserAnalytics(): Promise<{ data: UserAnalytics | null; error: any }> {
    try {
      const { data, error } = await supabase.rpc('get_user_analytics');

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching user analytics:', error);
      return { data: null, error };
    }
  }

  static async getQuestionsAnalytics(): Promise<{ data: QuestionsAnalytics | null; error: any }> {
    try {
      const { data, error } = await supabase.rpc('get_questions_analytics');

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching questions analytics:', error);
      return { data: null, error };
    }
  }

  static async getAdvocateAnalytics(): Promise<{ data: AdvocateAnalytics | null; error: any }> {
    try {
      const { data, error } = await supabase.rpc('get_advocate_analytics');

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching advocate analytics:', error);
      return { data: null, error };
    }
  }

  // Export data
  static async exportUsersData(format: 'csv' | 'json' = 'csv') {
    try {
      const { data: users, error } = await this.getAllUsers();
      
      if (error) throw error;
      if (!users) throw new Error('No data to export');

      if (format === 'csv') {
        return this.convertToCSV(users);
      } else {
        return JSON.stringify(users, null, 2);
      }
    } catch (error) {
      console.error('Error exporting data:', error);
      throw error;
    }
  }

  private static convertToCSV(data: any[]): string {
    if (!data.length) return '';

    const headers = ['ID', 'Email', 'Full Name', 'Role', 'Verified', 'Subscription Tier', 'Created At'];
    const csvContent = [
      headers.join(','),
      ...data.map(user => [
        user.id,
        user.email,
        user.full_name || '',
        user.role,
        user.is_verified ? 'Yes' : 'No',
        user.subscription_tier,
        new Date(user.created_at).toLocaleDateString()
      ].join(','))
    ].join('\n');

    return csvContent;
  }

  static downloadFile(content: string, filename: string, contentType: string) {
    const blob = new Blob([content], { type: contentType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }
}
