import { supabase } from '@/integrations/supabase/client';

export interface UserProfile {
  id: string;
  email: string;
  full_name?: string;
  phone?: string;
  role: 'admin' | 'user' | 'advocate';
  subscription_tier: 'free' | 'pro_user' | 'pro_advocate';
  subscription_end?: string;
  trial_end?: string;
  stripe_customer_id?: string;
  is_verified?: boolean;
  created_at: string;
  updated_at: string;
}

export interface AdvocateProfile {
  id: string;
  profile_id: string;
  specializations?: string[];
  bio?: string;
  hourly_rate?: number;
  is_featured?: boolean;
  rating?: number;
  total_reviews?: number;
  availability?: any;
  created_at: string;
  updated_at: string;
}

export class AuthService {
  static async signUpWithRole(
    email: string, 
    password: string, 
    fullName: string, 
    role: 'user' | 'advocate'
  ) {
    try {
      // First, sign up the user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            role: role,
          },
        },
      });

      if (authError) throw authError;

      if (authData.user) {
        // Update the profile with the selected role
        const { error: profileError } = await supabase
          .from('profiles')
          .update({ 
            role: role,
            full_name: fullName,
            is_verified: role === 'user' // Users are auto-verified, advocates need admin approval
          })
          .eq('id', authData.user.id);

        if (profileError) {
          console.error('Error updating profile:', profileError);
        }

        // If role is advocate, create advocate profile
        if (role === 'advocate') {
          const { error: advocateError } = await supabase
            .from('advocates')
            .insert({
              profile_id: authData.user.id,
              specializations: [],
              bio: '',
              hourly_rate: 0,
              is_featured: false,
              rating: 0,
              total_reviews: 0,
              availability: {}
            });

          if (advocateError) {
            console.error('Error creating advocate profile:', advocateError);
          }
        }
      }

      return { data: authData, error: null };
    } catch (error) {
      console.error('Signup error:', error);
      return { data: null, error };
    }
  }

  static async getAdvocateProfile(profileId: string): Promise<AdvocateProfile | null> {
    try {
      const { data, error } = await supabase
        .from('advocates')
        .select('*')
        .eq('profile_id', profileId)
        .single();

      if (error) {
        console.error('Error fetching advocate profile:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getAdvocateProfile:', error);
      return null;
    }
  }

  static async updateAdvocateProfile(profileId: string, updates: Partial<AdvocateProfile>) {
    try {
      const { data, error } = await supabase
        .from('advocates')
        .update(updates)
        .eq('profile_id', profileId)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error updating advocate profile:', error);
      return { data: null, error };
    }
  }

  static async getAllAdvocates(verified_only: boolean = true) {
    try {
      let query = supabase
        .from('advocates')
        .select(`
          *,
          profiles!inner(
            id,
            email,
            full_name,
            phone,
            is_verified,
            created_at
          )
        `);

      if (verified_only) {
        query = query.eq('profiles.is_verified', true);
      }

      const { data, error } = await query;

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching advocates:', error);
      return { data: null, error };
    }
  }

  static async verifyAdvocate(profileId: string) {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .update({ is_verified: true })
        .eq('id', profileId)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error verifying advocate:', error);
      return { data: null, error };
    }
  }

  static async unverifyAdvocate(profileId: string) {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .update({ is_verified: false })
        .eq('id', profileId)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error unverifying advocate:', error);
      return { data: null, error };
    }
  }

  static async getAppStatistics() {
    try {
      // Get total users count
      const { count: totalUsers } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      // Get total advocates count
      const { count: totalAdvocates } = await supabase
        .from('advocates')
        .select('*', { count: 'exact', head: true });

      // Get verified advocates count
      const { count: verifiedAdvocates } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .eq('role', 'advocate')
        .eq('is_verified', true);

      // Get total questions count
      const { count: totalQuestions } = await supabase
        .from('legal_questions')
        .select('*', { count: 'exact', head: true });

      // Get answered questions count
      const { count: answeredQuestions } = await supabase
        .from('legal_questions')
        .select('*', { count: 'exact', head: true })
        .eq('is_answered', true);

      // Get total documents count
      const { count: totalDocuments } = await supabase
        .from('legal_documents')
        .select('*', { count: 'exact', head: true });

      return {
        totalUsers: totalUsers || 0,
        totalAdvocates: totalAdvocates || 0,
        verifiedAdvocates: verifiedAdvocates || 0,
        totalQuestions: totalQuestions || 0,
        answeredQuestions: answeredQuestions || 0,
        totalDocuments: totalDocuments || 0,
      };
    } catch (error) {
      console.error('Error fetching app statistics:', error);
      return {
        totalUsers: 0,
        totalAdvocates: 0,
        verifiedAdvocates: 0,
        totalQuestions: 0,
        answeredQuestions: 0,
        totalDocuments: 0,
      };
    }
  }
}
