import { supabase } from '@/integrations/supabase/client';

export interface LegalQuestion {
  id: string;
  user_id: string;
  advocate_id?: string;
  title: string;
  description: string;
  category?: string;
  status: string;
  is_answered: boolean;
  created_at: string;
  updated_at: string;
  user_profile?: {
    full_name: string;
    email: string;
  };
  advocate_profile?: {
    full_name: string;
    email: string;
  };
  answer?: string;
  answer_created_at?: string;
}

export interface QuestionResponse {
  id: string;
  question_id: string;
  advocate_id: string;
  response_text: string;
  is_approved: boolean;
  created_at: string;
  updated_at: string;
  advocate_profile?: {
    full_name: string;
    email: string;
  };
  user_rating?: number;
  user_rating_comment?: string;
  average_rating?: number;
  total_ratings?: number;
}

export interface ResponseRating {
  id: string;
  response_id: string;
  user_id: string;
  rating: number;
  comment?: string;
  created_at: string;
  updated_at: string;
}

export interface QuestionWithResponses {
  question: LegalQuestion;
  user_profile: {
    full_name: string;
    email: string;
  };
  responses: Array<QuestionResponse & {
    advocate: {
      id: string;
      full_name: string;
      email: string;
      specializations: string[];
      rating: number;
      total_reviews: number;
    };
  }>;
}

export class QuestionsService {
  static async createQuestion(
    userId: string,
    title: string,
    description: string,
    category?: string,
    priority?: string,
    preferredAdvocateId?: string
  ) {
    try {
      console.log('🔄 Creating question for user:', userId);

      // Direct insertion with enhanced validation
      console.log('📝 Using direct insertion method');

      // Validate inputs
      if (!userId || !title || !description) {
        throw new Error('معلومات السؤال غير مكتملة');
      }

      if (title.length < 10) {
        throw new Error('عنوان السؤال يجب أن يكون 10 أحرف على الأقل');
      }

      if (description.length < 20) {
        throw new Error('وصف السؤال يجب أن يكون 20 حرف على الأقل');
      }

      const questionData = {
        user_id: userId,
        title: title.trim(),
        description: description.trim(),
        category: category || 'general',
        status: 'pending',
        is_answered: false,
        priority: priority || 'medium',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('📝 Question data:', questionData);

      const { data, error } = await supabase
        .from('legal_questions')
        .insert(questionData)
        .select()
        .single();

      if (error) {
        console.error('❌ Database error:', error);
        throw new Error(`فشل في حفظ السؤال: ${error.message}`);
      }

      console.log('✅ Question created successfully:', data);

      // If a preferred advocate was specified, try to assign the question
      if (preferredAdvocateId && data) {
        try {
          console.log('🔄 Assigning question to preferred advocate:', preferredAdvocateId);
          await this.assignQuestionToAdvocate(data.id, preferredAdvocateId);
          console.log('✅ Question assigned to advocate successfully');
        } catch (assignError) {
          console.warn('⚠️ Failed to assign to preferred advocate:', assignError);
          // Don't fail the whole operation if assignment fails
        }
      }

      return { data, error: null };
    } catch (error: any) {
      console.error('❌ Error creating question:', error);
      return {
        data: null,
        error: error instanceof Error ? error : new Error('حدث خطأ غير متوقع')
      };
    }
  }

  static async getUserQuestions(userId: string) {
    try {
      console.log('🔍 QuestionsService.getUserQuestions called for user:', userId);

      // Essayer d'abord une requête simple
      const { data: simpleData, error: simpleError } = await supabase
        .from('legal_questions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (simpleError) {
        console.error('❌ Simple query failed:', simpleError);
        throw simpleError;
      }

      console.log('✅ Simple query successful:', simpleData?.length || 0, 'questions');
      return { data: simpleData, error: null };

    } catch (error) {
      console.error('❌ Critical error in getUserQuestions:', error);
      return { data: null, error };
    }
  }

  static async getAdvocateQuestions(advocateId: string) {
    try {
      // First get the advocate profile to get the profile_id
      const { data: advocateProfile, error: advocateError } = await supabase
        .from('advocates')
        .select('id')
        .eq('profile_id', advocateId)
        .single();

      if (advocateError) throw advocateError;

      const { data, error } = await supabase
        .from('legal_questions')
        .select(`
          *,
          profiles!legal_questions_user_id_fkey(
            full_name,
            email
          )
        `)
        .eq('advocate_id', advocateProfile.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching advocate questions:', error);
      return { data: null, error };
    }
  }

  static async getPendingQuestions() {
    try {
      const { data, error } = await supabase
        .from('legal_questions')
        .select(`
          *,
          profiles!legal_questions_user_id_fkey(
            full_name,
            email
          )
        `)
        .is('advocate_id', null)
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching pending questions:', error);
      return { data: null, error };
    }
  }



  static async createResponse(questionId: string, responseText: string, advocateProfileId: string) {
    try {
      // First get the advocate ID from the profile ID
      const { data: advocateProfile, error: advocateError } = await supabase
        .from('advocates')
        .select('id')
        .eq('profile_id', advocateProfileId)
        .single();

      if (advocateError) throw advocateError;

      // Create the response
      const { data, error } = await supabase
        .from('responses')
        .insert({
          question_id: questionId,
          advocate_id: advocateProfile.id,
          response_text: responseText,
          is_approved: true, // Auto-approve for now
        })
        .select()
        .single();

      if (error) throw error;

      // The trigger will automatically update the question status
      return { data, error: null };
    } catch (error) {
      console.error('Error creating response:', error);
      return { data: null, error };
    }
  }

  static async getQuestionResponses(questionId: string) {
    try {
      const { data, error } = await supabase
        .from('responses')
        .select(`
          *,
          advocates!responses_advocate_id_fkey(
            profiles!advocates_profile_id_fkey(
              full_name,
              email
            )
          )
        `)
        .eq('question_id', questionId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching question responses:', error);
      return { data: null, error };
    }
  }

  static async assignQuestionToAdvocate(questionId: string, advocateProfileId: string) {
    try {
      console.log('🔄 Assigning question to advocate:', { questionId, advocateProfileId });

      // First verify the question exists and is available
      const { data: question, error: questionError } = await supabase
        .from('legal_questions')
        .select('id, status, advocate_id')
        .eq('id', questionId)
        .single();

      if (questionError) {
        console.error('❌ Question not found:', questionError);
        throw new Error('السؤال غير موجود');
      }

      if (question.advocate_id) {
        throw new Error('هذا السؤال مُعيَّن بالفعل لمحامي آخر');
      }

      // Try to use the database function first
      const { data: functionResult, error: functionError } = await supabase.rpc(
        'assign_question_to_advocate',
        {
          question_uuid: questionId,
          advocate_profile_uuid: advocateProfileId
        }
      );

      if (functionError) {
        console.log('⚠️ Database function failed, using fallback method:', functionError);
        // Fallback to manual assignment
        return await this.assignQuestionFallback(questionId, advocateProfileId);
      }

      if (functionResult === false) {
        throw new Error('فشل في تعيين السؤال - المحامي غير موجود');
      }

      console.log('✅ Question assigned successfully using database function');
      return { data: { success: true }, error: null };
    } catch (error: any) {
      console.error('❌ Error assigning question to advocate:', error);
      return {
        data: null,
        error: error instanceof Error ? error : new Error('حدث خطأ غير متوقع')
      };
    }
  }

  // Fallback method for question assignment
  static async assignQuestionFallback(questionId: string, advocateProfileId: string) {
    try {
      console.log('🔄 Using fallback assignment method');

      // Get or create advocate record
      let { data: advocateProfile, error: advocateError } = await supabase
        .from('advocates')
        .select('id, profile_id')
        .eq('profile_id', advocateProfileId)
        .single();

      if (advocateError && advocateError.code === 'PGRST116') {
        // Advocate profile doesn't exist, create it
        console.log('🔄 Creating advocate profile for:', advocateProfileId);

        // First verify the profile exists and is an advocate
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('id, role, is_verified')
          .eq('id', advocateProfileId)
          .single();

        if (profileError || !profile) {
          throw new Error('ملف المحامي غير موجود');
        }

        if (profile.role !== 'advocate') {
          throw new Error('هذا المستخدم ليس محامياً');
        }

        if (!profile.is_verified) {
          throw new Error('حساب المحامي غير مفعل');
        }

        const { data: newAdvocate, error: createError } = await supabase
          .from('advocates')
          .insert({
            profile_id: advocateProfileId,
            specializations: ['general'],
            bio: '',
            hourly_rate: 500,
            rating: 0.0,
            total_reviews: 0,
            availability: { status: 'available' }
          })
          .select('id, profile_id')
          .single();

        if (createError) {
          console.error('❌ Error creating advocate profile:', createError);
          throw new Error(`فشل في إنشاء ملف المحامي: ${createError.message}`);
        }

        advocateProfile = newAdvocate;
        console.log('✅ Advocate profile created:', advocateProfile);
      } else if (advocateError) {
        console.error('❌ Error fetching advocate profile:', advocateError);
        throw new Error(`خطأ في جلب بيانات المحامي: ${advocateError.message}`);
      }

      if (!advocateProfile) {
        throw new Error('فشل في الحصول على ملف المحامي');
      }

      // Now assign the question
      const { data, error } = await supabase
        .from('legal_questions')
        .update({
          advocate_id: advocateProfile.id,
          status: 'assigned',
          updated_at: new Date().toISOString(),
        })
        .eq('id', questionId)
        .eq('advocate_id', null) // Ensure it's still unassigned
        .select()
        .single();

      if (error) {
        console.error('❌ Error updating question:', error);
        throw new Error(`فشل في تعيين السؤال: ${error.message}`);
      }

      if (!data) {
        throw new Error('السؤال مُعيَّن بالفعل لمحامي آخر');
      }

      console.log('✅ Question assigned successfully using fallback method:', data);
      return { data, error: null };
    } catch (error: any) {
      console.error('❌ Error in fallback assignment:', error);
      return {
        data: null,
        error: error instanceof Error ? error : new Error('حدث خطأ غير متوقع')
      };
    }
  }

  static async getAllQuestions() {
    try {
      const { data, error } = await supabase
        .from('legal_questions')
        .select(`
          *,
          profiles!legal_questions_user_id_fkey(
            full_name,
            email
          ),
          advocates!legal_questions_advocate_id_fkey(
            id,
            profiles!advocates_profile_id_fkey(
              full_name,
              email
            )
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching all questions:', error);
      return { data: null, error };
    }
  }

  static async getQuestionById(questionId: string) {
    try {
      const { data, error } = await supabase
        .from('legal_questions')
        .select(`
          *,
          profiles!legal_questions_user_id_fkey(
            full_name,
            email
          ),
          advocates!legal_questions_advocate_id_fkey(
            id,
            profiles!advocates_profile_id_fkey(
              full_name,
              email
            )
          )
        `)
        .eq('id', questionId)
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching question by ID:', error);
      return { data: null, error };
    }
  }

  static async deleteQuestion(questionId: string, userId: string) {
    try {
      const { data, error } = await supabase
        .from('legal_questions')
        .delete()
        .eq('id', questionId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error deleting question:', error);
      return { data: null, error };
    }
  }

  static getQuestionCategories() {
    return [
      { value: 'family', label: 'قانون الأسرة', labelFr: 'Droit de la famille' },
      { value: 'labor', label: 'قانون العمل', labelFr: 'Droit du travail' },
      { value: 'real_estate', label: 'قانون العقارات', labelFr: 'Droit immobilier' },
      { value: 'commercial', label: 'القانون التجاري', labelFr: 'Droit commercial' },
      { value: 'criminal', label: 'القانون الجنائي', labelFr: 'Droit pénal' },
      { value: 'civil', label: 'القانون المدني', labelFr: 'Droit civil' },
      { value: 'administrative', label: 'القانون الإداري', labelFr: 'Droit administratif' },
      { value: 'tax', label: 'القانون الضريبي', labelFr: 'Droit fiscal' },
      { value: 'intellectual_property', label: 'الملكية الفكرية', labelFr: 'Propriété intellectuelle' },
      { value: 'general', label: 'عام', labelFr: 'Général' },
    ];
  }

  // Rating-related methods
  static async rateResponse(
    responseId: string,
    userId: string,
    rating: number,
    comment?: string
  ) {
    try {
      console.log('🌟 Rating response:', { responseId, userId, rating, comment });

      // Validate rating
      if (rating < 1 || rating > 5) {
        throw new Error('التقييم يجب أن يكون بين 1 و 5 نجوم');
      }

      // Check if user has already rated this response
      const { data: existingRating } = await supabase
        .from('response_ratings')
        .select('id')
        .eq('response_id', responseId)
        .eq('user_id', userId)
        .single();

      if (existingRating) {
        // Update existing rating
        const { data, error } = await supabase
          .from('response_ratings')
          .update({
            rating,
            comment: comment || null,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingRating.id)
          .select()
          .single();

        if (error) throw error;
        return { data, error: null };
      } else {
        // Create new rating
        const { data, error } = await supabase
          .from('response_ratings')
          .insert({
            response_id: responseId,
            user_id: userId,
            rating,
            comment: comment || null
          })
          .select()
          .single();

        if (error) throw error;
        return { data, error: null };
      }
    } catch (error) {
      console.error('Error rating response:', error);
      return { data: null, error };
    }
  }

  static async getResponseRating(responseId: string, userId: string) {
    try {
      const { data, error } = await supabase
        .from('response_ratings')
        .select('*')
        .eq('response_id', responseId)
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error getting response rating:', error);
      return { data: null, error };
    }
  }

  static async getQuestionWithResponsesAndRatings(questionId: string, userId: string) {
    try {
      console.log('🔍 Getting question with responses and ratings:', { questionId, userId });

      // Try to use the database function first
      const { data: functionResult, error: functionError } = await supabase.rpc(
        'get_question_with_responses_and_ratings',
        {
          question_uuid: questionId,
          user_uuid: userId
        }
      );

      if (functionError) {
        console.log('⚠️ Database function failed, using fallback method:', functionError);
        return await this.getQuestionWithResponsesAndRatingsFallback(questionId, userId);
      }

      return { data: functionResult, error: null };
    } catch (error) {
      console.error('Error getting question with responses and ratings:', error);
      return { data: null, error };
    }
  }

  static async getQuestionWithResponsesAndRatingsFallback(questionId: string, userId: string) {
    try {
      // Get question details
      const { data: question, error: questionError } = await supabase
        .from('legal_questions')
        .select(`
          *,
          user_profile:profiles!legal_questions_user_id_fkey(full_name, email)
        `)
        .eq('id', questionId)
        .single();

      if (questionError) throw questionError;

      // Get responses with advocate details and ratings
      const { data: responses, error: responsesError } = await supabase
        .from('responses')
        .select(`
          *,
          advocate:advocates!responses_advocate_id_fkey(
            id,
            specializations,
            rating,
            total_reviews,
            profile:profiles!advocates_profile_id_fkey(full_name, email)
          ),
          user_rating:response_ratings!response_ratings_response_id_fkey(
            rating,
            comment
          )
        `)
        .eq('question_id', questionId)
        .eq('response_ratings.user_id', userId)
        .eq('is_approved', true)
        .order('created_at', { ascending: true });

      if (responsesError) throw responsesError;

      // Format the response to match the expected structure
      const formattedResponses = responses?.map(response => ({
        ...response,
        advocate: {
          id: response.advocate.id,
          full_name: response.advocate.profile.full_name,
          email: response.advocate.profile.email,
          specializations: response.advocate.specializations || [],
          rating: response.advocate.rating || 0,
          total_reviews: response.advocate.total_reviews || 0
        },
        user_rating: response.user_rating?.[0]?.rating,
        user_rating_comment: response.user_rating?.[0]?.comment,
        average_rating: 0, // Will be calculated separately if needed
        total_ratings: 0 // Will be calculated separately if needed
      })) || [];

      const result = {
        question,
        user_profile: question.user_profile,
        responses: formattedResponses
      };

      return { data: result, error: null };
    } catch (error) {
      console.error('Error in fallback method:', error);
      return { data: null, error };
    }
  }
}
