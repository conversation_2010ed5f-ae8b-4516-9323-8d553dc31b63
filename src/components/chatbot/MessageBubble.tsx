import React from 'react';
import { useTranslation } from 'react-i18next';
import { User, Scale, Sparkles } from 'lucide-react';
import { ChatMessage } from '@/services/chatbotService';

interface MessageBubbleProps {
  message: ChatMessage;
  index: number;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({ message, index }) => {
  const { i18n } = useTranslation();
  const currentLanguage = i18n.language as 'ar' | 'fr';

  // Detect message language for proper RTL/LTR direction
  const detectMessageDirection = (text: string): 'rtl' | 'ltr' => {
    const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]/;
    return arabicRegex.test(text) ? 'rtl' : 'ltr';
  };

  // Get message alignment based on role
  const getMessageAlignment = (role: 'user' | 'assistant') => {
    return role === 'user' ? 'justify-end' : 'justify-start';
  };

  const messageDirection = detectMessageDirection(message.content);
  const alignment = getMessageAlignment(message.role);

  return (
    <div
      className={`flex gap-4 ${alignment} message-bubble`}
      style={{ animationDelay: `${index * 100}ms` }}
    >
      <div className={`flex gap-4 max-w-[85%] ${message.role === 'user' ? 'flex-row-reverse' : ''}`}>
        {/* Avatar */}
        <div className={`w-10 h-10 rounded-full flex items-center justify-center shadow-md flex-shrink-0 ${
          message.role === 'user' 
            ? 'avatar-user' 
            : 'avatar-bot'
        }`}>
          {message.role === 'user' ? (
            <User className="h-5 w-5 text-white" />
          ) : (
            <Scale className="h-5 w-5 text-gray-600" />
          )}
        </div>

        {/* Message Content */}
        <div 
          className={`rounded-2xl px-4 py-3 shadow-sm transition-all duration-200 hover:shadow-md ${
            message.role === 'user' 
              ? 'user-message' 
              : 'bot-message'
          }`}
          style={{ direction: messageDirection }}
        >
          {/* Message Text */}
          <p className={`whitespace-pre-wrap leading-relaxed ${
            messageDirection === 'rtl' ? 'text-right' : 'text-left'
          }`}>
            {message.content}
          </p>

          {/* Message Metadata */}
          {message.metadata?.confidence && (
            <div className={`mt-3 message-metadata ${
              messageDirection === 'rtl' ? 'flex justify-end' : 'flex justify-start'
            }`}>
              <div className="confidence-indicator flex items-center gap-1">
                <Sparkles className="h-3 w-3" />
                <span className="text-xs">
                  {currentLanguage === 'ar' ? 'الثقة:' : 'Confiance:'} {Math.round(message.metadata.confidence * 100)}%
                </span>
              </div>
            </div>
          )}

          {/* Sources (if available) */}
          {message.metadata?.sources && message.metadata.sources.length > 0 && (
            <div className={`mt-2 ${
              messageDirection === 'rtl' ? 'text-right' : 'text-left'
            }`}>
              <div className="text-xs opacity-70">
                <span className="font-medium">
                  {currentLanguage === 'ar' ? 'المصادر:' : 'Sources:'}
                </span>
                <div className="mt-1 flex flex-wrap gap-1">
                  {message.metadata.sources.map((source, idx) => (
                    <span 
                      key={idx}
                      className="inline-block bg-black/10 text-xs px-2 py-1 rounded-full"
                    >
                      {source}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Timestamp */}
          <div className={`mt-2 text-xs opacity-50 ${
            messageDirection === 'rtl' ? 'text-right' : 'text-left'
          }`}>
            {message.timestamp ? new Date(message.timestamp).toLocaleTimeString(
              currentLanguage === 'ar' ? 'ar-MA' : 'fr-FR',
              { hour: '2-digit', minute: '2-digit' }
            ) : ''}
          </div>
        </div>
      </div>
    </div>
  );
};

// Typing Indicator Component
export const TypingIndicator: React.FC = () => {
  const { i18n } = useTranslation();
  const currentLanguage = i18n.language as 'ar' | 'fr';

  return (
    <div className="flex gap-4 justify-start typing-indicator">
      <div className="w-10 h-10 rounded-full avatar-bot flex items-center justify-center shadow-md">
        <Scale className="h-5 w-5 text-gray-600" />
      </div>
      <div className="bot-message rounded-2xl px-4 py-3">
        <div className="flex gap-1 items-center">
          <span className="text-sm text-gray-500 mr-2">
            {currentLanguage === 'ar' ? 'يكتب...' : 'En train d\'écrire...'}
          </span>
          <div className="w-2 h-2 bg-blue-400 rounded-full typing-dot"></div>
          <div className="w-2 h-2 bg-blue-400 rounded-full typing-dot"></div>
          <div className="w-2 h-2 bg-blue-400 rounded-full typing-dot"></div>
        </div>
      </div>
    </div>
  );
};

// Welcome Message Component
export const WelcomeMessage: React.FC = () => {
  const { i18n } = useTranslation();
  const currentLanguage = i18n.language as 'ar' | 'fr';

  return (
    <div className="text-center py-12">
      <div className="relative mb-6">
        <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto shadow-lg">
          <Scale className="h-10 w-10 text-white" />
        </div>
        <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center shadow-md">
          <Sparkles className="h-4 w-4 text-white" />
        </div>
      </div>
      <h3 className="text-2xl font-bold mb-3 text-gray-900">
        {currentLanguage === 'ar' ? 'مرحباً بك في المساعد القانوني الذكي' : 'Bienvenue dans l\'Assistant Juridique Intelligent'}
      </h3>
      <p className="text-gray-600 mb-8 max-w-md mx-auto leading-relaxed">
        {currentLanguage === 'ar' 
          ? 'اطرح أي سؤال قانوني وسأساعدك في الحصول على إجابة دقيقة ومفصلة وفقاً للقانون المغربي'
          : 'Posez toute question juridique et je vous aiderai à obtenir une réponse précise et détaillée selon le droit marocain'
        }
      </p>
    </div>
  );
};
