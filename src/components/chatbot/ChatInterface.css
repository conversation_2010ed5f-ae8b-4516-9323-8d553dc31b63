/* Enhanced Chatbot Interface Styles */

/* RTL/LTR Support */
.chat-message-rtl {
  direction: rtl;
  text-align: right;
}

.chat-message-ltr {
  direction: ltr;
  text-align: left;
}

/* Message Animations */
@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Message Bubble Styles */
.message-bubble {
  animation: slideInFromBottom 0.3s ease-out;
  transition: all 0.2s ease;
}

.message-bubble:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* User Message Bubble */
.user-message {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border-radius: 18px 18px 4px 18px;
}

/* Bot Message Bubble */
.bot-message {
  background: white;
  color: #1f2937;
  border: 1px solid #e5e7eb;
  border-radius: 18px 18px 18px 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Typing Indicator */
.typing-indicator {
  animation: fadeIn 0.3s ease-in;
}

.typing-dot {
  animation: pulse 1.4s ease-in-out infinite;
}

.typing-dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

/* Input Field Enhancements */
.chat-input {
  transition: all 0.2s ease;
  border-radius: 12px;
}

.chat-input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
}

/* Send Button Animation */
.send-button {
  transition: all 0.2s ease;
}

.send-button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.send-button:active {
  transform: scale(0.95);
}

/* Quick Questions Styling */
.quick-question-button {
  transition: all 0.2s ease;
  border-radius: 12px;
}

.quick-question-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Gradient Background */
.chat-background {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Avatar Styles */
.avatar-user {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.avatar-bot {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border: 2px solid #cbd5e1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Scrollbar Styling */
.chat-scroll::-webkit-scrollbar {
  width: 6px;
}

.chat-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.chat-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.chat-scroll::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
  .message-bubble {
    max-width: 90%;
  }
  
  .chat-input {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Dark Mode Support (if needed) */
@media (prefers-color-scheme: dark) {
  .bot-message {
    background: #374151;
    color: #f9fafb;
    border-color: #4b5563;
  }
  
  .chat-background {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  }
}

/* Accessibility Improvements */
.message-bubble:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .user-message {
    background: #000;
    color: #fff;
    border: 2px solid #fff;
  }
  
  .bot-message {
    background: #fff;
    color: #000;
    border: 2px solid #000;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .message-bubble,
  .typing-indicator,
  .send-button,
  .quick-question-button {
    animation: none;
    transition: none;
  }
  
  .send-button:hover,
  .quick-question-button:hover {
    transform: none;
  }
}

/* Loading States */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Confidence Indicator */
.confidence-indicator {
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  padding: 4px 8px;
  font-size: 0.75rem;
}

/* Message Metadata */
.message-metadata {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.message-bubble:hover .message-metadata {
  opacity: 1;
}
