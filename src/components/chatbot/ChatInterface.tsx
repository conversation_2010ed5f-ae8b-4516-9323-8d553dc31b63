import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/contexts/AuthContext';
import './ChatInterface.css';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import {
  ChatbotService,
  ChatMessage,
  ChatConversation
} from '@/services/chatbotService';
import { MessageBubble, TypingIndicator, WelcomeMessage } from './MessageBubble';
import { QuickQuestions } from './QuickQuestions';
import {
  MessageCircle,
  Send,
  Bot,
  User,
  Loader2,
  Plus,
  Archive,
  Trash2,
  Sparkles,
  Scale
} from 'lucide-react';

interface ChatInterfaceProps {
  conversationId?: string;
  onConversationChange?: (conversationId: string) => void;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  conversationId,
  onConversationChange
}) => {
  const { user } = useAuth();
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const [currentConversation, setCurrentConversation] = useState<ChatConversation | null>(null);
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const currentLanguage = i18n.language as 'ar' | 'fr';

  useEffect(() => {
    if (conversationId) {
      loadConversation(conversationId);
    } else if (user) {
      createNewConversation();
    }
  }, [conversationId, user]);

  useEffect(() => {
    scrollToBottom();
  }, [currentConversation?.messages, isTyping]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };



  const loadConversation = async (convId: string) => {
    try {
      const conversation = await ChatbotService.getConversation(convId);
      setCurrentConversation(conversation);
    } catch (error) {
      console.error('Failed to load conversation:', error);
      toast({
        title: t('common.error'),
        description: 'فشل في تحميل المحادثة',
        variant: 'destructive'
      });
    }
  };

  const createNewConversation = async () => {
    if (!user) return;

    try {
      const newConversationId = await ChatbotService.createConversation(
        user.id,
        currentLanguage
      );

      const newConversation: ChatConversation = {
        id: newConversationId,
        userId: user.id,
        title: currentLanguage === 'ar' ? 'محادثة جديدة' : 'Nouvelle conversation',
        messages: [],
        language: currentLanguage,
        createdAt: new Date(),
        updatedAt: new Date(),
        status: 'active'
      };

      setCurrentConversation(newConversation);
      onConversationChange?.(newConversationId);
    } catch (error) {
      console.error('Failed to create conversation:', error);
      toast({
        title: currentLanguage === 'ar' ? 'خطأ' : 'Erreur',
        description: currentLanguage === 'ar'
          ? 'فشل في إنشاء محادثة جديدة'
          : 'Échec de la création d\'une nouvelle conversation',
        variant: 'destructive'
      });
    }
  };

  const sendMessage = async () => {
    if (!message.trim() || !user || !currentConversation) return;

    const userMessage = message.trim();
    setMessage('');
    setIsLoading(true);
    setIsTyping(true);

    try {
      const response = await ChatbotService.sendMessage({
        message: userMessage,
        language: currentLanguage,
        conversationId: currentConversation.id,
        userId: user.id,
        context: {
          previousMessages: currentConversation.messages.slice(-5)
        }
      });

      if (response.success && response.message) {
        // Update local conversation with new messages
        const updatedConversation = await ChatbotService.getConversation(currentConversation.id);
        if (updatedConversation) {
          setCurrentConversation(updatedConversation);
        }
      } else {
        throw new Error(response.error || 'Failed to send message');
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      toast({
        title: t('common.error'),
        description: 'فشل في إرسال الرسالة',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const handleQuickQuestion = (question: string) => {
    setMessage(question);
  };

  if (!user) {
    return (
      <Card className="h-96 flex items-center justify-center">
        <CardContent>
          <p className="text-gray-500">
            {currentLanguage === 'ar' ? 'يجب تسجيل الدخول للوصول للمساعد القانوني' : 'Vous devez vous connecter pour accéder à l\'assistant juridique'}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto px-4">
      <Card className="h-[700px] flex flex-col shadow-lg border-0 bg-gradient-to-br from-white to-gray-50">
        <CardHeader className="border-b border-gray-200 flex-shrink-0 bg-white/80 backdrop-blur-sm">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-3">
              <div className="relative">
                <Scale className="h-6 w-6 text-blue-600" />
                <Sparkles className="h-3 w-3 text-yellow-500 absolute -top-1 -right-1" />
              </div>
              <div className="flex flex-col">
                <span className="text-lg font-bold text-gray-900">
                  {currentLanguage === 'ar' ? 'المساعد القانوني الذكي' : 'Assistant Juridique Intelligent'}
                </span>
                <span className="text-xs text-gray-500 font-normal">
                  {currentLanguage === 'ar' ? 'مدعوم بالذكاء الاصطناعي' : 'Alimenté par l\'IA'}
                </span>
              </div>
            </CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={createNewConversation}
                className="hover:bg-blue-50 hover:border-blue-300 transition-colors"
              >
                <Plus className="h-4 w-4" />
                {currentLanguage === 'ar' ? 'محادثة جديدة' : 'Nouvelle conversation'}
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="flex-1 flex flex-col p-0 chat-background">
          <ScrollArea className="flex-1 p-6 chat-scroll">
            {currentConversation?.messages.length === 0 ? (
              <div className="space-y-6">
                <WelcomeMessage />

                <QuickQuestions onQuestionSelect={handleQuickQuestion} />
              </div>
            ) : (
              <div className="space-y-6">
                {currentConversation?.messages.map((msg, index) => (
                  <MessageBubble key={index} message={msg} index={index} />
                ))}
                
                {isTyping && <TypingIndicator />}
                
                <div ref={messagesEndRef} />
              </div>
            )}
          </ScrollArea>

          <div className="border-t border-gray-200 p-6 bg-white/80 backdrop-blur-sm">
            <div className="flex gap-3 items-end">
              <div className="flex-1 relative">
                <Input
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder={
                    currentLanguage === 'ar'
                      ? 'اكتب سؤالك القانوني هنا...'
                      : 'Tapez votre question juridique ici...'
                  }
                  disabled={isLoading}
                  className={`chat-input pr-4 pl-12 py-3 text-base border-2 border-gray-200 focus:border-blue-400 rounded-xl shadow-sm ${
                    currentLanguage === 'ar' ? 'text-right' : 'text-left'
                  }`}
                  style={{ direction: currentLanguage === 'ar' ? 'rtl' : 'ltr' }}
                />
                <MessageCircle className={`absolute top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 ${
                  currentLanguage === 'ar' ? 'right-4' : 'left-4'
                }`} />
              </div>
              <Button
                onClick={sendMessage}
                disabled={!message.trim() || isLoading}
                className="send-button h-12 w-12 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg hover:shadow-xl"
                size="icon"
              >
                {isLoading ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : (
                  <Send className="h-5 w-5" />
                )}
              </Button>
            </div>
            {message.trim() && (
              <div className={`mt-2 text-xs text-gray-500 flex items-center gap-1 ${
                currentLanguage === 'ar' ? 'justify-end' : 'justify-start'
              }`}>
                <span>
                  {currentLanguage === 'ar' ? 'اضغط Enter للإرسال' : 'Appuyez sur Entrée pour envoyer'}
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
