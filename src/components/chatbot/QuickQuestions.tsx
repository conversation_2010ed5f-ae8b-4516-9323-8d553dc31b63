import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MessageCircle } from 'lucide-react';
import { ChatbotService } from '@/services/chatbotService';

interface QuickQuestionsProps {
  onQuestionSelect: (question: string) => void;
}

export const QuickQuestions: React.FC<QuickQuestionsProps> = ({ onQuestionSelect }) => {
  const { i18n } = useTranslation();
  const currentLanguage = i18n.language as 'ar' | 'fr';
  
  const quickQuestions = ChatbotService.getQuickQuestions(currentLanguage);

  // Detect if text contains Arabic characters
  const isArabicText = (text: string): boolean => {
    const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]/;
    return arabicRegex.test(text);
  };

  return (
    <div className="space-y-6">
      <h4 className="font-semibold text-lg text-gray-800 text-center">
        {currentLanguage === 'ar' ? 'أسئلة سريعة للبدء:' : 'Questions rapides pour commencer:'}
      </h4>
      
      {quickQuestions.map((category, categoryIndex) => {
        const categoryIsArabic = isArabicText(category.category);
        
        return (
          <div key={categoryIndex} className="space-y-3">
            <Badge 
              variant="outline" 
              className="bg-blue-50 text-blue-700 border-blue-200 px-3 py-1"
              style={{ direction: categoryIsArabic ? 'rtl' : 'ltr' }}
            >
              {category.category}
            </Badge>
            
            <div className="grid gap-3">
              {category.questions.map((question, questionIndex) => {
                const questionIsArabic = isArabicText(question);
                
                return (
                  <Button
                    key={questionIndex}
                    variant="ghost"
                    className={`quick-question-button h-auto p-4 text-sm bg-white hover:bg-blue-50 border border-gray-200 hover:border-blue-300 shadow-sm hover:shadow-md ${
                      questionIsArabic ? 'text-right justify-end' : 'text-left justify-start'
                    }`}
                    onClick={() => onQuestionSelect(question)}
                    style={{ direction: questionIsArabic ? 'rtl' : 'ltr' }}
                  >
                    <div className={`flex items-center gap-2 w-full ${
                      questionIsArabic ? 'flex-row-reverse' : 'flex-row'
                    }`}>
                      <MessageCircle className="h-4 w-4 text-blue-500 flex-shrink-0" />
                      <span className="flex-1 leading-relaxed">{question}</span>
                    </div>
                  </Button>
                );
              })}
            </div>
          </div>
        );
      })}
    </div>
  );
};
