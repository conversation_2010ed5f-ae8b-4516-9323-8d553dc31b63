import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export const AuthDebug: React.FC = () => {
  const [connectionStatus, setConnectionStatus] = useState<'testing' | 'connected' | 'error'>('testing');
  const [sessionInfo, setSessionInfo] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    testConnection();
  }, []);

  const testConnection = async () => {
    try {
      setConnectionStatus('testing');
      setError(null);

      // Test 1: Basic connection
      console.log('Testing Supabase connection...');
      
      // Test 2: Get session
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        throw new Error(`Session error: ${sessionError.message}`);
      }

      setSessionInfo(sessionData);

      // Test 3: Try to query profiles table
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('count')
        .limit(1);

      if (profilesError) {
        console.warn('Profiles table error:', profilesError);
        // This is expected if table doesn't exist
      }

      setConnectionStatus('connected');
      console.log('Supabase connection successful');
      
    } catch (err: any) {
      console.error('Connection test failed:', err);
      setError(err.message);
      setConnectionStatus('error');
    }
  };

  const testSignIn = async () => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'testpassword'
      });

      if (error) {
        console.log('Expected signin error (no user):', error.message);
      } else {
        console.log('Signin successful:', data);
      }
    } catch (err) {
      console.error('Signin test error:', err);
    }
  };

  const getStatusBadge = () => {
    switch (connectionStatus) {
      case 'testing':
        return <Badge variant="secondary">Testing...</Badge>;
      case 'connected':
        return <Badge variant="default">Connected</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Supabase Connection Debug
            {getStatusBadge()}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-medium mb-2">Connection Status</h3>
            <p className="text-sm text-gray-600">
              Status: {connectionStatus}
            </p>
            {error && (
              <p className="text-sm text-red-600 mt-1">
                Error: {error}
              </p>
            )}
          </div>

          <div>
            <h3 className="font-medium mb-2">Session Info</h3>
            <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
              {JSON.stringify(sessionInfo, null, 2)}
            </pre>
          </div>

          <div>
            <h3 className="font-medium mb-2">Supabase Config</h3>
            <div className="text-xs space-y-1">
              <p>URL: https://stvxoaydqjutgqynewva.supabase.co</p>
              <p>Key: {supabase.supabaseKey.substring(0, 20)}...</p>
            </div>
          </div>

          <div className="flex gap-2">
            <Button onClick={testConnection} size="sm">
              Test Connection
            </Button>
            <Button onClick={testSignIn} size="sm" variant="outline">
              Test SignIn
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
