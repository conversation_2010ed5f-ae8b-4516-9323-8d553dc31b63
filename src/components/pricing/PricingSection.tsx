
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Check, Loader2 } from 'lucide-react';
import { SubscriptionService } from '@/services/subscriptionService';

export const PricingSection = () => {
  const { t } = useTranslation();
  const { user, profile } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState<string | null>(null);
  const [plans, setPlans] = useState<any[]>([]);
  const [plansLoading, setPlansLoading] = useState(true);

  // Load subscription plans from database
  useEffect(() => {
    const loadPlans = async () => {
      try {
        const { data, error } = await SubscriptionService.getSubscriptionPlans();
        if (error) {
          console.error('Error loading plans:', error);
          // Fallback to static plans if database fails
          setPlans(getStaticPlans());
        } else {
          // Map database plans to component format
          const mappedPlans = data.map((plan: any) => ({
            id: plan.id,
            title: plan.name_ar,
            price: plan.price === 0 ? 'مجاني' : `${plan.price} درهم/شهر`,
            features: plan.features_ar || [],
            cta: plan.price === 0 ? 'ابدأ مجاناً' : 'اشترك الآن',
            popular: plan.name.toLowerCase().includes('premium'),
            variant: plan.price === 0 ? 'outline' as const : 'default' as const,
            originalPlan: plan
          }));
          setPlans(mappedPlans);
        }
      } catch (error) {
        console.error('Error loading plans:', error);
        setPlans(getStaticPlans());
      } finally {
        setPlansLoading(false);
      }
    };

    loadPlans();
  }, []);

  const getStaticPlans = () => [
    {
      id: 'free',
      title: 'مجاني',
      price: 'مجاني',
      features: ['سؤال قانوني واحد شهرياً', 'إنشاء وثائق محدود', 'تجربة مجانية 7 أيام'],
      cta: 'ابدأ مجاناً',
      popular: false,
      variant: 'outline' as const,
    },
    {
      id: 'pro_user',
      title: 'المستخدم المتميز',
      price: '100 درهم/شهر',
      features: ['أسئلة قانونية غير محدودة', 'إنشاء وثائق غير محدود', 'دعم ذو أولوية', 'وصول كامل للمحامين'],
      cta: 'اشترك الآن',
      popular: true,
      variant: 'default' as const,
    },
    {
      id: 'pro_advocate',
      title: 'المحامي المتميز',
      price: '500 درهم/شهر',
      features: ['ملف شخصي مميز في البحث', 'أدوات الحجز والتحليلات', 'تحديد الأوقات والأسعار', 'أولوية في عرض الملف الشخصي'],
      cta: 'اشترك كمحامي',
      popular: false,
      variant: 'outline' as const,
    },
  ];

  const handleSubscribe = async (plan: any) => {
    if (!user) {
      toast({
        title: 'مطلوب تسجيل الدخول',
        description: 'يجب تسجيل الدخول أولاً للاشتراك',
        variant: 'destructive',
      });
      return;
    }

    // For free plan, handle differently
    if (plan.originalPlan?.price === 0 || plan.id === 'free') {
      if (profile?.subscription_tier === 'free') {
        toast({
          title: 'الخطة المجانية',
          description: 'أنت تستخدم الخطة المجانية بالفعل',
        });
        return;
      }
    }

    setLoading(plan.id);

    try {
      console.log('Subscribing to plan:', plan);

      // Use the plan ID from database or fallback to static ID
      const planId = plan.originalPlan?.id || plan.id;
      const paymentMethod = plan.originalPlan?.price === 0 ? 'free' : 'credit_card';

      const { data, error } = await SubscriptionService.subscribeUser(
        user.id,
        planId,
        paymentMethod
      );

      if (error) {
        console.error('Subscription error:', error);
        throw new Error(typeof error === 'string' ? error : error.message || 'فشل في تفعيل الاشتراك');
      }

      toast({
        title: 'تم الاشتراك بنجاح',
        description: `تم تفعيل اشتراكك في باقة ${plan.title}`,
      });

      // Refresh profile to update subscription status
      if (profile) {
        window.location.reload(); // Simple refresh for now
      }

    } catch (error: any) {
      console.error('Subscription error:', error);

      let errorMessage = 'فشل في تفعيل الاشتراك';

      if (error.message) {
        if (error.message.includes('duplicate') || error.message.includes('unique')) {
          errorMessage = 'لديك اشتراك نشط بالفعل. يرجى إلغاء الاشتراك الحالي أولاً.';
        } else if (error.message.includes('foreign key') || error.message.includes('not found')) {
          errorMessage = 'خطأ في البيانات. يرجى المحاولة مرة أخرى.';
        } else if (error.message.includes('permission') || error.message.includes('policy')) {
          errorMessage = 'ليس لديك صلاحية لتنفيذ هذا الإجراء.';
        } else {
          errorMessage = error.message;
        }
      }

      toast({
        title: 'خطأ في الاشتراك',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setLoading(null);
    }
  };



  if (plansLoading) {
    return (
      <section id="pricing" className="py-12 sm:py-20 bg-gray-50">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-gray-600">جاري تحميل الباقات...</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="pricing" className="py-12 sm:py-20 bg-gray-50">
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            خطط الاشتراك
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            اختر الخطة التي تناسب احتياجاتك القانونية
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 max-w-6xl mx-auto">
          {plans.map((plan) => {
            const isCurrentPlan = profile?.subscription_tier === plan.id ||
                                 (plan.originalPlan && profile?.subscription_tier === plan.originalPlan.id);
            const isLoading = loading === plan.id;

            return (
              <Card key={plan.id} className={`relative ${plan.popular ? 'border-blue-500 shadow-lg scale-105' : ''}`}>
                {plan.popular && (
                  <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-blue-500">
                    الأكثر شعبية
                  </Badge>
                )}

                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{plan.title}</CardTitle>
                  <CardDescription className="text-3xl font-bold text-blue-600">
                    {plan.price}
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-4">
                  <ul className="space-y-3">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center gap-3">
                        <Check className="h-5 w-5 text-green-500 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Button
                    className="w-full mt-6"
                    variant={plan.variant}
                    size="lg"
                    onClick={() => handleSubscribe(plan)}
                    disabled={isCurrentPlan || isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        جاري التحميل...
                      </>
                    ) : isCurrentPlan ? 'الخطة الحالية' : plan.cta}
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {profile?.subscription_tier && profile.subscription_tier !== 'free' && (
          <div className="text-center mt-8">
            <p className="text-gray-600 mb-4">
              تريد إدارة اشتراكك؟
            </p>
            <Button
              variant="outline"
              onClick={async () => {
                try {
                  // For now, show a simple message since we don't have Stripe portal
                  toast({
                    title: 'إدارة الاشتراك',
                    description: 'يمكنك إدارة اشتراكك من خلال الاتصال بالدعم الفني',
                  });
                } catch (error) {
                  toast({
                    title: 'خطأ',
                    description: 'حدث خطأ أثناء فتح لوحة الإدارة',
                    variant: 'destructive',
                  });
                }
              }}
            >
              إدارة الاشتراك
            </Button>
          </div>
        )}
      </div>
    </section>
  );
};
