import React, { useState } from 'react';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StarRatingProps {
  rating: number;
  onRatingChange?: (rating: number) => void;
  readonly?: boolean;
  size?: 'sm' | 'md' | 'lg';
  showValue?: boolean;
  className?: string;
}

export const StarRating: React.FC<StarRatingProps> = ({
  rating,
  onRatingChange,
  readonly = false,
  size = 'md',
  showValue = false,
  className
}) => {
  const [hoverRating, setHoverRating] = useState(0);

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  const handleStarClick = (starRating: number) => {
    if (!readonly && onRatingChange) {
      onRatingChange(starRating);
    }
  };

  const handleStarHover = (starRating: number) => {
    if (!readonly) {
      setHoverRating(starRating);
    }
  };

  const handleMouseLeave = () => {
    if (!readonly) {
      setHoverRating(0);
    }
  };

  const getStarFill = (starIndex: number) => {
    const currentRating = hoverRating || rating;
    if (starIndex <= currentRating) {
      return 'fill-yellow-400 text-yellow-400';
    }
    return 'fill-gray-200 text-gray-200';
  };

  return (
    <div className={cn('flex items-center gap-1', className)}>
      <div 
        className="flex items-center gap-0.5"
        onMouseLeave={handleMouseLeave}
      >
        {[1, 2, 3, 4, 5].map((starIndex) => (
          <Star
            key={starIndex}
            className={cn(
              sizeClasses[size],
              getStarFill(starIndex),
              !readonly && 'cursor-pointer hover:scale-110 transition-transform',
              readonly && 'cursor-default'
            )}
            onClick={() => handleStarClick(starIndex)}
            onMouseEnter={() => handleStarHover(starIndex)}
          />
        ))}
      </div>
      
      {showValue && (
        <span className="text-sm text-gray-600 mr-1">
          ({rating.toFixed(1)})
        </span>
      )}
    </div>
  );
};

interface RatingDisplayProps {
  rating: number;
  totalRatings: number;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const RatingDisplay: React.FC<RatingDisplayProps> = ({
  rating,
  totalRatings,
  size = 'md',
  className
}) => {
  return (
    <div className={cn('flex items-center gap-2', className)}>
      <StarRating 
        rating={rating} 
        readonly 
        size={size}
      />
      <span className="text-sm text-gray-600">
        {rating.toFixed(1)} ({totalRatings} {totalRatings === 1 ? 'تقييم' : 'تقييم'})
      </span>
    </div>
  );
};

interface InteractiveRatingProps {
  currentRating?: number;
  onRatingSubmit: (rating: number, comment?: string) => void;
  onCancel?: () => void;
  isSubmitting?: boolean;
  allowComment?: boolean;
  className?: string;
}

export const InteractiveRating: React.FC<InteractiveRatingProps> = ({
  currentRating = 0,
  onRatingSubmit,
  onCancel,
  isSubmitting = false,
  allowComment = true,
  className
}) => {
  const [selectedRating, setSelectedRating] = useState(currentRating);
  const [comment, setComment] = useState('');
  const [showComment, setShowComment] = useState(false);

  const handleRatingChange = (rating: number) => {
    setSelectedRating(rating);
    if (rating > 0) {
      setShowComment(true);
    }
  };

  const handleSubmit = () => {
    if (selectedRating > 0) {
      onRatingSubmit(selectedRating, comment.trim() || undefined);
    }
  };

  const handleCancel = () => {
    setSelectedRating(currentRating);
    setComment('');
    setShowComment(false);
    if (onCancel) {
      onCancel();
    }
  };

  return (
    <div className={cn('space-y-4', className)}>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          قيم هذه الإجابة:
        </label>
        <StarRating
          rating={selectedRating}
          onRatingChange={handleRatingChange}
          size="lg"
        />
      </div>

      {showComment && allowComment && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            تعليق (اختياري):
          </label>
          <textarea
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="شارك رأيك حول هذه الإجابة..."
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            rows={3}
            maxLength={500}
          />
          <div className="text-xs text-gray-500 mt-1">
            {comment.length}/500 حرف
          </div>
        </div>
      )}

      {selectedRating > 0 && (
        <div className="flex gap-2">
          <button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                جاري الحفظ...
              </>
            ) : (
              'حفظ التقييم'
            )}
          </button>
          
          <button
            onClick={handleCancel}
            disabled={isSubmitting}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            إلغاء
          </button>
        </div>
      )}
    </div>
  );
};
