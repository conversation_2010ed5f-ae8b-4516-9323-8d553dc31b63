import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { StarRating, RatingDisplay, InteractiveRating } from './StarRating';
import { QuestionsService } from '@/services/questionsService';

interface TestRatingSystemProps {
  userId: string;
}

export const TestRatingSystem: React.FC<TestRatingSystemProps> = ({ userId }) => {
  const { toast } = useToast();
  const [testRating, setTestRating] = useState(0);
  const [showInteractive, setShowInteractive] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleTestRating = async (rating: number, comment?: string) => {
    setIsSubmitting(true);
    try {
      // This is a test - in real usage, you'd have a real response ID
      const testResponseId = '00000000-0000-0000-0000-000000000001';
      
      const { data, error } = await QuestionsService.rateResponse(
        testResponseId,
        userId,
        rating,
        comment
      );

      if (error) {
        throw error;
      }

      toast({
        title: 'تم حفظ التقييم التجريبي',
        description: `تم حفظ التقييم: ${rating} نجوم`,
      });

      setShowInteractive(false);
      setTestRating(rating);
    } catch (error: any) {
      toast({
        title: 'خطأ في التقييم التجريبي',
        description: error.message || 'فشل في حفظ التقييم',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>اختبار نظام التقييم بالنجوم</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Star Rating */}
          <div>
            <h3 className="font-medium mb-2">تقييم أساسي (للقراءة فقط)</h3>
            <StarRating rating={4.5} readonly showValue />
          </div>

          {/* Interactive Star Rating */}
          <div>
            <h3 className="font-medium mb-2">تقييم تفاعلي</h3>
            <StarRating 
              rating={testRating} 
              onRatingChange={setTestRating}
              showValue
            />
            <p className="text-sm text-gray-600 mt-1">
              التقييم الحالي: {testRating} نجوم
            </p>
          </div>

          {/* Rating Display */}
          <div>
            <h3 className="font-medium mb-2">عرض التقييم مع العدد الإجمالي</h3>
            <RatingDisplay rating={4.2} totalRatings={156} />
          </div>

          {/* Interactive Rating Component */}
          <div>
            <h3 className="font-medium mb-2">واجهة التقييم الكاملة</h3>
            {showInteractive ? (
              <InteractiveRating
                currentRating={testRating}
                onRatingSubmit={handleTestRating}
                onCancel={() => setShowInteractive(false)}
                isSubmitting={isSubmitting}
              />
            ) : (
              <Button onClick={() => setShowInteractive(true)}>
                اختبار التقييم التفاعلي
              </Button>
            )}
          </div>

          {/* Different Sizes */}
          <div>
            <h3 className="font-medium mb-2">أحجام مختلفة</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-4">
                <span className="text-sm w-16">صغير:</span>
                <StarRating rating={3} readonly size="sm" />
              </div>
              <div className="flex items-center gap-4">
                <span className="text-sm w-16">متوسط:</span>
                <StarRating rating={4} readonly size="md" />
              </div>
              <div className="flex items-center gap-4">
                <span className="text-sm w-16">كبير:</span>
                <StarRating rating={5} readonly size="lg" />
              </div>
            </div>
          </div>

          {/* Test Database Functions */}
          <div>
            <h3 className="font-medium mb-2">اختبار وظائف قاعدة البيانات</h3>
            <div className="space-y-2">
              <Button
                variant="outline"
                onClick={async () => {
                  try {
                    const { data, error } = await QuestionsService.getResponseRating(
                      '00000000-0000-0000-0000-000000000001',
                      userId
                    );
                    
                    toast({
                      title: 'نتيجة الاختبار',
                      description: data ? `تم العثور على تقييم: ${data.rating}` : 'لا يوجد تقييم',
                    });
                  } catch (error: any) {
                    toast({
                      title: 'خطأ في الاختبار',
                      description: error.message,
                      variant: 'destructive',
                    });
                  }
                }}
              >
                اختبار جلب التقييم
              </Button>

              <Button
                variant="outline"
                onClick={async () => {
                  try {
                    const { data, error } = await QuestionsService.getQuestionWithResponsesAndRatings(
                      '00000000-0000-0000-0000-000000000001',
                      userId
                    );
                    
                    console.log('Question with responses and ratings:', data);
                    toast({
                      title: 'نتيجة الاختبار',
                      description: data ? 'تم جلب السؤال مع التقييمات' : 'فشل في جلب البيانات',
                    });
                  } catch (error: any) {
                    toast({
                      title: 'خطأ في الاختبار',
                      description: error.message,
                      variant: 'destructive',
                    });
                  }
                }}
              >
                اختبار جلب السؤال مع التقييمات
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
