import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { QuestionDetailView } from './QuestionDetailView';
import { RatingDisplay } from './StarRating';
import { QuestionsService, LegalQuestion } from '@/services/questionsService';
import { 
  Eye, 
  MessageCircle, 
  Clock, 
  CheckCircle, 
  Search,
  Filter,
  RefreshCw,
  Calendar,
  User,
  Star,
  Plus,
  Loader2
} from 'lucide-react';

interface QuestionsListProps {
  userId: string;
  onCreateQuestion?: () => void;
}

export const QuestionsList: React.FC<QuestionsListProps> = ({
  userId,
  onCreateQuestion
}) => {
  const { toast } = useToast();
  const [questions, setQuestions] = useState<LegalQuestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedQuestionId, setSelectedQuestionId] = useState<string | null>(null);
  
  // Filters
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    category: '',
    isAnswered: ''
  });

  useEffect(() => {
    fetchQuestions();
  }, [userId]);

  const fetchQuestions = async () => {
    try {
      setLoading(true);
      const { data, error } = await QuestionsService.getUserQuestions(userId);
      
      if (error) {
        throw error;
      }
      
      setQuestions(data || []);
    } catch (error: any) {
      console.error('Error fetching questions:', error);
      toast({
        title: 'خطأ في تحميل الأسئلة',
        description: error.message || 'فشل في تحميل الأسئلة',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchQuestions();
    setRefreshing(false);
    toast({
      title: 'تم تحديث الأسئلة',
      description: 'تم تحديث قائمة الأسئلة بنجاح',
    });
  };

  const getCategoryLabel = (category: string) => {
    const categories: Record<string, string> = {
      general: 'عام',
      family: 'أحوال شخصية',
      commercial: 'تجاري',
      criminal: 'جنائي',
      civil: 'مدني',
      administrative: 'إداري',
      labor: 'عمالي',
      real_estate: 'عقاري'
    };
    return categories[category] || category;
  };

  const getStatusBadge = (question: LegalQuestion) => {
    if (question.is_answered) {
      return <Badge className="bg-green-100 text-green-800">تم الرد</Badge>;
    }
    
    switch (question.status) {
      case 'pending':
        return <Badge variant="outline" className="text-orange-600 border-orange-600">في الانتظار</Badge>;
      case 'assigned':
        return <Badge variant="outline" className="text-blue-600 border-blue-600">تم التعيين</Badge>;
      case 'answered':
        return <Badge className="bg-green-100 text-green-800">تم الرد</Badge>;
      default:
        return <Badge variant="outline">{question.status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Filter questions based on search and filters
  const filteredQuestions = questions.filter(question => {
    const matchesSearch = !filters.search || 
      question.title.toLowerCase().includes(filters.search.toLowerCase()) ||
      question.description.toLowerCase().includes(filters.search.toLowerCase());
    
    const matchesStatus = !filters.status || question.status === filters.status;
    const matchesCategory = !filters.category || question.category === filters.category;
    const matchesAnswered = !filters.isAnswered || 
      (filters.isAnswered === 'true' ? question.is_answered : !question.is_answered);
    
    return matchesSearch && matchesStatus && matchesCategory && matchesAnswered;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="mr-2">جاري تحميل الأسئلة...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">أسئلتي القانونية</h2>
          <p className="text-gray-600">إدارة وتتبع أسئلتك القانونية</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            تحديث
          </Button>
          {onCreateQuestion && (
            <Button onClick={onCreateQuestion} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              سؤال جديد
            </Button>
          )}
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="البحث في الأسئلة..."
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                className="pl-10"
              />
            </div>
            
            <Select
              value={filters.status}
              onValueChange={(value) => setFilters({ ...filters, status: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">جميع الحالات</SelectItem>
                <SelectItem value="pending">في الانتظار</SelectItem>
                <SelectItem value="assigned">تم التعيين</SelectItem>
                <SelectItem value="answered">تم الرد</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.category}
              onValueChange={(value) => setFilters({ ...filters, category: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="التصنيف" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">جميع التصنيفات</SelectItem>
                <SelectItem value="general">عام</SelectItem>
                <SelectItem value="family">أحوال شخصية</SelectItem>
                <SelectItem value="commercial">تجاري</SelectItem>
                <SelectItem value="criminal">جنائي</SelectItem>
                <SelectItem value="civil">مدني</SelectItem>
                <SelectItem value="administrative">إداري</SelectItem>
                <SelectItem value="labor">عمالي</SelectItem>
                <SelectItem value="real_estate">عقاري</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.isAnswered}
              onValueChange={(value) => setFilters({ ...filters, isAnswered: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="الردود" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">الكل</SelectItem>
                <SelectItem value="true">تم الرد عليها</SelectItem>
                <SelectItem value="false">لم يتم الرد عليها</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Questions List */}
      {filteredQuestions.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <MessageCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {questions.length === 0 ? 'لا توجد أسئلة بعد' : 'لا توجد أسئلة مطابقة للفلاتر'}
            </h3>
            <p className="text-gray-600 mb-4">
              {questions.length === 0 ? 'ابدأ بطرح سؤالك الأول' : 'جرب تغيير الفلاتر أو البحث'}
            </p>
            {questions.length === 0 && onCreateQuestion && (
              <Button onClick={onCreateQuestion}>
                <Plus className="h-4 w-4 mr-2" />
                طرح سؤال جديد
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 gap-4">
          {filteredQuestions.map((question) => (
            <Card key={question.id} className="hover:shadow-md transition-shadow h-fit">
              <CardHeader className="pb-3">
                <div className="space-y-3">
                  <div className="flex justify-between items-start gap-3">
                    <CardTitle className="text-lg leading-tight flex-1">{question.title}</CardTitle>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedQuestionId(question.id)}
                      className="flex items-center gap-1 shrink-0"
                    >
                      <Eye className="h-3 w-3" />
                      <span className="hidden sm:inline">عرض التفاصيل</span>
                      <span className="sm:hidden">عرض</span>
                    </Button>
                  </div>

                  <p className="text-gray-600 text-sm line-clamp-3">
                    {question.description}
                  </p>

                  <div className="flex flex-wrap gap-2">
                    {getStatusBadge(question)}
                    <Badge variant="outline">
                      {getCategoryLabel(question.category || 'general')}
                    </Badge>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <div className="flex flex-wrap items-center gap-3 text-sm text-gray-500">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span className="text-xs">{formatDate(question.created_at)}</span>
                  </div>
                  {question.advocate_profile && (
                    <div className="flex items-center gap-1">
                      <User className="h-3 w-3" />
                      <span className="text-xs truncate max-w-[120px]">{question.advocate_profile.full_name}</span>
                    </div>
                  )}
                  {question.is_answered && (
                    <div className="flex items-center gap-1">
                      <CheckCircle className="h-3 w-3 text-green-600" />
                      <span className="text-xs">تم الرد</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Question Detail Modal */}
      {selectedQuestionId && (
        <QuestionDetailView
          questionId={selectedQuestionId}
          userId={userId}
          onClose={() => setSelectedQuestionId(null)}
        />
      )}
    </div>
  );
};
