import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { QuestionDetailView } from './QuestionDetailView';
import { 
  Monitor, 
  Tablet, 
  Smartphone, 
  Eye,
  Calendar,
  User,
  CheckCircle,
  MessageCircle
} from 'lucide-react';

interface WidthTestComponentProps {
  userId: string;
}

export const WidthTestComponent: React.FC<WidthTestComponentProps> = ({ userId }) => {
  const [showModal, setShowModal] = useState(false);
  const [currentBreakpoint, setCurrentBreakpoint] = useState('desktop');

  // Mock data for testing
  const mockQuestions = [
    {
      id: '1',
      title: 'سؤال قانوني حول العقود التجارية والالتزامات المالية',
      description: 'أحتاج إلى استشارة قانونية حول عقد تجاري معقد يتضمن عدة أطراف وشروط مالية متنوعة. هل يمكن للمحامي مراجعة العقد وتقديم النصائح اللازمة؟',
      category: 'commercial',
      status: 'answered',
      is_answered: true,
      created_at: new Date().toISOString(),
      advocate_profile: {
        full_name: 'المحامي أحمد محمد'
      }
    },
    {
      id: '2',
      title: 'استفسار حول قانون الأسرة والطلاق',
      description: 'لدي استفسار حول إجراءات الطلاق وحقوق الحضانة في القانون المغربي.',
      category: 'family',
      status: 'pending',
      is_answered: false,
      created_at: new Date().toISOString()
    },
    {
      id: '3',
      title: 'مشكلة في العقارات والملكية',
      description: 'أواجه مشكلة في نقل ملكية عقار ورثته من والدي.',
      category: 'real_estate',
      status: 'assigned',
      is_answered: false,
      created_at: new Date().toISOString(),
      advocate_profile: {
        full_name: 'المحامية فاطمة الزهراء'
      }
    }
  ];

  const getCategoryLabel = (category: string) => {
    const categories: Record<string, string> = {
      commercial: 'تجاري',
      family: 'أحوال شخصية',
      real_estate: 'عقاري'
    };
    return categories[category] || category;
  };

  const getStatusBadge = (status: string, isAnswered: boolean) => {
    if (isAnswered) {
      return <Badge className="bg-green-100 text-green-800">تم الرد</Badge>;
    }
    
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-orange-600 border-orange-600">في الانتظار</Badge>;
      case 'assigned':
        return <Badge variant="outline" className="text-blue-600 border-blue-600">تم التعيين</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      {/* Header with breakpoint indicators */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            اختبار عرض الشاشة والاستجابة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2 mb-4">
            <div className="flex items-center gap-2 text-sm">
              <Smartphone className="h-4 w-4" />
              <span className="block sm:hidden text-green-600 font-medium">Mobile (< 640px)</span>
              <span className="hidden sm:block text-gray-400">Mobile</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Tablet className="h-4 w-4" />
              <span className="hidden sm:block md:hidden text-green-600 font-medium">Tablet (640px - 768px)</span>
              <span className="hidden md:block text-gray-400">Tablet</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Monitor className="h-4 w-4" />
              <span className="hidden md:block lg:hidden text-green-600 font-medium">Desktop Small (768px - 1024px)</span>
              <span className="hidden lg:block xl:hidden text-green-600 font-medium">Desktop Medium (1024px - 1280px)</span>
              <span className="hidden xl:block 2xl:hidden text-green-600 font-medium">Desktop Large (1280px - 1536px)</span>
              <span className="hidden 2xl:block text-green-600 font-medium">Desktop XL (> 1536px)</span>
            </div>
          </div>
          
          <div className="text-sm text-gray-600">
            <p>العرض الحالي: <span className="font-mono bg-gray-100 px-2 py-1 rounded">{window.innerWidth}px</span></p>
            <p className="mt-1">هذا المكون يختبر كيفية استخدام المساحة الكاملة للشاشة</p>
          </div>
        </CardContent>
      </Card>

      {/* Test full-width container */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>اختبار الحاوي كامل العرض</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="w-full bg-blue-50 p-4 rounded border-2 border-dashed border-blue-200">
            <p className="text-center text-blue-800">
              هذا الحاوي يستخدم العرض الكامل (w-full) - يجب أن يمتد من الحافة إلى الحافة
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Test responsive grid */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>اختبار الشبكة المتجاوبة للأسئلة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 gap-4">
            {mockQuestions.map((question) => (
              <Card key={question.id} className="hover:shadow-md transition-shadow h-fit">
                <CardHeader className="pb-3">
                  <div className="space-y-3">
                    <div className="flex justify-between items-start gap-3">
                      <CardTitle className="text-lg leading-tight flex-1">{question.title}</CardTitle>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowModal(true)}
                        className="flex items-center gap-1 shrink-0"
                      >
                        <Eye className="h-3 w-3" />
                        <span className="hidden sm:inline">عرض التفاصيل</span>
                        <span className="sm:hidden">عرض</span>
                      </Button>
                    </div>
                    
                    <p className="text-gray-600 text-sm line-clamp-3">
                      {question.description}
                    </p>
                    
                    <div className="flex flex-wrap gap-2">
                      {getStatusBadge(question.status, question.is_answered)}
                      <Badge variant="outline">
                        {getCategoryLabel(question.category)}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <div className="flex flex-wrap items-center gap-3 text-sm text-gray-500">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span className="text-xs">{formatDate(question.created_at)}</span>
                    </div>
                    {question.advocate_profile && (
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span className="text-xs truncate max-w-[120px]">{question.advocate_profile.full_name}</span>
                      </div>
                    )}
                    {question.is_answered && (
                      <div className="flex items-center gap-1">
                        <CheckCircle className="h-3 w-3 text-green-600" />
                        <span className="text-xs">تم الرد</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Test filters grid */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>اختبار شبكة الفلاتر</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-4">
            <div className="bg-gray-100 p-4 rounded text-center">
              <p className="text-sm">فلتر البحث</p>
            </div>
            <div className="bg-gray-100 p-4 rounded text-center">
              <p className="text-sm">فلتر الحالة</p>
            </div>
            <div className="bg-gray-100 p-4 rounded text-center">
              <p className="text-sm">فلتر التصنيف</p>
            </div>
            <div className="bg-gray-100 p-4 rounded text-center">
              <p className="text-sm">فلتر الردود</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test modal button */}
      <Card>
        <CardHeader>
          <CardTitle>اختبار النافذة المنبثقة كاملة العرض</CardTitle>
        </CardHeader>
        <CardContent>
          <Button onClick={() => setShowModal(true)} className="w-full">
            فتح نافذة التفاصيل (كامل العرض)
          </Button>
          <p className="text-sm text-gray-600 mt-2">
            النافذة المنبثقة يجب أن تستخدم العرض الكامل مع هوامش متجاوبة
          </p>
        </CardContent>
      </Card>

      {/* Mock Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-2 sm:p-4 z-50">
          <Card className="w-full max-w-none mx-4 sm:mx-8 lg:mx-16 xl:mx-24 max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>اختبار النافذة كاملة العرض</CardTitle>
                <Button variant="ghost" size="sm" onClick={() => setShowModal(false)}>
                  ✕
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-green-50 p-4 rounded border border-green-200">
                  <p className="text-green-800">
                    ✅ هذه النافذة تستخدم العرض الكامل مع هوامش متجاوبة:
                  </p>
                  <ul className="list-disc list-inside mt-2 text-sm text-green-700">
                    <li>Mobile: هامش 16px من كل جانب</li>
                    <li>Small screens: هامش 32px من كل جانب</li>
                    <li>Large screens: هامش 64px من كل جانب</li>
                    <li>XL screens: هامش 96px من كل جانب</li>
                  </ul>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[1, 2, 3, 4, 5, 6].map((i) => (
                    <div key={i} className="bg-gray-100 p-4 rounded text-center">
                      <p>محتوى تجريبي {i}</p>
                    </div>
                  ))}
                </div>
                
                <div className="text-center">
                  <p className="text-gray-600">
                    العرض الحالي للنافذة يتكيف مع حجم الشاشة ويستخدم المساحة المتاحة بكفاءة
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};
