import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { StarRating, RatingDisplay, InteractiveRating } from './StarRating';
import { QuestionsService } from '@/services/questionsService';
import { 
  X, 
  Calendar, 
  User, 
  MessageCircle, 
  Clock,
  CheckCircle,
  AlertCircle,
  Star,
  Award
} from 'lucide-react';

interface QuestionDetailViewProps {
  questionId: string;
  userId: string;
  onClose: () => void;
}

interface QuestionWithResponses {
  question: {
    id: string;
    title: string;
    description: string;
    category: string;
    status: string;
    is_answered: boolean;
    created_at: string;
    updated_at: string;
  };
  user_profile: {
    full_name: string;
    email: string;
  };
  responses: Array<{
    id: string;
    response_text: string;
    is_approved: boolean;
    created_at: string;
    updated_at: string;
    advocate: {
      id: string;
      full_name: string;
      email: string;
      specializations: string[];
      rating: number;
      total_reviews: number;
    };
    user_rating?: number;
    user_rating_comment?: string;
    average_rating: number;
    total_ratings: number;
  }>;
}

export const QuestionDetailView: React.FC<QuestionDetailViewProps> = ({
  questionId,
  userId,
  onClose
}) => {
  const { toast } = useToast();
  const [questionData, setQuestionData] = useState<QuestionWithResponses | null>(null);
  const [loading, setLoading] = useState(true);
  const [ratingResponse, setRatingResponse] = useState<string | null>(null);
  const [submittingRating, setSubmittingRating] = useState(false);

  useEffect(() => {
    fetchQuestionDetails();
  }, [questionId, userId]);

  const fetchQuestionDetails = async () => {
    try {
      setLoading(true);
      const { data, error } = await QuestionsService.getQuestionWithResponsesAndRatings(questionId, userId);
      
      if (error) {
        throw error;
      }
      
      setQuestionData(data);
    } catch (error: any) {
      console.error('Error fetching question details:', error);
      toast({
        title: 'خطأ في تحميل التفاصيل',
        description: error.message || 'فشل في تحميل تفاصيل السؤال',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRatingSubmit = async (responseId: string, rating: number, comment?: string) => {
    try {
      setSubmittingRating(true);
      const { error } = await QuestionsService.rateResponse(responseId, userId, rating, comment);
      
      if (error) {
        throw error;
      }
      
      toast({
        title: 'تم حفظ التقييم',
        description: 'شكراً لك على تقييم الإجابة',
      });
      
      // Refresh question details to show updated rating
      await fetchQuestionDetails();
      setRatingResponse(null);
    } catch (error: any) {
      console.error('Error submitting rating:', error);
      toast({
        title: 'خطأ في حفظ التقييم',
        description: error.message || 'فشل في حفظ التقييم',
        variant: 'destructive',
      });
    } finally {
      setSubmittingRating(false);
    }
  };

  const getCategoryLabel = (category: string) => {
    const categories: Record<string, string> = {
      general: 'عام',
      family: 'أحوال شخصية',
      commercial: 'تجاري',
      criminal: 'جنائي',
      civil: 'مدني',
      administrative: 'إداري',
      labor: 'عمالي',
      real_estate: 'عقاري'
    };
    return categories[category] || category;
  };

  const getStatusBadge = (status: string, isAnswered: boolean) => {
    if (isAnswered) {
      return <Badge className="bg-green-100 text-green-800">تم الرد</Badge>;
    }
    
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-orange-600 border-orange-600">في الانتظار</Badge>;
      case 'assigned':
        return <Badge variant="outline" className="text-blue-600 border-blue-600">تم التعيين</Badge>;
      case 'answered':
        return <Badge className="bg-green-100 text-green-800">تم الرد</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-2 sm:p-4 z-50">
        <Card className="w-full max-w-none mx-4 sm:mx-8 lg:mx-16 xl:mx-24">
          <CardContent className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
            <span>جاري تحميل التفاصيل...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!questionData) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-2 sm:p-4 z-50">
        <Card className="w-full max-w-none mx-4 sm:mx-8 lg:mx-16 xl:mx-24">
          <CardContent className="text-center py-12">
            <AlertCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لم يتم العثور على السؤال</h3>
            <Button onClick={onClose}>إغلاق</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-2 sm:p-4 z-50">
      <Card className="w-full max-w-none mx-4 sm:mx-8 lg:mx-16 xl:mx-24 max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl">{questionData.question.title}</CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex flex-wrap gap-2 mt-2">
            {getStatusBadge(questionData.question.status, questionData.question.is_answered)}
            <Badge variant="outline">
              {getCategoryLabel(questionData.question.category)}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Question Details */}
          <div>
            <h3 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
              <MessageCircle className="h-4 w-4" />
              تفاصيل السؤال
            </h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-gray-700 whitespace-pre-wrap">{questionData.question.description}</p>
              <div className="flex items-center gap-4 mt-3 text-sm text-gray-500">
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {formatDate(questionData.question.created_at)}
                </div>
                <div className="flex items-center gap-1">
                  <User className="h-3 w-3" />
                  {questionData.user_profile.full_name}
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Responses */}
          <div>
            <h3 className="font-medium text-gray-900 mb-4 flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              الردود ({questionData.responses.length})
            </h3>
            
            {questionData.responses.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Clock className="h-12 w-12 mx-auto mb-2 text-gray-400" />
                <p>لم يتم الرد على هذا السؤال بعد</p>
                <p className="text-sm">سيتم إشعارك عند وصول رد من المحامين</p>
              </div>
            ) : (
              <div className="space-y-4">
                {questionData.responses.map((response) => (
                  <Card key={response.id} className="border-l-4 border-l-blue-500">
                    <CardContent className="p-4">
                      {/* Advocate Info */}
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <div className="bg-blue-100 p-2 rounded-full">
                            <User className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900">{response.advocate.full_name}</h4>
                            <div className="flex items-center gap-2 text-sm text-gray-500">
                              <RatingDisplay 
                                rating={response.advocate.rating} 
                                totalRatings={response.advocate.total_reviews}
                                size="sm"
                              />
                            </div>
                          </div>
                        </div>
                        <div className="text-sm text-gray-500">
                          {formatDate(response.created_at)}
                        </div>
                      </div>

                      {/* Response Text */}
                      <div className="mb-4">
                        <p className="text-gray-700 whitespace-pre-wrap">{response.response_text}</p>
                      </div>

                      {/* Specializations */}
                      {response.advocate.specializations && response.advocate.specializations.length > 0 && (
                        <div className="mb-4">
                          <div className="flex flex-wrap gap-1">
                            {response.advocate.specializations.map((spec, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {spec}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}

                      <Separator className="my-4" />

                      {/* Rating Section */}
                      <div>
                        {response.user_rating ? (
                          <div className="bg-green-50 p-3 rounded-lg">
                            <div className="flex items-center gap-2 mb-2">
                              <Award className="h-4 w-4 text-green-600" />
                              <span className="text-sm font-medium text-green-800">تقييمك لهذه الإجابة:</span>
                            </div>
                            <StarRating rating={response.user_rating} readonly size="sm" />
                            {response.user_rating_comment && (
                              <p className="text-sm text-green-700 mt-2">{response.user_rating_comment}</p>
                            )}
                          </div>
                        ) : (
                          <div>
                            {ratingResponse === response.id ? (
                              <InteractiveRating
                                onRatingSubmit={(rating, comment) => handleRatingSubmit(response.id, rating, comment)}
                                onCancel={() => setRatingResponse(null)}
                                isSubmitting={submittingRating}
                              />
                            ) : (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setRatingResponse(response.id)}
                                className="flex items-center gap-2"
                              >
                                <Star className="h-3 w-3" />
                                قيم هذه الإجابة
                              </Button>
                            )}
                          </div>
                        )}

                        {/* Response Rating Summary */}
                        {response.total_ratings > 0 && (
                          <div className="mt-3 pt-3 border-t">
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <span>تقييم الإجابة:</span>
                              <RatingDisplay 
                                rating={response.average_rating} 
                                totalRatings={response.total_ratings}
                                size="sm"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
