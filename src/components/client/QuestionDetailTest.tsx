import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { QuestionDetailView } from './QuestionDetailView';
import { 
  Eye, 
  Calendar, 
  User, 
  CheckCircle,
  AlertTriangle,
  TestTube
} from 'lucide-react';

interface QuestionDetailTestProps {
  userId: string;
}

export const QuestionDetailTest: React.FC<QuestionDetailTestProps> = ({ userId }) => {
  const { toast } = useToast();
  const [selectedQuestionId, setSelectedQuestionId] = useState<string | null>(null);
  const [testMode, setTestMode] = useState<'real' | 'mock'>('mock');

  // Mock question data for testing
  const mockQuestions = [
    {
      id: 'test-question-1',
      title: 'سؤال تجريبي حول العقود التجارية',
      description: 'هذا سؤال تجريبي لاختبار عرض التفاصيل. يحتوي على نص طويل لاختبار كيفية عرض المحتوى في النافذة المنبثقة.',
      category: 'commercial',
      status: 'answered',
      is_answered: true,
      created_at: new Date().toISOString(),
      advocate_profile: {
        full_name: 'المحامي أحمد محمد'
      }
    },
    {
      id: 'test-question-2',
      title: 'استفسار حول قانون الأسرة',
      description: 'سؤال تجريبي آخر لاختبار النظام.',
      category: 'family',
      status: 'pending',
      is_answered: false,
      created_at: new Date().toISOString()
    }
  ];

  const getCategoryLabel = (category: string) => {
    const categories: Record<string, string> = {
      commercial: 'تجاري',
      family: 'أحوال شخصية',
      real_estate: 'عقاري'
    };
    return categories[category] || category;
  };

  const getStatusBadge = (status: string, isAnswered: boolean) => {
    if (isAnswered) {
      return <Badge className="bg-green-100 text-green-800">تم الرد</Badge>;
    }
    
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-orange-600 border-orange-600">في الانتظار</Badge>;
      case 'assigned':
        return <Badge variant="outline" className="text-blue-600 border-blue-600">تم التعيين</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleTestClick = (questionId: string) => {
    console.log('🧪 Testing QuestionDetailView with ID:', questionId);
    setSelectedQuestionId(questionId);
    
    toast({
      title: 'اختبار عرض التفاصيل',
      description: 'جاري فتح نافذة التفاصيل...',
    });
  };

  const handleCloseModal = () => {
    console.log('🔒 Closing QuestionDetailView modal');
    setSelectedQuestionId(null);
    
    toast({
      title: 'تم إغلاق النافذة',
      description: 'تم إغلاق نافذة التفاصيل بنجاح',
    });
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5 text-blue-600" />
            اختبار نافذة تفاصيل السؤال
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded p-4">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-800">معلومات الاختبار</span>
            </div>
            <p className="text-blue-700 text-sm">
              هذا المكون يختبر فتح وإغلاق نافذة تفاصيل السؤال للتأكد من عدم وجود أخطاء JavaScript.
            </p>
          </div>

          <div className="space-y-2">
            <h3 className="font-medium">وضع الاختبار:</h3>
            <div className="flex gap-2">
              <Button
                variant={testMode === 'mock' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setTestMode('mock')}
              >
                بيانات تجريبية
              </Button>
              <Button
                variant={testMode === 'real' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setTestMode('real')}
              >
                بيانات حقيقية
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Questions */}
      <Card>
        <CardHeader>
          <CardTitle>أسئلة الاختبار</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {mockQuestions.map((question) => (
              <Card key={question.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="space-y-3">
                    <div className="flex justify-between items-start gap-3">
                      <CardTitle className="text-lg leading-tight flex-1">{question.title}</CardTitle>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleTestClick(question.id)}
                        className="flex items-center gap-1 shrink-0"
                      >
                        <Eye className="h-3 w-3" />
                        <span className="hidden sm:inline">عرض التفاصيل</span>
                        <span className="sm:hidden">عرض</span>
                      </Button>
                    </div>
                    
                    <p className="text-gray-600 text-sm line-clamp-2">
                      {question.description}
                    </p>
                    
                    <div className="flex flex-wrap gap-2">
                      {getStatusBadge(question.status, question.is_answered)}
                      <Badge variant="outline">
                        {getCategoryLabel(question.category)}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <div className="flex flex-wrap items-center gap-3 text-sm text-gray-500">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span className="text-xs">{formatDate(question.created_at)}</span>
                    </div>
                    {question.advocate_profile && (
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span className="text-xs">{question.advocate_profile.full_name}</span>
                      </div>
                    )}
                    {question.is_answered && (
                      <div className="flex items-center gap-1">
                        <CheckCircle className="h-3 w-3 text-green-600" />
                        <span className="text-xs">تم الرد</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      <Card>
        <CardHeader>
          <CardTitle>نتائج الاختبار</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="w-3 h-3 bg-green-500 rounded-full"></span>
              <span className="text-sm">إذا فتحت النافذة بنجاح، فالاختبار نجح</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-3 h-3 bg-red-500 rounded-full"></span>
              <span className="text-sm">إذا ظهرت رسالة خطأ "X is not defined"، فهناك مشكلة في الاستيراد</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-3 h-3 bg-blue-500 rounded-full"></span>
              <span className="text-sm">تحقق من وحدة التحكم (Console) لرؤية رسائل التشخيص</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Question Detail Modal */}
      {selectedQuestionId && (
        <QuestionDetailView
          questionId={selectedQuestionId}
          userId={userId}
          onClose={handleCloseModal}
        />
      )}
    </div>
  );
};
