import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { SubscriptionService, SubscriptionDetails } from '@/services/subscriptionService';
import { SubscriptionForm } from './SubscriptionForm';
import { SubscriptionPreferencesComponent } from './SubscriptionPreferences';
import { 
  Crown, 
  Calendar, 
  CreditCard, 
  Settings,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Loader2,
  RefreshCw,
  Gift,
  Star
} from 'lucide-react';

interface SubscriptionManagementProps {
  userId: string;
  className?: string;
}

export const SubscriptionManagement: React.FC<SubscriptionManagementProps> = ({
  userId,
  className
}) => {
  const { toast } = useToast();
  const [subscriptionDetails, setSubscriptionDetails] = useState<SubscriptionDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [cancelling, setCancelling] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'plans' | 'preferences'>('overview');

  useEffect(() => {
    fetchSubscriptionDetails();
  }, [userId]);

  const fetchSubscriptionDetails = async () => {
    try {
      setLoading(true);
      const { data, error } = await SubscriptionService.getUserSubscriptionDetails(userId);
      
      if (error) {
        throw error;
      }
      
      setSubscriptionDetails(data);
    } catch (error: any) {
      console.error('Error fetching subscription details:', error);
      toast({
        title: 'خطأ في تحميل بيانات الاشتراك',
        description: error.message || 'فشل في تحميل تفاصيل الاشتراك',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    try {
      setCancelling(true);
      
      const { data, error } = await SubscriptionService.cancelUserSubscription(userId);
      
      if (error) {
        throw error;
      }
      
      toast({
        title: 'تم إلغاء الاشتراك',
        description: 'تم إلغاء اشتراكك بنجاح',
      });
      
      // Refresh subscription details
      await fetchSubscriptionDetails();
      
    } catch (error: any) {
      console.error('Error cancelling subscription:', error);
      toast({
        title: 'خطأ في إلغاء الاشتراك',
        description: error.message || 'فشل في إلغاء الاشتراك',
        variant: 'destructive',
      });
    } finally {
      setCancelling(false);
    }
  };

  const handleSubscriptionChange = async (planId: string) => {
    // Refresh subscription details after change
    await fetchSubscriptionDetails();
    setActiveTab('overview');
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            نشط
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge variant="outline" className="text-red-600 border-red-600">
            <XCircle className="h-3 w-3 mr-1" />
            ملغي
          </Badge>
        );
      case 'expired':
        return (
          <Badge variant="outline" className="text-orange-600 border-orange-600">
            <AlertTriangle className="h-3 w-3 mr-1" />
            منتهي الصلاحية
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        );
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="mr-2">جاري تحميل بيانات الاشتراك...</span>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          إدارة الاشتراك
        </h2>
        <p className="text-gray-600">
          إدارة اشتراكك وإعداداتك
        </p>
      </div>

      {/* Navigation Tabs */}
      <div className="flex justify-center">
        <div className="bg-gray-100 p-1 rounded-lg">
          <button
            onClick={() => setActiveTab('overview')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'overview'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            نظرة عامة
          </button>
          <button
            onClick={() => setActiveTab('plans')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'plans'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            الباقات
          </button>
          <button
            onClick={() => setActiveTab('preferences')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'preferences'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            الإعدادات
          </button>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Current Subscription */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Crown className="h-5 w-5" />
                الاشتراك الحالي
              </CardTitle>
            </CardHeader>
            <CardContent>
              {subscriptionDetails?.subscription && subscriptionDetails?.plan ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {subscriptionDetails.plan.name.toLowerCase().includes('premium') ? (
                        <Star className="h-8 w-8 text-blue-600" />
                      ) : subscriptionDetails.plan.name.toLowerCase().includes('professional') ? (
                        <Crown className="h-8 w-8 text-purple-600" />
                      ) : (
                        <Gift className="h-8 w-8 text-gray-600" />
                      )}
                      <div>
                        <h3 className="text-lg font-semibold">{subscriptionDetails.plan.name_ar}</h3>
                        <p className="text-gray-600">{subscriptionDetails.plan.description_ar}</p>
                      </div>
                    </div>
                    {getStatusBadge(subscriptionDetails.subscription.status)}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
                    <div>
                      <p className="text-sm text-gray-600">السعر الشهري</p>
                      <p className="text-lg font-semibold">
                        {subscriptionDetails.plan.price === 0 
                          ? 'مجاني' 
                          : `${subscriptionDetails.plan.price} درهم`
                        }
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">تاريخ البداية</p>
                      <p className="text-lg font-semibold">
                        {formatDate(subscriptionDetails.subscription.start_date)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">تاريخ الانتهاء</p>
                      <p className="text-lg font-semibold">
                        {subscriptionDetails.subscription.end_date 
                          ? formatDate(subscriptionDetails.subscription.end_date)
                          : 'غير محدد'
                        }
                      </p>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="pt-4 border-t">
                    <h4 className="font-medium mb-3">مميزات الباقة:</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {subscriptionDetails.plan.features_ar.map((feature, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                          <span className="text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Actions */}
                  {subscriptionDetails.subscription.status === 'active' && (
                    <div className="pt-4 border-t">
                      <div className="flex gap-3">
                        <Button
                          variant="outline"
                          onClick={() => setActiveTab('plans')}
                        >
                          تغيير الباقة
                        </Button>
                        <Button
                          variant="outline"
                          onClick={handleCancelSubscription}
                          disabled={cancelling}
                          className="text-red-600 border-red-600 hover:bg-red-50"
                        >
                          {cancelling ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              جاري الإلغاء...
                            </>
                          ) : (
                            'إلغاء الاشتراك'
                          )}
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Gift className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">لا يوجد اشتراك نشط</h3>
                  <p className="text-gray-600 mb-4">اختر باقة للبدء في الاستفادة من خدماتنا</p>
                  <Button onClick={() => setActiveTab('plans')}>
                    اختيار باقة
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Payment Information */}
          {subscriptionDetails?.subscription && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  معلومات الدفع
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">طريقة الدفع</span>
                    <span className="font-medium">
                      {subscriptionDetails.subscription.payment_method === 'free' 
                        ? 'مجاني' 
                        : subscriptionDetails.subscription.payment_method || 'غير محدد'
                      }
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">التجديد التلقائي</span>
                    <span className="font-medium">
                      {subscriptionDetails.subscription.auto_renew ? 'مفعل' : 'معطل'}
                    </span>
                  </div>
                  {subscriptionDetails.subscription.next_payment_date && (
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">الدفعة التالية</span>
                      <span className="font-medium">
                        {formatDate(subscriptionDetails.subscription.next_payment_date)}
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {activeTab === 'plans' && (
        <SubscriptionForm
          userId={userId}
          currentPlanId={subscriptionDetails?.subscription?.plan_id}
          onSubscriptionChange={handleSubscriptionChange}
        />
      )}

      {activeTab === 'preferences' && (
        <SubscriptionPreferencesComponent userId={userId} />
      )}
    </div>
  );
};
