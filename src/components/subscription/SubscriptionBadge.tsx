import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useSubscription } from '@/hooks/useSubscription';
import { 
  Crown, 
  Star, 
  Gift,
  Loader2,
  Sparkles
} from 'lucide-react';

interface SubscriptionBadgeProps {
  showUpgradeButton?: boolean;
  onUpgradeClick?: () => void;
  className?: string;
}

export const SubscriptionBadge: React.FC<SubscriptionBadgeProps> = ({
  showUpgradeButton = false,
  onUpgradeClick,
  className
}) => {
  const { 
    currentPlan, 
    loading, 
    isFreePlan, 
    isPremiumPlan, 
    isProfessionalPlan 
  } = useSubscription();

  if (loading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm">جاري التحميل...</span>
      </div>
    );
  }

  const getPlanIcon = () => {
    if (isProfessionalPlan) {
      return <Crown className="h-4 w-4 text-purple-600" />;
    }
    if (isPremiumPlan) {
      return <Star className="h-4 w-4 text-blue-600" />;
    }
    return <Gift className="h-4 w-4 text-gray-600" />;
  };

  const getPlanBadgeVariant = () => {
    if (isProfessionalPlan) {
      return "default"; // Purple
    }
    if (isPremiumPlan) {
      return "secondary"; // Blue
    }
    return "outline"; // Gray
  };

  const getPlanBadgeClass = () => {
    if (isProfessionalPlan) {
      return "bg-purple-100 text-purple-800 border-purple-200";
    }
    if (isPremiumPlan) {
      return "bg-blue-100 text-blue-800 border-blue-200";
    }
    return "bg-gray-100 text-gray-800 border-gray-200";
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Badge 
        variant={getPlanBadgeVariant()}
        className={`flex items-center gap-1 ${getPlanBadgeClass()}`}
      >
        {getPlanIcon()}
        <span className="text-xs font-medium">
          {currentPlan?.name_ar || 'مجاني'}
        </span>
      </Badge>

      {showUpgradeButton && isFreePlan && (
        <Button
          size="sm"
          onClick={onUpgradeClick}
          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white text-xs px-3 py-1 h-auto"
        >
          <Sparkles className="h-3 w-3 mr-1" />
          ترقية
        </Button>
      )}
    </div>
  );
};

// Compact version for mobile
export const SubscriptionBadgeCompact: React.FC<SubscriptionBadgeProps> = ({
  showUpgradeButton = false,
  onUpgradeClick,
  className
}) => {
  const { 
    currentPlan, 
    loading, 
    isFreePlan, 
    isPremiumPlan, 
    isProfessionalPlan 
  } = useSubscription();

  if (loading) {
    return (
      <div className={`flex items-center ${className}`}>
        <Loader2 className="h-4 w-4 animate-spin" />
      </div>
    );
  }

  const getPlanIcon = () => {
    if (isProfessionalPlan) {
      return <Crown className="h-4 w-4 text-purple-600" />;
    }
    if (isPremiumPlan) {
      return <Star className="h-4 w-4 text-blue-600" />;
    }
    return <Gift className="h-4 w-4 text-gray-600" />;
  };

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      {getPlanIcon()}
      {showUpgradeButton && isFreePlan && (
        <Button
          size="sm"
          onClick={onUpgradeClick}
          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white p-1 h-auto"
        >
          <Sparkles className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
};

// Feature access indicator
export const FeatureAccessBadge: React.FC<{
  feature: string;
  children: React.ReactNode;
  className?: string;
}> = ({ feature, children, className }) => {
  const { canAccessFeature, loading } = useSubscription();

  if (loading) {
    return <div className={className}>{children}</div>;
  }

  const hasAccess = canAccessFeature(feature);

  return (
    <div className={`relative ${className}`}>
      {children}
      {!hasAccess && (
        <div className="absolute inset-0 bg-gray-900/50 rounded-lg flex items-center justify-center">
          <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
            <Crown className="h-3 w-3 mr-1" />
            ترقية مطلوبة
          </Badge>
        </div>
      )}
    </div>
  );
};
