import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { SubscriptionService, SubscriptionPlan } from '@/services/subscriptionService';
import { 
  Check, 
  Crown, 
  Star, 
  Gift,
  CreditCard,
  Loader2,
  AlertCircle,
  Sparkles
} from 'lucide-react';

interface SubscriptionFormProps {
  userId: string;
  currentPlanId?: string;
  onSubscriptionChange?: (planId: string) => void;
  className?: string;
}

export const SubscriptionForm: React.FC<SubscriptionFormProps> = ({
  userId,
  currentPlanId,
  onSubscriptionChange,
  className
}) => {
  const { toast } = useToast();
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [subscribing, setSubscribing] = useState<string | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);

  useEffect(() => {
    fetchSubscriptionPlans();
  }, []);

  const fetchSubscriptionPlans = async () => {
    try {
      setLoading(true);
      const { data, error } = await SubscriptionService.getSubscriptionPlans();
      
      if (error) {
        throw error;
      }
      
      setPlans(data || []);
    } catch (error: any) {
      console.error('Error fetching subscription plans:', error);
      toast({
        title: 'خطأ في تحميل الباقات',
        description: error.message || 'فشل في تحميل باقات الاشتراك',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = async (planId: string) => {
    try {
      setSubscribing(planId);

      // Find the plan to get its details
      const selectedPlan = plans.find(p => p.id === planId);
      if (!selectedPlan) {
        throw new Error('Plan not found');
      }

      console.log('Subscribing to plan:', selectedPlan);

      // Determine payment method based on plan price
      const paymentMethod = selectedPlan.price === 0 ? 'free' : 'credit_card';

      const { data, error } = await SubscriptionService.subscribeUser(
        userId,
        planId,
        paymentMethod
      );

      if (error) {
        console.error('Subscription service error:', error);
        throw new Error(typeof error === 'string' ? error : error.message || 'فشل في تفعيل الاشتراك');
      }

      toast({
        title: 'تم الاشتراك بنجاح',
        description: `تم تفعيل اشتراكك في باقة ${selectedPlan.name_ar}`,
      });

      if (onSubscriptionChange) {
        onSubscriptionChange(planId);
      }

    } catch (error: any) {
      console.error('Error subscribing:', error);

      let errorMessage = 'فشل في تفعيل الاشتراك';

      // Handle specific error types
      if (error.message) {
        if (error.message.includes('duplicate') || error.message.includes('unique')) {
          errorMessage = 'لديك اشتراك نشط بالفعل. يرجى إلغاء الاشتراك الحالي أولاً.';
        } else if (error.message.includes('foreign key') || error.message.includes('not found')) {
          errorMessage = 'خطأ في البيانات. يرجى المحاولة مرة أخرى.';
        } else if (error.message.includes('permission') || error.message.includes('policy')) {
          errorMessage = 'ليس لديك صلاحية لتنفيذ هذا الإجراء.';
        } else {
          errorMessage = error.message;
        }
      }

      toast({
        title: 'خطأ في الاشتراك',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setSubscribing(null);
    }
  };

  const getPlanIcon = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'free':
      case 'مجاني':
        return <Gift className="h-8 w-8 text-gray-600" />;
      case 'premium':
      case 'المستخدم المتميز':
        return <Star className="h-8 w-8 text-blue-600" />;
      case 'professional':
      case 'المحامي المتميز':
        return <Crown className="h-8 w-8 text-purple-600" />;
      default:
        return <Sparkles className="h-8 w-8 text-green-600" />;
    }
  };

  const getPlanColor = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'free':
      case 'مجاني':
        return 'border-gray-200 hover:border-gray-300';
      case 'premium':
      case 'المستخدم المتميز':
        return 'border-blue-200 hover:border-blue-300 bg-blue-50/50';
      case 'professional':
      case 'المحامي المتميز':
        return 'border-purple-200 hover:border-purple-300 bg-purple-50/50';
      default:
        return 'border-green-200 hover:border-green-300';
    }
  };

  const isCurrentPlan = (planId: string) => {
    return currentPlanId === planId;
  };

  const isPopularPlan = (planName: string) => {
    return planName.toLowerCase().includes('premium') || planName.includes('المستخدم المتميز');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="mr-2">جاري تحميل الباقات...</span>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">
          اختر الباقة المناسبة لك
        </h2>
        <p className="text-lg text-gray-600">
          باقات مصممة لتلبية احتياجاتك القانونية
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {plans.map((plan) => (
          <Card 
            key={plan.id} 
            className={`relative transition-all duration-200 ${getPlanColor(plan.name)} ${
              isCurrentPlan(plan.id) ? 'ring-2 ring-green-500' : ''
            }`}
          >
            {isPopularPlan(plan.name) && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-blue-600 text-white px-3 py-1">
                  الأكثر شعبية
                </Badge>
              </div>
            )}
            
            {isCurrentPlan(plan.id) && (
              <div className="absolute -top-3 right-4">
                <Badge className="bg-green-600 text-white px-3 py-1">
                  الباقة الحالية
                </Badge>
              </div>
            )}

            <CardHeader className="text-center pb-4">
              <div className="flex justify-center mb-4">
                {getPlanIcon(plan.name)}
              </div>
              
              <CardTitle className="text-xl mb-2">{plan.name_ar}</CardTitle>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900">
                  {plan.price === 0 ? (
                    'مجاني'
                  ) : (
                    <>
                      {plan.price} <span className="text-lg text-gray-600">درهم/شهر</span>
                    </>
                  )}
                </div>
                <p className="text-sm text-gray-600 mt-1">{plan.description_ar}</p>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <div className="space-y-3">
                {plan.features_ar.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-600 flex-shrink-0" />
                    <span className="text-sm text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>

              <div className="pt-4">
                {isCurrentPlan(plan.id) ? (
                  <Button disabled className="w-full">
                    <Check className="h-4 w-4 mr-2" />
                    الباقة الحالية
                  </Button>
                ) : (
                  <Button
                    onClick={() => handleSubscribe(plan.id)}
                    disabled={subscribing !== null}
                    className={`w-full ${
                      plan.price === 0 
                        ? 'bg-gray-600 hover:bg-gray-700' 
                        : isPopularPlan(plan.name)
                        ? 'bg-blue-600 hover:bg-blue-700'
                        : 'bg-purple-600 hover:bg-purple-700'
                    }`}
                  >
                    {subscribing === plan.id ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        جاري الاشتراك...
                      </>
                    ) : (
                      <>
                        {plan.price === 0 ? (
                          <>
                            <Gift className="h-4 w-4 mr-2" />
                            ابدأ مجاناً
                          </>
                        ) : (
                          <>
                            <CreditCard className="h-4 w-4 mr-2" />
                            اشترك الآن
                          </>
                        )}
                      </>
                    )}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Additional Information */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-start gap-3">
          <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div>
            <h3 className="font-medium text-blue-900 mb-2">معلومات مهمة</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• يمكنك تغيير أو إلغاء اشتراكك في أي وقت</li>
              <li>• جميع الباقات تشمل دعم فني على مدار الساعة</li>
              <li>• الأسعار شاملة ضريبة القيمة المضافة</li>
              <li>• تجربة مجانية لمدة 7 أيام للباقات المدفوعة</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
