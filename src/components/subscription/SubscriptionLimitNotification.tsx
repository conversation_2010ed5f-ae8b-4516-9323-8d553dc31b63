import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { useSubscription } from '@/hooks/useSubscription';
import { 
  AlertTriangle, 
  Crown, 
  Star,
  ArrowRight,
  X
} from 'lucide-react';

interface SubscriptionLimitNotificationProps {
  feature: string;
  message?: string;
  onUpgrade?: () => void;
  onDismiss?: () => void;
  className?: string;
}

export const SubscriptionLimitNotification: React.FC<SubscriptionLimitNotificationProps> = ({
  feature,
  message,
  onUpgrade,
  onDismiss,
  className
}) => {
  const { canAccessFeature, isFreePlan, isPremiumPlan } = useSubscription();

  // Don't show if user has access to the feature
  if (canAccessFeature(feature)) {
    return null;
  }

  const getFeatureMessage = (feature: string) => {
    switch (feature) {
      case 'unlimited_questions':
        return 'لقد وصلت إلى الحد الأقصى للأسئلة المجانية هذا الشهر. ترقى للحصول على أسئلة غير محدودة.';
      case 'unlimited_documents':
        return 'لقد وصلت إلى الحد الأقصى لإنشاء الوثائق المجانية. ترقى للحصول على إنشاء غير محدود.';
      case 'priority_support':
        return 'احصل على دعم ذو أولوية مع الباقة المتميزة.';
      case 'advanced_analytics':
        return 'احصل على تحليلات متقدمة مع الباقة المهنية.';
      case 'lawyer_profile':
        return 'أنشئ ملفك الشخصي كمحامي مع الباقة المهنية.';
      default:
        return message || 'هذه الميزة متاحة فقط للمشتركين في الباقات المدفوعة.';
    }
  };

  const getRecommendedPlan = () => {
    if (feature === 'advanced_analytics' || feature === 'lawyer_profile') {
      return {
        name: 'المحامي المتميز',
        price: '500 درهم/شهر',
        icon: <Crown className="h-4 w-4" />
      };
    }
    return {
      name: 'المستخدم المتميز',
      price: '100 درهم/شهر',
      icon: <Star className="h-4 w-4" />
    };
  };

  const recommendedPlan = getRecommendedPlan();

  return (
    <Alert className={`border-yellow-200 bg-yellow-50 ${className}`}>
      <AlertTriangle className="h-4 w-4 text-yellow-600" />
      <AlertDescription className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-yellow-800 mb-2">
            {getFeatureMessage(feature)}
          </p>
          <div className="flex items-center gap-2 text-sm text-yellow-700">
            {recommendedPlan.icon}
            <span>
              ترقى إلى <strong>{recommendedPlan.name}</strong> - {recommendedPlan.price}
            </span>
          </div>
        </div>
        
        <div className="flex items-center gap-2 mr-4">
          <Button
            size="sm"
            onClick={onUpgrade}
            className="bg-yellow-600 hover:bg-yellow-700 text-white"
          >
            ترقية الآن
            <ArrowRight className="h-3 w-3 mr-1" />
          </Button>
          
          {onDismiss && (
            <Button
              size="sm"
              variant="ghost"
              onClick={onDismiss}
              className="text-yellow-600 hover:text-yellow-700 p-1"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
};

// Usage quota component
export const UsageQuotaNotification: React.FC<{
  used: number;
  limit: number;
  feature: string;
  onUpgrade?: () => void;
  className?: string;
}> = ({ used, limit, feature, onUpgrade, className }) => {
  const { isFreePlan } = useSubscription();
  
  // Don't show for unlimited plans
  if (!isFreePlan) {
    return null;
  }

  const percentage = (used / limit) * 100;
  const isNearLimit = percentage >= 80;
  const isAtLimit = used >= limit;

  if (!isNearLimit) {
    return null;
  }

  return (
    <Alert className={`${isAtLimit ? 'border-red-200 bg-red-50' : 'border-yellow-200 bg-yellow-50'} ${className}`}>
      <AlertTriangle className={`h-4 w-4 ${isAtLimit ? 'text-red-600' : 'text-yellow-600'}`} />
      <AlertDescription>
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className={`${isAtLimit ? 'text-red-800' : 'text-yellow-800'} mb-2`}>
              {isAtLimit 
                ? `لقد استنفدت حصتك من ${feature} لهذا الشهر (${used}/${limit})`
                : `أوشكت على استنفاد حصتك من ${feature} (${used}/${limit})`
              }
            </p>
            
            {/* Progress bar */}
            <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${
                  isAtLimit ? 'bg-red-600' : 'bg-yellow-600'
                }`}
                style={{ width: `${Math.min(percentage, 100)}%` }}
              />
            </div>
            
            <p className={`text-sm ${isAtLimit ? 'text-red-700' : 'text-yellow-700'}`}>
              ترقى للحصول على استخدام غير محدود
            </p>
          </div>
          
          <Button
            size="sm"
            onClick={onUpgrade}
            className={`${
              isAtLimit 
                ? 'bg-red-600 hover:bg-red-700' 
                : 'bg-yellow-600 hover:bg-yellow-700'
            } text-white mr-4`}
          >
            ترقية الآن
            <ArrowRight className="h-3 w-3 mr-1" />
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  );
};

// Trial expiration notification
export const TrialExpirationNotification: React.FC<{
  daysLeft: number;
  onUpgrade?: () => void;
  onDismiss?: () => void;
  className?: string;
}> = ({ daysLeft, onUpgrade, onDismiss, className }) => {
  if (daysLeft > 3) {
    return null;
  }

  const isExpired = daysLeft <= 0;

  return (
    <Alert className={`${isExpired ? 'border-red-200 bg-red-50' : 'border-blue-200 bg-blue-50'} ${className}`}>
      <AlertTriangle className={`h-4 w-4 ${isExpired ? 'text-red-600' : 'text-blue-600'}`} />
      <AlertDescription className="flex items-center justify-between">
        <div className="flex-1">
          <p className={`${isExpired ? 'text-red-800' : 'text-blue-800'} mb-1`}>
            {isExpired 
              ? 'انتهت فترة التجربة المجانية'
              : `تنتهي فترة التجربة المجانية خلال ${daysLeft} ${daysLeft === 1 ? 'يوم' : 'أيام'}`
            }
          </p>
          <p className={`text-sm ${isExpired ? 'text-red-700' : 'text-blue-700'}`}>
            اشترك الآن للاستمرار في الاستفادة من جميع المميزات
          </p>
        </div>
        
        <div className="flex items-center gap-2 mr-4">
          <Button
            size="sm"
            onClick={onUpgrade}
            className={`${
              isExpired 
                ? 'bg-red-600 hover:bg-red-700' 
                : 'bg-blue-600 hover:bg-blue-700'
            } text-white`}
          >
            اشترك الآن
            <ArrowRight className="h-3 w-3 mr-1" />
          </Button>
          
          {onDismiss && !isExpired && (
            <Button
              size="sm"
              variant="ghost"
              onClick={onDismiss}
              className="text-blue-600 hover:text-blue-700 p-1"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
};
