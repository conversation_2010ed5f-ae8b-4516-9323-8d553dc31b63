import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { SubscriptionService } from '@/services/subscriptionService';
import { useAuth } from '@/hooks/useAuth';
import { 
  Bug, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  RefreshCw,
  Database,
  User,
  CreditCard
} from 'lucide-react';

export const SubscriptionDebugPanel: React.FC = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);

  const runDiagnostics = async () => {
    if (!user?.id) {
      toast({
        title: 'خطأ',
        description: 'يجب تسجيل الدخول أولاً',
        variant: 'destructive',
      });
      return;
    }

    try {
      setLoading(true);
      const diagnostics: any = {
        timestamp: new Date().toISOString(),
        userId: user.id,
        tests: {}
      };

      // Test 1: Get subscription plans
      console.log('🔍 Testing: Get subscription plans');
      const plansResult = await SubscriptionService.getSubscriptionPlans();
      diagnostics.tests.getPlans = {
        success: !plansResult.error,
        data: plansResult.data,
        error: plansResult.error,
        count: plansResult.data?.length || 0
      };

      // Test 2: Get user subscription details
      console.log('🔍 Testing: Get user subscription details');
      const detailsResult = await SubscriptionService.getUserSubscriptionDetails(user.id);
      diagnostics.tests.getUserDetails = {
        success: !detailsResult.error,
        data: detailsResult.data,
        error: detailsResult.error
      };

      // Test 3: Get subscription preferences
      console.log('🔍 Testing: Get subscription preferences');
      const preferencesResult = await SubscriptionService.getSubscriptionPreferences(user.id);
      diagnostics.tests.getPreferences = {
        success: !preferencesResult.error,
        data: preferencesResult.data,
        error: preferencesResult.error
      };

      // Test 4: Check active subscription
      console.log('🔍 Testing: Check active subscription');
      const activeResult = await SubscriptionService.hasActiveSubscription(user.id);
      diagnostics.tests.hasActiveSubscription = {
        success: !activeResult.error,
        data: activeResult.data,
        error: activeResult.error
      };

      // Test 5: Get user plan name
      console.log('🔍 Testing: Get user plan name');
      const planNameResult = await SubscriptionService.getUserPlanName(user.id);
      diagnostics.tests.getUserPlanName = {
        success: !planNameResult.error,
        data: planNameResult.data,
        error: planNameResult.error
      };

      setDebugInfo(diagnostics);
      
      toast({
        title: 'تم تشغيل التشخيص',
        description: 'تم فحص جميع وظائف النظام',
      });

    } catch (error: any) {
      console.error('Error running diagnostics:', error);
      toast({
        title: 'خطأ في التشخيص',
        description: error.message || 'فشل في تشغيل التشخيص',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const testSubscription = async () => {
    if (!user?.id || !debugInfo?.tests?.getPlans?.data?.length) {
      toast({
        title: 'خطأ',
        description: 'يجب تشغيل التشخيص أولاً للحصول على الباقات',
        variant: 'destructive',
      });
      return;
    }

    try {
      setTesting(true);
      
      // Get the free plan
      const freePlan = debugInfo.tests.getPlans.data.find((plan: any) => plan.price === 0);
      if (!freePlan) {
        throw new Error('Free plan not found');
      }

      console.log('🧪 Testing subscription to free plan:', freePlan.id);
      
      const subscribeResult = await SubscriptionService.subscribeUser(
        user.id, 
        freePlan.id, 
        'free'
      );

      if (subscribeResult.error) {
        throw new Error(subscribeResult.error.message || 'Subscription failed');
      }

      toast({
        title: 'نجح الاختبار',
        description: 'تم اختبار الاشتراك بنجاح',
      });

      // Refresh diagnostics
      await runDiagnostics();

    } catch (error: any) {
      console.error('Error testing subscription:', error);
      toast({
        title: 'فشل اختبار الاشتراك',
        description: error.message || 'فشل في اختبار الاشتراك',
        variant: 'destructive',
      });
    } finally {
      setTesting(false);
    }
  };

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="h-4 w-4 text-green-600" />
    ) : (
      <XCircle className="h-4 w-4 text-red-600" />
    );
  };

  const getStatusBadge = (success: boolean) => {
    return success ? (
      <Badge className="bg-green-100 text-green-800">نجح</Badge>
    ) : (
      <Badge variant="destructive">فشل</Badge>
    );
  };

  useEffect(() => {
    if (user?.id) {
      runDiagnostics();
    }
  }, [user?.id]);

  if (!user) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <User className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">يجب تسجيل الدخول لاستخدام لوحة التشخيص</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bug className="h-5 w-5" />
            لوحة تشخيص نظام الاشتراكات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-3 mb-6">
            <Button
              onClick={runDiagnostics}
              disabled={loading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              تشغيل التشخيص
            </Button>
            
            <Button
              onClick={testSubscription}
              disabled={testing || !debugInfo}
              variant="outline"
              className="flex items-center gap-2"
            >
              <CreditCard className={`h-4 w-4 ${testing ? 'animate-pulse' : ''}`} />
              اختبار الاشتراك
            </Button>
          </div>

          {debugInfo && (
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-medium mb-2 flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  معلومات المستخدم
                </h3>
                <div className="text-sm space-y-1">
                  <p><strong>معرف المستخدم:</strong> {debugInfo.userId}</p>
                  <p><strong>وقت التشخيص:</strong> {new Date(debugInfo.timestamp).toLocaleString('ar-EG')}</p>
                </div>
              </div>

              <div className="space-y-3">
                <h3 className="font-medium">نتائج الاختبارات:</h3>
                
                {Object.entries(debugInfo.tests).map(([testName, result]: [string, any]) => (
                  <div key={testName} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(result.success)}
                        <span className="font-medium">{testName}</span>
                      </div>
                      {getStatusBadge(result.success)}
                    </div>
                    
                    {result.error && (
                      <div className="bg-red-50 border border-red-200 rounded p-2 mb-2">
                        <p className="text-red-800 text-sm">
                          <strong>خطأ:</strong> {JSON.stringify(result.error)}
                        </p>
                      </div>
                    )}
                    
                    {result.success && result.data && (
                      <div className="bg-green-50 border border-green-200 rounded p-2">
                        <p className="text-green-800 text-sm">
                          <strong>البيانات:</strong> {
                            typeof result.data === 'object' 
                              ? JSON.stringify(result.data, null, 2).substring(0, 200) + '...'
                              : result.data
                          }
                        </p>
                        {result.count !== undefined && (
                          <p className="text-green-800 text-sm">
                            <strong>العدد:</strong> {result.count}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Summary */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-medium mb-2 flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-blue-600" />
                  ملخص التشخيص
                </h3>
                <div className="text-sm space-y-1">
                  <p>
                    <strong>الاختبارات الناجحة:</strong> {
                      Object.values(debugInfo.tests).filter((test: any) => test.success).length
                    } / {Object.keys(debugInfo.tests).length}
                  </p>
                  <p>
                    <strong>حالة النظام:</strong> {
                      Object.values(debugInfo.tests).every((test: any) => test.success)
                        ? '✅ يعمل بشكل صحيح'
                        : '❌ يحتاج إلى إصلاح'
                    }
                  </p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
