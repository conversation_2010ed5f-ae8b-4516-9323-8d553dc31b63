import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle, Clock, Shield } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'admin' | 'user' | 'advocate';
  requireVerification?: boolean;
  fallbackPath?: string;
  allowUnverifiedAccess?: boolean;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  requireVerification = false,
  fallbackPath = '/',
  allowUnverifiedAccess = false
}) => {
  const { user, profile, loading, initialized } = useAuth();

  // Show loading only while initializing
  if (loading && !initialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-sm">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  // If no user and we need authentication, redirect to home
  if (!user && (requiredRole || requireVerification)) {
    return <Navigate to={fallbackPath} replace />;
  }

  // If user exists but no profile yet, show loading or allow access based on requirements
  if (user && !profile) {
    if (requiredRole) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-center flex items-center justify-center gap-2">
                <Clock className="h-5 w-5" />
                جاري تحميل الملف الشخصي
              </CardTitle>
              <CardDescription className="text-center">
                يرجى الانتظار بينما نحمل بياناتك...
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      );
    }
    return <>{children}</>;
  }

  // Check role requirements
  if (requiredRole && profile && profile.role !== requiredRole) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center flex items-center justify-center gap-2">
              <Shield className="h-5 w-5 text-red-500" />
              غير مصرح
            </CardTitle>
            <CardDescription className="text-center">
              ليس لديك صلاحية للوصول إلى هذه الصفحة
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-sm text-gray-600 mb-4">
              الدور المطلوب: {requiredRole === 'admin' ? 'مدير' : requiredRole === 'advocate' ? 'محامي' : 'مستخدم'}
            </p>
            <p className="text-sm text-gray-600 mb-4">
              دورك الحالي: {profile?.role === 'admin' ? 'مدير' : profile?.role === 'advocate' ? 'محامي' : 'مستخدم'}
            </p>
            <Button onClick={() => window.history.back()} variant="outline">
              العودة
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check verification requirements for advocates
  if (requireVerification && profile && !profile.is_verified && !allowUnverifiedAccess) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center flex items-center justify-center gap-2">
              <AlertCircle className="h-5 w-5 text-yellow-500" />
              في انتظار التفعيل
            </CardTitle>
            <CardDescription className="text-center">
              حسابك قيد المراجعة من قبل الإدارة. سيتم تفعيله قريباً.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-sm text-gray-600 mb-4">
              {profile?.role === 'advocate'
                ? 'يتم مراجعة مؤهلاتك كمحامي من قبل فريق الإدارة'
                : 'يتم مراجعة حسابك من قبل فريق الإدارة'
              }
            </p>
            <Button onClick={() => window.location.href = '/'} variant="outline">
              العودة للصفحة الرئيسية
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return <>{children}</>;
};
