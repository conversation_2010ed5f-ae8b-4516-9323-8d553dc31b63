import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Save, 
  Upload, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  DollarSign,
  Clock,
  Star,
  Camera,
  Edit,
  Check,
  X
} from 'lucide-react';
import { AuthService, AdvocateProfile } from '@/services/authService';

const SPECIALIZATIONS = [
  { value: 'family', label: 'قانون الأسرة' },
  { value: 'commercial', label: 'القانون التجاري' },
  { value: 'civil', label: 'القانون المدني' },
  { value: 'criminal', label: 'القانون الجنائي' },
  { value: 'labor', label: 'قانون العمل' },
  { value: 'real_estate', label: 'قانون العقارات' },
  { value: 'administrative', label: 'القانون الإداري' },
  { value: 'tax', label: 'القانون الضريبي' },
  { value: 'intellectual_property', label: 'الملكية الفكرية' },
  { value: 'general', label: 'عام' }
];

const AVAILABILITY_STATUS = [
  { value: 'available', label: 'متاح', color: 'bg-green-500' },
  { value: 'busy', label: 'مشغول', color: 'bg-yellow-500' },
  { value: 'unavailable', label: 'غير متاح', color: 'bg-red-500' }
];

interface AdvocateProfileEditProps {
  profile: AdvocateProfile | null;
  onProfileUpdated: () => void;
}

export const AdvocateProfileEdit: React.FC<AdvocateProfileEditProps> = ({ 
  profile, 
  onProfileUpdated 
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  
  const [formData, setFormData] = useState({
    bio: profile?.bio || '',
    specializations: profile?.specializations || [],
    hourly_rate: profile?.hourly_rate || 500,
    availability_status: 'available',
    phone: '',
    location: '',
    years_experience: 0,
    education: '',
    languages: [] as string[],
    is_featured: profile?.is_featured || false
  });

  const [profileImage, setProfileImage] = useState<File | null>(null);
  const [previewImage, setPreviewImage] = useState<string>('');

  useEffect(() => {
    if (profile) {
      setFormData({
        bio: profile.bio || '',
        specializations: profile.specializations || [],
        hourly_rate: profile.hourly_rate || 500,
        availability_status: profile.availability?.status || 'available',
        phone: profile.availability?.phone || '',
        location: profile.availability?.location || '',
        years_experience: profile.availability?.years_experience || 0,
        education: profile.availability?.education || '',
        languages: profile.availability?.languages || [],
        is_featured: profile.is_featured || false
      });
    }
  }, [profile]);

  const handleSpecializationToggle = (specialization: string) => {
    const current = formData.specializations;
    const updated = current.includes(specialization)
      ? current.filter(s => s !== specialization)
      : [...current, specialization];
    
    setFormData({ ...formData, specializations: updated });
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setProfileImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setIsLoading(true);
    try {
      // Prepare update data
      const updateData = {
        bio: formData.bio,
        specializations: formData.specializations,
        hourly_rate: formData.hourly_rate,
        is_featured: formData.is_featured,
        availability: {
          status: formData.availability_status,
          phone: formData.phone,
          location: formData.location,
          years_experience: formData.years_experience,
          education: formData.education,
          languages: formData.languages
        }
      };

      // Update advocate profile
      const { error } = await AuthService.updateAdvocateProfile(user.id, updateData);
      
      if (error) throw error;

      toast({
        title: 'تم التحديث بنجاح',
        description: 'تم تحديث ملفك الشخصي بنجاح',
      });

      setIsEditing(false);
      onProfileUpdated();
    } catch (error: any) {
      toast({
        title: 'خطأ في التحديث',
        description: error.message || 'حدث خطأ غير متوقع',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Profile Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                الملف الشخصي
              </CardTitle>
              <CardDescription>
                إدارة معلوماتك الشخصية والمهنية
              </CardDescription>
            </div>
            <Button
              variant={isEditing ? "outline" : "default"}
              onClick={() => setIsEditing(!isEditing)}
            >
              {isEditing ? (
                <>
                  <X className="h-4 w-4 mr-2" />
                  إلغاء
                </>
              ) : (
                <>
                  <Edit className="h-4 w-4 mr-2" />
                  تعديل
                </>
              )}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-6">
            <div className="relative">
              <Avatar className="h-24 w-24">
                <AvatarImage src={previewImage || ''} />
                <AvatarFallback className="text-lg">
                  {user?.email?.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              {isEditing && (
                <label className="absolute bottom-0 right-0 bg-primary text-primary-foreground rounded-full p-2 cursor-pointer hover:bg-primary/90">
                  <Camera className="h-4 w-4" />
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                    className="hidden"
                  />
                </label>
              )}
            </div>
            <div className="flex-1">
              <h3 className="text-xl font-semibold">{user?.email}</h3>
              <p className="text-muted-foreground">محامي معتمد</p>
              <div className="flex items-center gap-2 mt-2">
                <div className={`w-3 h-3 rounded-full ${
                  AVAILABILITY_STATUS.find(s => s.value === formData.availability_status)?.color || 'bg-gray-500'
                }`} />
                <span className="text-sm">
                  {AVAILABILITY_STATUS.find(s => s.value === formData.availability_status)?.label || 'غير محدد'}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Profile Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>المعلومات الأساسية</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="bio">نبذة تعريفية</Label>
              <Textarea
                id="bio"
                value={formData.bio}
                onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
                placeholder="اكتب نبذة مختصرة عن خبراتك ومؤهلاتك..."
                rows={4}
                disabled={!isEditing}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="hourly_rate">السعر بالساعة (درهم)</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="hourly_rate"
                    type="number"
                    value={formData.hourly_rate}
                    onChange={(e) => setFormData({ 
                      ...formData, 
                      hourly_rate: parseFloat(e.target.value) || 500 
                    })}
                    min="0"
                    step="50"
                    className="pl-10"
                    disabled={!isEditing}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="availability">حالة التوفر</Label>
                <Select
                  value={formData.availability_status}
                  onValueChange={(value) => setFormData({ ...formData, availability_status: value })}
                  disabled={!isEditing}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {AVAILABILITY_STATUS.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${status.color}`} />
                          {status.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone">رقم الهاتف</Label>
                <div className="relative">
                  <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                    placeholder="+212 6XX XXX XXX"
                    className="pl-10"
                    disabled={!isEditing}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="location">الموقع</Label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                    placeholder="المدينة، المغرب"
                    className="pl-10"
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Specializations */}
        <Card>
          <CardHeader>
            <CardTitle>التخصصات</CardTitle>
            <CardDescription>
              اختر التخصصات القانونية التي تمارسها
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {SPECIALIZATIONS.map((spec) => (
                <Badge
                  key={spec.value}
                  variant={formData.specializations.includes(spec.value) ? "default" : "outline"}
                  className={`cursor-pointer transition-colors ${
                    isEditing ? 'hover:bg-primary/80' : 'cursor-default'
                  }`}
                  onClick={() => isEditing && handleSpecializationToggle(spec.value)}
                >
                  {spec.label}
                  {formData.specializations.includes(spec.value) && (
                    <Check className="h-3 w-3 ml-1" />
                  )}
                </Badge>
              ))}
            </div>
            {isEditing && (
              <p className="text-sm text-muted-foreground mt-2">
                انقر على التخصصات لإضافتها أو إزالتها
              </p>
            )}
          </CardContent>
        </Card>

        {/* Professional Information */}
        <Card>
          <CardHeader>
            <CardTitle>المعلومات المهنية</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="years_experience">سنوات الخبرة</Label>
                <Input
                  id="years_experience"
                  type="number"
                  value={formData.years_experience}
                  onChange={(e) => setFormData({ 
                    ...formData, 
                    years_experience: parseInt(e.target.value) || 0 
                  })}
                  min="0"
                  disabled={!isEditing}
                />
              </div>

              <div className="space-y-2">
                <Label>حساب مميز</Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={formData.is_featured}
                    onCheckedChange={(checked) => 
                      setFormData({ ...formData, is_featured: checked })
                    }
                    disabled={!isEditing}
                  />
                  <Label className="text-sm">عرض كمحامي مميز</Label>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="education">المؤهلات التعليمية</Label>
              <Textarea
                id="education"
                value={formData.education}
                onChange={(e) => setFormData({ ...formData, education: e.target.value })}
                placeholder="اذكر مؤهلاتك التعليمية والشهادات..."
                rows={3}
                disabled={!isEditing}
              />
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        {isEditing && (
          <div className="flex justify-end gap-3">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setIsEditing(false)}
            >
              إلغاء
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Clock className="h-4 w-4 animate-spin mr-2" />
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  حفظ التغييرات
                </>
              )}
            </Button>
          </div>
        )}
      </form>
    </div>
  );
};
