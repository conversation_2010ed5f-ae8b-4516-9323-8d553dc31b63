import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart3, TrendingUp, TrendingDown } from 'lucide-react';

interface ChartData {
  label: string;
  value: number;
  color?: string;
}

interface SimpleChartProps {
  title: string;
  description?: string;
  data: ChartData[];
  type?: 'bar' | 'line' | 'pie';
  showTrend?: boolean;
  trendValue?: number;
}

export const SimpleChart: React.FC<SimpleChartProps> = ({
  title,
  description,
  data,
  type = 'bar',
  showTrend = false,
  trendValue = 0
}) => {
  const maxValue = Math.max(...data.map(d => d.value));

  const renderBarChart = () => (
    <div className="space-y-3">
      {data.map((item, index) => (
        <div key={index} className="flex items-center gap-3">
          <div className="w-20 text-sm text-right">{item.label}</div>
          <div className="flex-1 bg-gray-200 rounded-full h-3 relative">
            <div
              className={`h-3 rounded-full transition-all duration-500 ${
                item.color || 'bg-blue-500'
              }`}
              style={{
                width: `${(item.value / maxValue) * 100}%`
              }}
            />
          </div>
          <div className="w-12 text-sm font-medium">{item.value}</div>
        </div>
      ))}
    </div>
  );

  const renderLineChart = () => (
    <div className="h-32 flex items-end justify-between gap-2">
      {data.map((item, index) => (
        <div key={index} className="flex flex-col items-center gap-2 flex-1">
          <div
            className={`w-full rounded-t transition-all duration-500 ${
              item.color || 'bg-green-500'
            }`}
            style={{
              height: `${(item.value / maxValue) * 100}%`,
              minHeight: '4px'
            }}
          />
          <div className="text-xs text-center">{item.label}</div>
          <div className="text-xs font-medium">{item.value}</div>
        </div>
      ))}
    </div>
  );

  const renderPieChart = () => {
    const total = data.reduce((sum, item) => sum + item.value, 0);
    
    return (
      <div className="space-y-3">
        <div className="flex justify-center">
          <div className="w-32 h-32 rounded-full border-8 border-gray-200 flex items-center justify-center">
            <div className="text-center">
              <div className="text-2xl font-bold">{total}</div>
              <div className="text-xs text-muted-foreground">المجموع</div>
            </div>
          </div>
        </div>
        <div className="space-y-2">
          {data.map((item, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div
                  className={`w-3 h-3 rounded-full ${item.color || 'bg-purple-500'}`}
                />
                <span className="text-sm">{item.label}</span>
              </div>
              <div className="text-sm font-medium">
                {item.value} ({total > 0 ? Math.round((item.value / total) * 100) : 0}%)
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderChart = () => {
    switch (type) {
      case 'line':
        return renderLineChart();
      case 'pie':
        return renderPieChart();
      default:
        return renderBarChart();
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              {title}
            </CardTitle>
            {description && (
              <CardDescription>{description}</CardDescription>
            )}
          </div>
          {showTrend && (
            <div className={`flex items-center gap-1 text-sm ${
              trendValue >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {trendValue >= 0 ? (
                <TrendingUp className="h-4 w-4" />
              ) : (
                <TrendingDown className="h-4 w-4" />
              )}
              {Math.abs(trendValue)}%
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {data.length > 0 ? (
          renderChart()
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            لا توجد بيانات للعرض
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Example usage data
export const sampleChartData = {
  weeklyQuestions: [
    { label: 'السبت', value: 5, color: 'bg-blue-500' },
    { label: 'الأحد', value: 8, color: 'bg-blue-500' },
    { label: 'الاثنين', value: 12, color: 'bg-blue-500' },
    { label: 'الثلاثاء', value: 7, color: 'bg-blue-500' },
    { label: 'الأربعاء', value: 15, color: 'bg-blue-500' },
    { label: 'الخميس', value: 10, color: 'bg-blue-500' },
    { label: 'الجمعة', value: 6, color: 'bg-blue-500' }
  ],
  categoryDistribution: [
    { label: 'قانون الأسرة', value: 25, color: 'bg-red-500' },
    { label: 'القانون التجاري', value: 18, color: 'bg-blue-500' },
    { label: 'القانون المدني', value: 15, color: 'bg-green-500' },
    { label: 'القانون الجنائي', value: 12, color: 'bg-yellow-500' },
    { label: 'قانون العمل', value: 8, color: 'bg-purple-500' }
  ],
  monthlyEarnings: [
    { label: 'يناير', value: 15000, color: 'bg-green-500' },
    { label: 'فبراير', value: 18000, color: 'bg-green-500' },
    { label: 'مارس', value: 22000, color: 'bg-green-500' },
    { label: 'أبريل', value: 19000, color: 'bg-green-500' },
    { label: 'مايو', value: 25000, color: 'bg-green-500' },
    { label: 'يونيو', value: 28000, color: 'bg-green-500' }
  ]
};
