
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { LanguageSwitcher } from '@/components/LanguageSwitcher';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import {
  Shield,
  Users,
  Crown,
  MessageCircle,
  DollarSign,
  LogOut,
  User,
  Settings
} from 'lucide-react';

interface AdminNavbarProps {
  userEmail?: string;
  stats: {
    totalUsers: number;
    totalAdvocates: number;
    verifiedAdvocates: number;
    totalQuestions: number;
    monthlyRevenue: number;
  };
  statsLoading: boolean;
}

export const AdminNavbar: React.FC<AdminNavbarProps> = ({
  userEmail,
  stats,
  statsLoading
}) => {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const { signOut } = useAuth();
  const { toast } = useToast();

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/');
      toast({
        title: t('auth.signout_success') || 'تم تسجيل الخروج بنجاح',
        description: t('auth.signout_description') || 'شكراً لاستخدام خدماتنا',
      });
    } catch (error) {
      console.error('Error signing out:', error);
      toast({
        title: t('common.error') || 'خطأ',
        description: t('auth.signout_error') || 'حدث خطأ أثناء تسجيل الخروج',
        variant: 'destructive',
      });
    }
  };
  return (
    <>
      {/* Desktop Navbar */}
      <div className="bg-white border-b shadow-sm">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            {/* Logo and Title */}
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Shield className="h-8 w-8 text-blue-600" />
                <span className="font-bold text-xl text-gray-900">
                  إدارة النظام
                </span>
              </div>
            </div>

            {/* Dynamic Stats in Navbar - Desktop */}
            <div className="hidden md:flex items-center space-x-6 space-x-reverse">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Users className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-gray-700">
                  {statsLoading ? '...' : stats.totalUsers}
                </span>
                <span className="text-xs text-gray-500">مستخدم</span>
              </div>
              
              <div className="flex items-center space-x-2 space-x-reverse">
                <Crown className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium text-gray-700">
                  {statsLoading ? '...' : `${stats.verifiedAdvocates}/${stats.totalAdvocates}`}
                </span>
                <span className="text-xs text-gray-500">محامي</span>
              </div>
              
              <div className="flex items-center space-x-2 space-x-reverse">
                <MessageCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-gray-700">
                  {statsLoading ? '...' : stats.totalQuestions}
                </span>
                <span className="text-xs text-gray-500">سؤال</span>
              </div>
              
              <div className="flex items-center space-x-2 space-x-reverse">
                <DollarSign className="h-4 w-4 text-emerald-600" />
                <span className="text-sm font-medium text-gray-700">
                  {statsLoading ? '...' : stats.monthlyRevenue}
                </span>
                <span className="text-xs text-gray-500">MAD</span>
              </div>
            </div>

            {/* Admin Controls */}
            <div className="flex items-center gap-3">
              {/* Language Switcher */}
              <LanguageSwitcher />

              {/* Admin Dropdown Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center gap-2 h-9">
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Shield className="h-3 w-3" />
                      مدير
                    </Badge>
                    {userEmail && (
                      <Badge variant="secondary" className="hidden sm:flex max-w-[150px] truncate">
                        {userEmail}
                      </Badge>
                    )}
                    <User className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <div className="flex items-center justify-start gap-2 p-2">
                    <div className="flex flex-col space-y-1 leading-none">
                      <p className="font-medium text-sm">مدير النظام</p>
                      {userEmail && (
                        <p className="w-[200px] truncate text-xs text-muted-foreground">
                          {userEmail}
                        </p>
                      )}
                    </div>
                  </div>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => navigate('/admin')} className="cursor-pointer">
                    <Settings className="mr-2 h-4 w-4" />
                    إعدادات النظام
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleSignOut} className="cursor-pointer text-red-600">
                    <LogOut className="mr-2 h-4 w-4" />
                    تسجيل الخروج
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Stats Bar */}
      <div className="md:hidden bg-gray-50 border-b">
        <div className="w-full max-w-7xl mx-auto px-4 py-3">
          <div className="grid grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-blue-600">
                {statsLoading ? '...' : stats.totalUsers}
              </div>
              <div className="text-xs text-gray-600">مستخدم</div>
            </div>
            <div>
              <div className="text-lg font-bold text-purple-600">
                {statsLoading ? '...' : `${stats.verifiedAdvocates}/${stats.totalAdvocates}`}
              </div>
              <div className="text-xs text-gray-600">محامي</div>
            </div>
            <div>
              <div className="text-lg font-bold text-green-600">
                {statsLoading ? '...' : stats.totalQuestions}
              </div>
              <div className="text-xs text-gray-600">سؤال</div>
            </div>
            <div>
              <div className="text-lg font-bold text-emerald-600">
                {statsLoading ? '...' : stats.monthlyRevenue}
              </div>
              <div className="text-xs text-gray-600">MAD</div>
            </div>
          </div>

          {/* Mobile Controls */}
          <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-200">
            <LanguageSwitcher />
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                <Shield className="h-3 w-3 mr-1" />
                مدير
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSignOut}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <LogOut className="h-4 w-4 mr-1" />
                خروج
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
