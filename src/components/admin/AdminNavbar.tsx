
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Users, 
  Crown, 
  MessageCircle, 
  DollarSign 
} from 'lucide-react';

interface AdminNavbarProps {
  userEmail?: string;
  stats: {
    totalUsers: number;
    totalAdvocates: number;
    verifiedAdvocates: number;
    totalQuestions: number;
    monthlyRevenue: number;
  };
  statsLoading: boolean;
}

export const AdminNavbar: React.FC<AdminNavbarProps> = ({ 
  userEmail, 
  stats, 
  statsLoading 
}) => {
  return (
    <>
      {/* Desktop Navbar */}
      <div className="bg-white border-b shadow-sm">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            {/* Logo and Title */}
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Shield className="h-8 w-8 text-blue-600" />
                <span className="font-bold text-xl text-gray-900">
                  إدارة النظام
                </span>
              </div>
            </div>

            {/* Dynamic Stats in Navbar - Desktop */}
            <div className="hidden md:flex items-center space-x-6 space-x-reverse">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Users className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-gray-700">
                  {statsLoading ? '...' : stats.totalUsers}
                </span>
                <span className="text-xs text-gray-500">مستخدم</span>
              </div>
              
              <div className="flex items-center space-x-2 space-x-reverse">
                <Crown className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium text-gray-700">
                  {statsLoading ? '...' : `${stats.verifiedAdvocates}/${stats.totalAdvocates}`}
                </span>
                <span className="text-xs text-gray-500">محامي</span>
              </div>
              
              <div className="flex items-center space-x-2 space-x-reverse">
                <MessageCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-gray-700">
                  {statsLoading ? '...' : stats.totalQuestions}
                </span>
                <span className="text-xs text-gray-500">سؤال</span>
              </div>
              
              <div className="flex items-center space-x-2 space-x-reverse">
                <DollarSign className="h-4 w-4 text-emerald-600" />
                <span className="text-sm font-medium text-gray-700">
                  {statsLoading ? '...' : stats.monthlyRevenue}
                </span>
                <span className="text-xs text-gray-500">MAD</span>
              </div>
            </div>

            {/* Admin Badge */}
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="flex items-center gap-1">
                <Shield className="h-3 w-3" />
                مدير
              </Badge>
              {userEmail && (
                <Badge variant="secondary" className="hidden sm:flex">
                  {userEmail}
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Stats Bar */}
      <div className="md:hidden bg-gray-50 border-b">
        <div className="w-full max-w-7xl mx-auto px-4 py-3">
          <div className="grid grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-blue-600">
                {statsLoading ? '...' : stats.totalUsers}
              </div>
              <div className="text-xs text-gray-600">مستخدم</div>
            </div>
            <div>
              <div className="text-lg font-bold text-purple-600">
                {statsLoading ? '...' : `${stats.verifiedAdvocates}/${stats.totalAdvocates}`}
              </div>
              <div className="text-xs text-gray-600">محامي</div>
            </div>
            <div>
              <div className="text-lg font-bold text-green-600">
                {statsLoading ? '...' : stats.totalQuestions}
              </div>
              <div className="text-xs text-gray-600">سؤال</div>
            </div>
            <div>
              <div className="text-lg font-bold text-emerald-600">
                {statsLoading ? '...' : stats.monthlyRevenue}
              </div>
              <div className="text-xs text-gray-600">MAD</div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
