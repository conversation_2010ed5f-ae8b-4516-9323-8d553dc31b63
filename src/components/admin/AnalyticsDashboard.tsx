import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { AdminService, UserAnalytics, QuestionsAnalytics, AdvocateAnalytics } from '@/services/adminService';
import {
  BarChart3,
  Users,
  MessageCircle,
  TrendingUp,
  Clock,
  Star,
  DollarSign,
  Activity,
  RefreshCw,
  Loader2
} from 'lucide-react';

export const AnalyticsDashboard: React.FC = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [userAnalytics, setUserAnalytics] = useState<UserAnalytics | null>(null);
  const [questionsAnalytics, setQuestionsAnalytics] = useState<QuestionsAnalytics | null>(null);
  const [advocateAnalytics, setAdvocateAnalytics] = useState<AdvocateAnalytics | null>(null);

  useEffect(() => {
    fetchAllAnalytics();
  }, []);

  const fetchAllAnalytics = async () => {
    setLoading(true);
    try {
      const [userResult, questionsResult, advocateResult] = await Promise.all([
        AdminService.getUserAnalytics(),
        AdminService.getQuestionsAnalytics(),
        AdminService.getAdvocateAnalytics()
      ]);

      if (userResult.error) throw userResult.error;
      if (questionsResult.error) throw questionsResult.error;
      if (advocateResult.error) throw advocateResult.error;

      setUserAnalytics(userResult.data);
      setQuestionsAnalytics(questionsResult.data);
      setAdvocateAnalytics(advocateResult.data);
    } catch (error: any) {
      toast({
        title: 'خطأ في تحميل الإحصائيات',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchAllAnalytics();
    setRefreshing(false);
    toast({
      title: 'تم تحديث الإحصائيات',
      description: 'تم تحديث جميع البيانات بنجاح',
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="mr-2">جاري تحميل الإحصائيات...</span>
      </div>
    );
  }

  const calculateResponseRate = () => {
    if (!questionsAnalytics) return 0;
    const { total_questions, answered_questions } = questionsAnalytics;
    return total_questions > 0 ? Math.round((answered_questions / total_questions) * 100) : 0;
  };

  const estimateMonthlyRevenue = () => {
    if (!userAnalytics) return 0;
    const proUsers = (userAnalytics.subscription_tiers?.pro_user || 0) * 99; // 99 MAD per month
    const proAdvocates = (userAnalytics.subscription_tiers?.pro_advocate || 0) * 199; // 199 MAD per month
    return proUsers + proAdvocates;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">لوحة الإحصائيات</h2>
          <p className="text-gray-600">نظرة شاملة على أداء النظام</p>
        </div>
        <Button onClick={handleRefresh} disabled={refreshing} variant="outline">
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          تحديث
        </Button>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المستخدمين</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userAnalytics?.total_users || 0}</div>
            <p className="text-xs text-muted-foreground">
              +{userAnalytics?.new_users_last_30_days || 0} هذا الشهر
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الأسئلة</CardTitle>
            <MessageCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{questionsAnalytics?.total_questions || 0}</div>
            <p className="text-xs text-muted-foreground">
              +{questionsAnalytics?.questions_last_30_days || 0} هذا الشهر
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">معدل الاستجابة</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{calculateResponseRate()}%</div>
            <p className="text-xs text-muted-foreground">
              {questionsAnalytics?.answered_questions || 0} من {questionsAnalytics?.total_questions || 0}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الإيرادات الشهرية</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{estimateMonthlyRevenue()} MAD</div>
            <p className="text-xs text-muted-foreground">
              من الاشتراكات المدفوعة
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              إحصائيات المستخدمين
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">المستخدمين حسب النوع</h4>
              <div className="space-y-2">
                {userAnalytics?.users_by_role && Object.entries(userAnalytics.users_by_role).map(([role, count]) => (
                  <div key={role} className="flex justify-between items-center">
                    <span className="capitalize">
                      {role === 'admin' ? 'مدير' : role === 'advocate' ? 'محامي' : 'مستخدم'}
                    </span>
                    <Badge variant="outline">{count}</Badge>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">حالة التفعيل</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span>مفعل</span>
                  <Badge variant="default">{userAnalytics?.verification_status.verified || 0}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>غير مفعل</span>
                  <Badge variant="secondary">{userAnalytics?.verification_status.unverified || 0}</Badge>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">الاشتراكات</h4>
              <div className="space-y-2">
                {userAnalytics?.subscription_tiers && Object.entries(userAnalytics.subscription_tiers).map(([tier, count]) => (
                  <div key={tier} className="flex justify-between items-center">
                    <span>
                      {tier === 'free' ? 'مجاني' : tier === 'pro_user' ? 'مستخدم متميز' : 'محامي متميز'}
                    </span>
                    <Badge variant="outline">{count}</Badge>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Questions Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5" />
              إحصائيات الأسئلة
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">الأسئلة حسب الفئة</h4>
              <div className="space-y-2">
                {questionsAnalytics?.questions_by_category && Object.entries(questionsAnalytics.questions_by_category).map(([category, count]) => (
                  <div key={category} className="flex justify-between items-center">
                    <span className="capitalize">{category}</span>
                    <Badge variant="outline">{count}</Badge>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">الأسئلة حسب الحالة</h4>
              <div className="space-y-2">
                {questionsAnalytics?.questions_by_status && Object.entries(questionsAnalytics.questions_by_status).map(([status, count]) => (
                  <div key={status} className="flex justify-between items-center">
                    <span className="capitalize">{status}</span>
                    <Badge variant="outline">{count}</Badge>
                  </div>
                ))}
              </div>
            </div>

            <div className="pt-2 border-t">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="h-4 w-4" />
                <span className="font-medium">متوسط وقت الاستجابة</span>
              </div>
              <div className="text-2xl font-bold">
                {questionsAnalytics?.average_response_time_hours || 0} ساعة
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Advocate Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            أداء المحامين
          </CardTitle>
          <CardDescription>
            إحصائيات شاملة عن أداء المحامين في النظام
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {advocateAnalytics?.total_advocates || 0}
              </div>
              <div className="text-sm text-gray-600">إجمالي المحامين</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {advocateAnalytics?.verified_advocates || 0}
              </div>
              <div className="text-sm text-gray-600">محامين مفعلين</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">
                {advocateAnalytics?.average_rating || 0}
              </div>
              <div className="text-sm text-gray-600">متوسط التقييم</div>
            </div>
          </div>

          {/* Top Advocates */}
          {questionsAnalytics?.top_advocates_by_responses && (
            <div>
              <h4 className="font-medium mb-3">أفضل المحامين (آخر 30 يوم)</h4>
              <div className="space-y-2">
                {questionsAnalytics.top_advocates_by_responses.slice(0, 5).map((advocate, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium">{advocate.advocate_name}</div>
                      <div className="text-sm text-gray-600">{advocate.advocate_email}</div>
                    </div>
                    <Badge variant="default">{advocate.response_count} رد</Badge>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* System Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            نشاط النظام
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3">النشاط الأخير</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span>مستخدمين نشطين (7 أيام)</span>
                  <Badge variant="default">{userAnalytics?.active_users_last_7_days || 0}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>ردود جديدة (7 أيام)</span>
                  <Badge variant="default">{advocateAnalytics?.response_rates.responses_last_7_days || 0}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>ردود جديدة (30 يوم)</span>
                  <Badge variant="outline">{advocateAnalytics?.response_rates.responses_last_30_days || 0}</Badge>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-3">التخصصات الأكثر طلباً</h4>
              <div className="space-y-2">
                {advocateAnalytics?.advocates_by_specialization && 
                  Object.entries(advocateAnalytics.advocates_by_specialization)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 5)
                    .map(([spec, count]) => (
                      <div key={spec} className="flex justify-between items-center">
                        <span className="capitalize">{spec}</span>
                        <Badge variant="outline">{count}</Badge>
                      </div>
                    ))
                }
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
