import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { AdminService, UserProfile } from '@/services/adminService';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Shield,
  ShieldOff,
  Download,
  Users,
  Loader2
} from 'lucide-react';

interface UserManagementTableProps {
  onEditUser: (user: UserProfile) => void;
  refreshTrigger: number;
}

export const UserManagementTable: React.FC<UserManagementTableProps> = ({
  onEditUser,
  refreshTrigger
}) => {
  const { toast } = useToast();
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<UserProfile | null>(null);
  const [bulkActionLoading, setBulkActionLoading] = useState(false);

  // Filters
  const [filters, setFilters] = useState({
    search: '',
    role: '',
    is_verified: '',
    subscription_tier: ''
  });

  useEffect(() => {
    fetchUsers();
  }, [refreshTrigger, filters]);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const { data, error } = await AdminService.getAllUsers({
        role: filters.role || undefined,
        is_verified: filters.is_verified ? filters.is_verified === 'true' : undefined,
        subscription_tier: filters.subscription_tier || undefined,
        search: filters.search || undefined
      });

      if (error) throw error;
      setUsers(data || []);
    } catch (error: any) {
      toast({
        title: 'خطأ في تحميل المستخدمين',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUser = async (user: UserProfile) => {
    try {
      const { data, error } = await AdminService.deleteUser(user.id);

      if (error) throw error;

      if (data?.success) {
        toast({
          title: 'تم حذف المستخدم',
          description: `تم حذف ${user.email} بنجاح`,
        });
        fetchUsers();
      } else {
        throw new Error(data?.message || 'فشل في حذف المستخدم');
      }
    } catch (error: any) {
      toast({
        title: 'خطأ في حذف المستخدم',
        description: error.message,
        variant: 'destructive',
      });
    }
    setDeleteDialogOpen(false);
    setUserToDelete(null);
  };

  const handleBulkAction = async (action: string, options?: any) => {
    if (selectedUsers.length === 0) {
      toast({
        title: 'لم يتم تحديد مستخدمين',
        description: 'يرجى تحديد مستخدم واحد على الأقل',
        variant: 'destructive',
      });
      return;
    }

    setBulkActionLoading(true);
    try {
      const { data, error } = await AdminService.bulkUpdateUsers(
        selectedUsers,
        action as any,
        options
      );

      if (error) throw error;

      if (data?.success) {
        toast({
          title: 'تمت العملية بنجاح',
          description: data.message,
        });
        setSelectedUsers([]);
        fetchUsers();
      } else {
        throw new Error(data?.message || 'فشلت العملية');
      }
    } catch (error: any) {
      toast({
        title: 'خطأ في العملية',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setBulkActionLoading(false);
    }
  };

  const handleExportData = async () => {
    try {
      const csvData = await AdminService.exportUsersData('csv');
      AdminService.downloadFile(
        csvData,
        `users_export_${new Date().toISOString().split('T')[0]}.csv`,
        'text/csv'
      );
      toast({
        title: 'تم تصدير البيانات',
        description: 'تم تحميل ملف البيانات بنجاح',
      });
    } catch (error: any) {
      toast({
        title: 'خطأ في تصدير البيانات',
        description: error.message,
        variant: 'destructive',
      });
    }
  };

  const getRoleBadge = (role: string) => {
    const variants: Record<string, any> = {
      admin: 'destructive',
      advocate: 'default',
      user: 'secondary'
    };
    const labels: Record<string, string> = {
      admin: 'مدير',
      advocate: 'محامي',
      user: 'مستخدم'
    };
    return <Badge variant={variants[role]}>{labels[role]}</Badge>;
  };

  const getSubscriptionBadge = (tier: string) => {
    const variants: Record<string, any> = {
      free: 'outline',
      pro_user: 'default',
      pro_advocate: 'secondary'
    };
    const labels: Record<string, string> = {
      free: 'مجاني',
      pro_user: 'مستخدم متميز',
      pro_advocate: 'محامي متميز'
    };
    return <Badge variant={variants[tier]}>{labels[tier]}</Badge>;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="mr-2">جاري تحميل المستخدمين...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters and Actions */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-wrap gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="البحث بالاسم أو البريد الإلكتروني..."
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              className="pl-10 w-64"
            />
          </div>
          
          <Select
            value={filters.role || "all"}
            onValueChange={(value) => setFilters({ ...filters, role: value === "all" ? "" : value })}
          >
            <SelectTrigger className="w-32">
              <SelectValue placeholder="الدور" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">الكل</SelectItem>
              <SelectItem value="admin">مدير</SelectItem>
              <SelectItem value="advocate">محامي</SelectItem>
              <SelectItem value="user">مستخدم</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.is_verified || "all"}
            onValueChange={(value) => setFilters({ ...filters, is_verified: value === "all" ? "" : value })}
          >
            <SelectTrigger className="w-32">
              <SelectValue placeholder="التفعيل" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">الكل</SelectItem>
              <SelectItem value="true">مفعل</SelectItem>
              <SelectItem value="false">غير مفعل</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.subscription_tier || "all"}
            onValueChange={(value) => setFilters({ ...filters, subscription_tier: value === "all" ? "" : value })}
          >
            <SelectTrigger className="w-40">
              <SelectValue placeholder="الاشتراك" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">الكل</SelectItem>
              <SelectItem value="free">مجاني</SelectItem>
              <SelectItem value="pro_user">مستخدم متميز</SelectItem>
              <SelectItem value="pro_advocate">محامي متميز</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" onClick={handleExportData}>
            <Download className="h-4 w-4 mr-2" />
            تصدير
          </Button>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedUsers.length > 0 && (
        <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
          <Users className="h-4 w-4" />
          <span className="text-sm">تم تحديد {selectedUsers.length} مستخدم</span>
          <div className="flex gap-2 mr-auto">
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleBulkAction('verify')}
              disabled={bulkActionLoading}
            >
              <Shield className="h-4 w-4 mr-1" />
              تفعيل
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleBulkAction('unverify')}
              disabled={bulkActionLoading}
            >
              <ShieldOff className="h-4 w-4 mr-1" />
              إلغاء تفعيل
            </Button>
            <Button
              size="sm"
              variant="destructive"
              onClick={() => handleBulkAction('delete')}
              disabled={bulkActionLoading}
            >
              <Trash2 className="h-4 w-4 mr-1" />
              حذف
            </Button>
          </div>
        </div>
      )}

      {/* Users Table */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedUsers.length === users.length && users.length > 0}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedUsers(users.map(u => u.id));
                    } else {
                      setSelectedUsers([]);
                    }
                  }}
                />
              </TableHead>
              <TableHead>المستخدم</TableHead>
              <TableHead>الدور</TableHead>
              <TableHead>الاشتراك</TableHead>
              <TableHead>الحالة</TableHead>
              <TableHead>تاريخ الإنشاء</TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <Checkbox
                    checked={selectedUsers.includes(user.id)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedUsers([...selectedUsers, user.id]);
                      } else {
                        setSelectedUsers(selectedUsers.filter(id => id !== user.id));
                      }
                    }}
                  />
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">{user.full_name || user.email}</div>
                    <div className="text-sm text-gray-500">{user.email}</div>
                    {user.phone && (
                      <div className="text-sm text-gray-500">{user.phone}</div>
                    )}
                  </div>
                </TableCell>
                <TableCell>{getRoleBadge(user.role)}</TableCell>
                <TableCell>{getSubscriptionBadge(user.subscription_tier)}</TableCell>
                <TableCell>
                  <Badge variant={user.is_verified ? "default" : "secondary"}>
                    {user.is_verified ? "مفعل" : "غير مفعل"}
                  </Badge>
                </TableCell>
                <TableCell>
                  {new Date(user.created_at || '').toLocaleDateString('ar-EG')}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>الإجراءات</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => onEditUser(user)}>
                        <Edit className="h-4 w-4 mr-2" />
                        تعديل
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => {
                          setUserToDelete(user);
                          setDeleteDialogOpen(true);
                        }}
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        حذف
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {users.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          لا توجد مستخدمين مطابقين للفلاتر المحددة
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>تأكيد الحذف</AlertDialogTitle>
            <AlertDialogDescription>
              هل أنت متأكد من حذف المستخدم {userToDelete?.email}؟ 
              هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع البيانات المرتبطة بهذا المستخدم.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => userToDelete && handleDeleteUser(userToDelete)}
              className="bg-red-600 hover:bg-red-700"
            >
              حذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
