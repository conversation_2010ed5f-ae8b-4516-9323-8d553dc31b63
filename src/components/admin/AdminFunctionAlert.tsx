import React, { useState, useEffect } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { AlertTriangle, CheckCircle, XCircle, Copy, ExternalLink } from 'lucide-react';
import { checkAdminFunctions } from '@/utils/adminDiagnostic';

export const AdminFunctionAlert: React.FC = () => {
  const { toast } = useToast();
  const [functionStatus, setFunctionStatus] = useState<'checking' | 'working' | 'missing'>('checking');
  const [showSolution, setShowSolution] = useState(false);

  useEffect(() => {
    checkFunctions();
  }, []);

  const checkFunctions = async () => {
    const result = await checkAdminFunctions();
    setFunctionStatus(result.success ? 'working' : 'missing');
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: 'تم النسخ',
        description: 'تم نسخ النص إلى الحافظة',
      });
    } catch (error) {
      toast({
        title: 'خطأ في النسخ',
        description: 'فشل في نسخ النص',
        variant: 'destructive',
      });
    }
  };

  const sqlScript = `-- FIX ADMIN FUNCTIONS FOR LAW APP MOROCCO
-- Execute this script in Supabase SQL Editor

-- 1. Drop existing functions if they exist
DROP FUNCTION IF EXISTS public.admin_update_user(UUID, TEXT, TEXT, TEXT, TEXT, BOOLEAN, TEXT[], TEXT, DECIMAL);

-- 2. Create the admin_update_user function
CREATE OR REPLACE FUNCTION public.admin_update_user(
  user_id UUID,
  user_name TEXT DEFAULT NULL,
  user_phone TEXT DEFAULT NULL,
  user_role TEXT DEFAULT NULL,
  subscription_tier TEXT DEFAULT NULL,
  is_verified BOOLEAN DEFAULT NULL,
  advocate_specializations TEXT[] DEFAULT NULL,
  advocate_bio TEXT DEFAULT NULL,
  advocate_hourly_rate DECIMAL DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  -- Check if user exists
  IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = user_id) THEN
    RETURN json_build_object('success', false, 'message', 'User not found');
  END IF;

  -- Update profile table
  UPDATE public.profiles 
  SET 
    full_name = COALESCE(user_name, full_name),
    phone = COALESCE(user_phone, phone),
    role = COALESCE(user_role, role),
    subscription_tier = COALESCE(subscription_tier, subscription_tier),
    is_verified = COALESCE(is_verified, is_verified),
    updated_at = NOW()
  WHERE id = user_id;

  RETURN json_build_object('success', true, 'message', 'User updated successfully');
  
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object('success', false, 'error', SQLERRM);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Grant permissions
GRANT EXECUTE ON FUNCTION public.admin_update_user TO authenticated;

SELECT 'Admin functions fixed!' as status;`;

  if (functionStatus === 'checking') {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>جاري فحص وظائف الإدارة...</AlertTitle>
        <AlertDescription>
          يتم التحقق من وجود الوظائف المطلوبة في قاعدة البيانات
        </AlertDescription>
      </Alert>
    );
  }

  if (functionStatus === 'working') {
    return (
      <Alert className="border-green-200 bg-green-50">
        <CheckCircle className="h-4 w-4 text-green-600" />
        <AlertTitle className="text-green-800">وظائف الإدارة تعمل بشكل صحيح</AlertTitle>
        <AlertDescription className="text-green-700">
          جميع الوظائف المطلوبة متوفرة في قاعدة البيانات
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className="border-red-200 bg-red-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-red-800">
          <XCircle className="h-5 w-5" />
          وظائف الإدارة مفقودة
        </CardTitle>
        <CardDescription className="text-red-700">
          الوظائف المطلوبة لإدارة المستخدمين غير موجودة في قاعدة البيانات
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="flex items-center gap-2">
          <Badge variant="destructive">خطأ</Badge>
          <span className="text-sm">Function admin_update_user does not exist</span>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium text-red-800">الحل:</h4>
          <ol className="list-decimal list-inside space-y-1 text-sm text-red-700">
            <li>افتح لوحة تحكم Supabase</li>
            <li>اذهب إلى SQL Editor</li>
            <li>انسخ والصق الكود أدناه</li>
            <li>اضغط على Run لتنفيذ الكود</li>
          </ol>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowSolution(!showSolution)}
          >
            {showSolution ? 'إخفاء الحل' : 'عرض الحل'}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open('https://supabase.com/dashboard', '_blank')}
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            فتح Supabase
          </Button>
        </div>

        {showSolution && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h5 className="font-medium">كود SQL للإصلاح:</h5>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(sqlScript)}
              >
                <Copy className="h-4 w-4 mr-2" />
                نسخ الكود
              </Button>
            </div>
            
            <pre className="bg-gray-900 text-green-400 p-4 rounded-lg text-xs overflow-x-auto max-h-64">
              {sqlScript}
            </pre>
          </div>
        )}

        <Alert className="border-blue-200 bg-blue-50">
          <AlertTriangle className="h-4 w-4 text-blue-600" />
          <AlertTitle className="text-blue-800">ملاحظة مهمة</AlertTitle>
          <AlertDescription className="text-blue-700">
            بعد تنفيذ الكود، قم بتحديث الصفحة لاختبار الوظائف مرة أخرى
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
};
