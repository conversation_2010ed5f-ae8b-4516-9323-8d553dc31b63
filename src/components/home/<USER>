import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AuthService } from '@/services/authService';
import { Users, Crown, MessageCircle, FileText, CheckCircle, TrendingUp } from 'lucide-react';

interface AppStats {
  totalUsers: number;
  totalAdvocates: number;
  verifiedAdvocates: number;
  totalQuestions: number;
  answeredQuestions: number;
  totalDocuments: number;
}

export const StatsSection: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [stats, setStats] = useState<AppStats>({
    totalUsers: 0,
    totalAdvocates: 0,
    verifiedAdvocates: 0,
    totalQuestions: 0,
    answeredQuestions: 0,
    totalDocuments: 0,
  });
  const [loading, setLoading] = useState(true);
  const currentLanguage = i18n.language as 'ar' | 'fr';

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const appStats = await AuthService.getAppStatistics();
      setStats(appStats);
    } catch (error) {
      console.error('Error loading stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const statsData = [
    {
      title: currentLanguage === 'ar' ? 'المستخدمون المسجلون' : 'Utilisateurs inscrits',
      value: stats.totalUsers,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: currentLanguage === 'ar' ? 'المحامون المعتمدون' : 'Avocats vérifiés',
      value: stats.verifiedAdvocates,
      icon: Crown,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: currentLanguage === 'ar' ? 'الأسئلة المجابة' : 'Questions répondues',
      value: stats.answeredQuestions,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: currentLanguage === 'ar' ? 'الوثائق المنشأة' : 'Documents générés',
      value: stats.totalDocuments,
      icon: FileText,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            {currentLanguage === 'ar' ? 'إحصائيات المنصة' : 'Statistiques de la plateforme'}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {currentLanguage === 'ar' 
              ? 'أرقام تعكس ثقة المستخدمين في خدماتنا القانونية'
              : 'Des chiffres qui reflètent la confiance des utilisateurs en nos services juridiques'
            }
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statsData.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardHeader className="pb-2">
                  <div className={`w-12 h-12 ${stat.bgColor} rounded-lg flex items-center justify-center mx-auto mb-4`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                  <CardTitle className="text-sm font-medium text-gray-600">
                    {stat.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-gray-900">
                    {loading ? (
                      <div className="animate-pulse bg-gray-200 h-8 w-16 mx-auto rounded"></div>
                    ) : (
                      stat.value.toLocaleString()
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Additional stats row */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="text-center">
            <CardHeader>
              <div className="w-12 h-12 bg-indigo-50 rounded-lg flex items-center justify-center mx-auto mb-4">
                <MessageCircle className="h-6 w-6 text-indigo-600" />
              </div>
              <CardTitle className="text-sm font-medium text-gray-600">
                {currentLanguage === 'ar' ? 'إجمالي الأسئلة' : 'Total des questions'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900">
                {loading ? (
                  <div className="animate-pulse bg-gray-200 h-8 w-16 mx-auto rounded"></div>
                ) : (
                  stats.totalQuestions.toLocaleString()
                )}
              </div>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="w-12 h-12 bg-emerald-50 rounded-lg flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="h-6 w-6 text-emerald-600" />
              </div>
              <CardTitle className="text-sm font-medium text-gray-600">
                {currentLanguage === 'ar' ? 'معدل الإجابة' : 'Taux de réponse'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900">
                {loading ? (
                  <div className="animate-pulse bg-gray-200 h-8 w-16 mx-auto rounded"></div>
                ) : (
                  `${stats.totalQuestions > 0 ? Math.round((stats.answeredQuestions / stats.totalQuestions) * 100) : 0}%`
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};
