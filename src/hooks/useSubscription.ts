import { useState, useEffect, useCallback } from 'react';
import { SubscriptionService, SubscriptionDetails, SubscriptionPlan } from '@/services/subscriptionService';
import { useAuth } from '@/hooks/useAuth';

export interface UseSubscriptionReturn {
  // Subscription data
  subscriptionDetails: SubscriptionDetails | null;
  plans: SubscriptionPlan[];
  currentPlan: SubscriptionPlan | null;
  
  // Loading states
  loading: boolean;
  plansLoading: boolean;
  subscribing: boolean;
  
  // Actions
  subscribe: (planId: string, paymentMethod?: string) => Promise<boolean>;
  cancelSubscription: () => Promise<boolean>;
  refreshSubscription: () => Promise<void>;
  
  // Utilities
  hasActiveSubscription: boolean;
  isFreePlan: boolean;
  isPremiumPlan: boolean;
  isProfessionalPlan: boolean;
  canAccessFeature: (feature: string) => boolean;
}

export const useSubscription = (): UseSubscriptionReturn => {
  const { user } = useAuth();
  const [subscriptionDetails, setSubscriptionDetails] = useState<SubscriptionDetails | null>(null);
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [plansLoading, setPlansLoading] = useState(true);
  const [subscribing, setSubscribing] = useState(false);

  // Fetch subscription details
  const fetchSubscriptionDetails = useCallback(async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const { data, error } = await SubscriptionService.getUserSubscriptionDetails(user.id);
      
      if (error) {
        console.error('Error fetching subscription details:', error);
        return;
      }
      
      setSubscriptionDetails(data);
    } catch (error) {
      console.error('Error in fetchSubscriptionDetails:', error);
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // Fetch available plans
  const fetchPlans = useCallback(async () => {
    try {
      setPlansLoading(true);
      const { data, error } = await SubscriptionService.getSubscriptionPlans();
      
      if (error) {
        console.error('Error fetching plans:', error);
        return;
      }
      
      setPlans(data || []);
    } catch (error) {
      console.error('Error in fetchPlans:', error);
    } finally {
      setPlansLoading(false);
    }
  }, []);

  // Subscribe to a plan
  const subscribe = useCallback(async (planId: string, paymentMethod?: string): Promise<boolean> => {
    if (!user?.id) return false;

    try {
      setSubscribing(true);
      const { data, error } = await SubscriptionService.subscribeUser(user.id, planId, paymentMethod);
      
      if (error) {
        console.error('Error subscribing:', error);
        return false;
      }
      
      // Refresh subscription details
      await fetchSubscriptionDetails();
      return true;
    } catch (error) {
      console.error('Error in subscribe:', error);
      return false;
    } finally {
      setSubscribing(false);
    }
  }, [user?.id, fetchSubscriptionDetails]);

  // Cancel subscription
  const cancelSubscription = useCallback(async (): Promise<boolean> => {
    if (!user?.id) return false;

    try {
      const { data, error } = await SubscriptionService.cancelUserSubscription(user.id);
      
      if (error) {
        console.error('Error cancelling subscription:', error);
        return false;
      }
      
      // Refresh subscription details
      await fetchSubscriptionDetails();
      return true;
    } catch (error) {
      console.error('Error in cancelSubscription:', error);
      return false;
    }
  }, [user?.id, fetchSubscriptionDetails]);

  // Refresh subscription data
  const refreshSubscription = useCallback(async () => {
    await Promise.all([
      fetchSubscriptionDetails(),
      fetchPlans()
    ]);
  }, [fetchSubscriptionDetails, fetchPlans]);

  // Initialize data
  useEffect(() => {
    if (user?.id) {
      fetchSubscriptionDetails();
      fetchPlans();
    }
  }, [user?.id, fetchSubscriptionDetails, fetchPlans]);

  // Computed values
  const currentPlan = subscriptionDetails?.plan || null;
  const hasActiveSubscription = subscriptionDetails?.subscription?.status === 'active';
  
  const isFreePlan = currentPlan?.price === 0 || !hasActiveSubscription;
  const isPremiumPlan = currentPlan?.name.toLowerCase().includes('premium') || 
                       currentPlan?.name.includes('المستخدم المتميز');
  const isProfessionalPlan = currentPlan?.name.toLowerCase().includes('professional') || 
                            currentPlan?.name.includes('المحامي المتميز');

  // Feature access control
  const canAccessFeature = useCallback((feature: string): boolean => {
    if (!hasActiveSubscription) {
      // Free tier features
      return ['basic_questions', 'basic_documents', 'trial'].includes(feature);
    }

    if (isProfessionalPlan) {
      // Professional plan has access to all features
      return true;
    }

    if (isPremiumPlan) {
      // Premium plan features
      return ![
        'advanced_analytics', 
        'priority_profile', 
        'specialized_tools'
      ].includes(feature);
    }

    // Free plan (fallback)
    return ['basic_questions', 'basic_documents', 'trial'].includes(feature);
  }, [hasActiveSubscription, isPremiumPlan, isProfessionalPlan]);

  return {
    // Data
    subscriptionDetails,
    plans,
    currentPlan,
    
    // Loading states
    loading,
    plansLoading,
    subscribing,
    
    // Actions
    subscribe,
    cancelSubscription,
    refreshSubscription,
    
    // Utilities
    hasActiveSubscription,
    isFreePlan,
    isPremiumPlan,
    isProfessionalPlan,
    canAccessFeature
  };
};

// Hook for checking specific subscription features
export const useSubscriptionFeature = (feature: string) => {
  const { canAccessFeature, loading } = useSubscription();
  
  return {
    hasAccess: canAccessFeature(feature),
    loading
  };
};

// Hook for getting user's plan name
export const useUserPlan = () => {
  const { currentPlan, loading } = useSubscription();
  
  return {
    planName: currentPlan?.name_ar || 'مجاني',
    planNameEn: currentPlan?.name || 'Free',
    planPrice: currentPlan?.price || 0,
    loading
  };
};
