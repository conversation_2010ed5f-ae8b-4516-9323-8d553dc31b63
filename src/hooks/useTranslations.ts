import { useTranslation } from 'react-i18next';
import { getTranslation, getLanguageTranslations, legalTranslations } from '@/lib/translations-config';

export const useTranslations = () => {
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language as 'ar' | 'fr';

  // Get translation with fallback
  const translate = (key: string, isLegal: boolean = false) => {
    return getTranslation(currentLanguage, key, isLegal);
  };

  // Get legal term translation
  const getLegalTerm = (term: string) => {
    const legalTerms = legalTranslations[currentLanguage]?.legal_terms;
    return legalTerms?.[term] || term;
  };

  // Get Moroccan law term translation
  const getMoroccanLawTerm = (term: string) => {
    const moroccanLaw = legalTranslations[currentLanguage]?.moroccan_law;
    return moroccanLaw?.[term] || term;
  };

  // Get document type translation
  const getDocumentType = (type: string) => {
    const documentTypes = legalTranslations[currentLanguage]?.document_types;
    return documentTypes?.[type] || type;
  };

  // Get all translations for current language
  const getAllTranslations = () => {
    return getLanguageTranslations(currentLanguage);
  };

  // Check if current language is RTL
  const isRTL = () => {
    return currentLanguage === 'ar';
  };

  // Get direction for CSS
  const getDirection = () => {
    return isRTL() ? 'rtl' : 'ltr';
  };

  // Format currency based on language
  const formatCurrency = (amount: number) => {
    if (currentLanguage === 'ar') {
      return `${amount} درهم`;
    }
    return `${amount} MAD`;
  };

  // Format date based on language
  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    if (currentLanguage === 'ar') {
      return dateObj.toLocaleDateString('ar-MA');
    }
    return dateObj.toLocaleDateString('fr-FR');
  };

  // Get language-specific placeholder text
  const getPlaceholder = (type: 'search' | 'email' | 'password' | 'name' | 'phone' | 'message') => {
    const placeholders = {
      ar: {
        search: 'ابحث...',
        email: 'البريد الإلكتروني',
        password: 'كلمة المرور',
        name: 'الاسم الكامل',
        phone: 'رقم الهاتف',
        message: 'اكتب رسالتك هنا...'
      },
      fr: {
        search: 'Rechercher...',
        email: 'Email',
        password: 'Mot de passe',
        name: 'Nom complet',
        phone: 'Numéro de téléphone',
        message: 'Tapez votre message ici...'
      }
    };

    return placeholders[currentLanguage][type];
  };

  // Get language-specific error messages
  const getErrorMessage = (type: 'required' | 'invalid_email' | 'min_length' | 'max_length' | 'network') => {
    const errors = {
      ar: {
        required: 'هذا الحقل مطلوب',
        invalid_email: 'البريد الإلكتروني غير صحيح',
        min_length: 'يجب أن يكون النص أطول',
        max_length: 'النص طويل جداً',
        network: 'خطأ في الاتصال بالشبكة'
      },
      fr: {
        required: 'Ce champ est requis',
        invalid_email: 'Email invalide',
        min_length: 'Le texte doit être plus long',
        max_length: 'Le texte est trop long',
        network: 'Erreur de connexion réseau'
      }
    };

    return errors[currentLanguage][type];
  };

  // Get language-specific success messages
  const getSuccessMessage = (type: 'saved' | 'deleted' | 'updated' | 'created' | 'sent') => {
    const messages = {
      ar: {
        saved: 'تم الحفظ بنجاح',
        deleted: 'تم الحذف بنجاح',
        updated: 'تم التحديث بنجاح',
        created: 'تم الإنشاء بنجاح',
        sent: 'تم الإرسال بنجاح'
      },
      fr: {
        saved: 'Sauvegardé avec succès',
        deleted: 'Supprimé avec succès',
        updated: 'Mis à jour avec succès',
        created: 'Créé avec succès',
        sent: 'Envoyé avec succès'
      }
    };

    return messages[currentLanguage][type];
  };

  // Get language-specific confirmation messages
  const getConfirmationMessage = (type: 'delete' | 'save' | 'cancel' | 'logout') => {
    const confirmations = {
      ar: {
        delete: 'هل أنت متأكد من الحذف؟',
        save: 'هل تريد حفظ التغييرات؟',
        cancel: 'هل تريد إلغاء العملية؟',
        logout: 'هل تريد تسجيل الخروج؟'
      },
      fr: {
        delete: 'Êtes-vous sûr de vouloir supprimer ?',
        save: 'Voulez-vous sauvegarder les modifications ?',
        cancel: 'Voulez-vous annuler l\'opération ?',
        logout: 'Voulez-vous vous déconnecter ?'
      }
    };

    return confirmations[currentLanguage][type];
  };

  return {
    t,
    i18n,
    currentLanguage,
    translate,
    getLegalTerm,
    getMoroccanLawTerm,
    getDocumentType,
    getAllTranslations,
    isRTL,
    getDirection,
    formatCurrency,
    formatDate,
    getPlaceholder,
    getErrorMessage,
    getSuccessMessage,
    getConfirmationMessage
  };
};

export default useTranslations;
