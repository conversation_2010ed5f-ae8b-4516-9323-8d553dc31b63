import translationsData from '../../translations.json';
import legalTranslationsData from '../../legal-translations.json';

// Type definitions for translations
export interface TranslationKeys {
  nav: {
    home: string;
    pricing: string;
    advocates: string;
    documents: string;
    questions: string;
    dashboard: string;
    chatbot: string;
    admin: string;
    advocate_dashboard: string;
    login: string;
    signup: string;
    logout: string;
  };
  hero: {
    title: string;
    subtitle: string;
    description: string;
    cta: {
      primary: string;
      secondary: string;
    };
  };
  pricing: {
    title: string;
    subtitle: string;
    free: {
      title: string;
      price: string;
      features: {
        questions: string;
        documents: string;
        trial: string;
      };
      cta: string;
    };
    pro: {
      title: string;
      price: string;
      features: {
        questions: string;
        documents: string;
        support: string;
        advocates: string;
      };
      cta: string;
    };
    advocate: {
      title: string;
      price: string;
      features: {
        featured: string;
        booking: string;
        availability: string;
        priority: string;
      };
      cta: string;
    };
  };
  auth: {
    signin: {
      title: string;
      subtitle: string;
      button: string;
    };
    signup: {
      title: string;
      subtitle: string;
      button: string;
    };
    email: string;
    password: string;
    fullname: string;
    google: string;
    switch: {
      signin: string;
      signup: string;
    };
  };
  admin: {
    title: string;
    dashboard: string;
    users: {
      title: string;
      total: string;
      advocates: string;
      verified: string;
      unverified: string;
      create: string;
      edit: string;
      delete: string;
    };
    stats: {
      revenue: string;
      questions: string;
      documents: string;
      subscriptions: string;
    };
    actions: {
      verify: string;
      unverify: string;
      promote: string;
      demote: string;
    };
  };
  documents: {
    title: string;
    create: string;
    templates: string;
    categories: {
      all: string;
      contracts: string;
      legal: string;
      business: string;
      personal: string;
    };
    search: string;
    download: string;
    preview: string;
  };
  questions: {
    title: string;
    ask: string;
    categories: {
      family: string;
      business: string;
      criminal: string;
      civil: string;
      labor: string;
    };
    status: {
      pending: string;
      answered: string;
      closed: string;
    };
  };
  chatbot: {
    title: string;
    placeholder: string;
    send: string;
    thinking: string;
    error: string;
  };
  subscription: {
    title: string;
    current_plan: string;
    upgrade: string;
    downgrade: string;
    cancel: string;
    renew: string;
    billing: string;
    payment_method: string;
    next_billing: string;
    usage: string;
    limits: string;
  };
  profile: {
    title: string;
    edit: string;
    personal_info: string;
    contact_info: string;
    preferences: string;
    security: string;
    notifications: string;
    language: string;
    timezone: string;
  };
  advocate: {
    title: string;
    profile: string;
    specializations: string;
    experience: string;
    education: string;
    certifications: string;
    hourly_rate: string;
    availability: string;
    reviews: string;
    cases: string;
    clients: string;
  };
  legal: {
    terms: string;
    privacy: string;
    disclaimer: string;
    copyright: string;
    license: string;
    compliance: string;
    regulations: string;
  };
  notifications: {
    title: string;
    new_message: string;
    new_question: string;
    answer_received: string;
    document_ready: string;
    payment_due: string;
    subscription_expiring: string;
    mark_read: string;
    mark_unread: string;
    clear_all: string;
  };
  common: {
    loading: string;
    error: string;
    success: string;
    cancel: string;
    save: string;
    edit: string;
    delete: string;
    confirm: string;
    back: string;
    next: string;
    previous: string;
    search: string;
    filter: string;
    sort: string;
    view: string;
    download: string;
    upload: string;
    submit: string;
    close: string;
    yes: string;
    no: string;
    ok: string;
    apply: string;
    reset: string;
    refresh: string;
    export: string;
    import: string;
    print: string;
    share: string;
  };
}

// Export the translations data
export const translations = translationsData;
export const legalTranslations = legalTranslationsData;

// Helper function to get translation by key
export const getTranslation = (
  language: 'ar' | 'fr',
  key: string,
  isLegal: boolean = false
): string => {
  const data = isLegal ? legalTranslations : translations;
  const keys = key.split('.');
  let value: any = data[language]?.translation || data[language];
  
  for (const k of keys) {
    value = value?.[k];
  }
  
  return value || key;
};

// Helper function to get all translations for a language
export const getLanguageTranslations = (language: 'ar' | 'fr') => {
  return {
    ...translations[language]?.translation,
    legal: legalTranslations[language]
  };
};

// Available languages
export const availableLanguages = [
  { code: 'ar', name: 'العربية', nativeName: 'العربية', dir: 'rtl' },
  { code: 'fr', name: 'Français', nativeName: 'Français', dir: 'ltr' }
];

// Default language
export const defaultLanguage = 'ar';

// Language detection settings
export const languageDetectionOptions = {
  order: ['localStorage', 'navigator', 'htmlTag'],
  caches: ['localStorage'],
  excludeCacheFor: ['cimode'],
  checkWhitelist: true
};

export default {
  translations,
  legalTranslations,
  getTranslation,
  getLanguageTranslations,
  availableLanguages,
  defaultLanguage,
  languageDetectionOptions
};
