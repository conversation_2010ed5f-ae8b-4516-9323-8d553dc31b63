-- SIMPLE ADMIN UPDATE FUNCTION - ALTERNATIVE APPROACH
-- Execute this script if the previous one still has type issues

-- 1. Drop existing function
DROP FUNCTION IF EXISTS public.admin_update_user(UUID, TEXT, TEXT, TEXT, TEXT, BOOLEAN, TEXT[], TEXT, DECIMAL);

-- 2. Create a simpler version that handles types more carefully
CREATE OR REPLACE FUNCTION public.admin_update_user(
  user_id UUID,
  user_name TEXT DEFAULT NULL,
  user_phone TEXT DEFAULT NULL,
  user_role TEXT DEFAULT NULL,
  subscription_tier TEXT DEFAULT NULL,
  is_verified BOOLEAN DEFAULT NULL,
  advocate_specializations TEXT[] DEFAULT NULL,
  advocate_bio TEXT DEFAULT NULL,
  advocate_hourly_rate DECIMAL DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  result JSON;
  update_count INTEGER := 0;
BEGIN
  -- Check if user exists
  IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = user_id) THEN
    RETURN json_build_object('success', false, 'message', 'User not found');
  END IF;

  -- Update profile fields one by one to avoid type conflicts
  IF user_name IS NOT NULL THEN
    UPDATE public.profiles SET full_name = user_name, updated_at = NOW() WHERE id = user_id;
    GET DIAGNOSTICS update_count = ROW_COUNT;
  END IF;

  IF user_phone IS NOT NULL THEN
    UPDATE public.profiles SET phone = user_phone, updated_at = NOW() WHERE id = user_id;
    GET DIAGNOSTICS update_count = ROW_COUNT;
  END IF;

  IF user_role IS NOT NULL THEN
    -- Handle role update with explicit casting
    BEGIN
      UPDATE public.profiles SET role = user_role::user_role, updated_at = NOW() WHERE id = user_id;
      GET DIAGNOSTICS update_count = ROW_COUNT;
    EXCEPTION WHEN OTHERS THEN
      -- If enum casting fails, try direct assignment
      UPDATE public.profiles SET role = user_role, updated_at = NOW() WHERE id = user_id;
      GET DIAGNOSTICS update_count = ROW_COUNT;
    END;
  END IF;

  IF subscription_tier IS NOT NULL THEN
    -- Handle subscription_tier update with explicit casting
    BEGIN
      UPDATE public.profiles SET subscription_tier = subscription_tier::subscription_tier, updated_at = NOW() WHERE id = user_id;
      GET DIAGNOSTICS update_count = ROW_COUNT;
    EXCEPTION WHEN OTHERS THEN
      -- If enum casting fails, try direct assignment
      UPDATE public.profiles SET subscription_tier = subscription_tier, updated_at = NOW() WHERE id = user_id;
      GET DIAGNOSTICS update_count = ROW_COUNT;
    END;
  END IF;

  IF is_verified IS NOT NULL THEN
    UPDATE public.profiles SET is_verified = is_verified, updated_at = NOW() WHERE id = user_id;
    GET DIAGNOSTICS update_count = ROW_COUNT;
  END IF;

  -- Handle advocate-specific updates
  IF user_role = 'advocate' OR advocate_specializations IS NOT NULL OR advocate_bio IS NOT NULL OR advocate_hourly_rate IS NOT NULL THEN
    -- Check if advocate record exists
    IF EXISTS (SELECT 1 FROM public.advocates WHERE profile_id = user_id) THEN
      -- Update existing advocate record
      IF advocate_specializations IS NOT NULL THEN
        UPDATE public.advocates SET specializations = advocate_specializations, updated_at = NOW() WHERE profile_id = user_id;
      END IF;
      
      IF advocate_bio IS NOT NULL THEN
        UPDATE public.advocates SET bio = advocate_bio, updated_at = NOW() WHERE profile_id = user_id;
      END IF;
      
      IF advocate_hourly_rate IS NOT NULL THEN
        UPDATE public.advocates SET hourly_rate = advocate_hourly_rate, updated_at = NOW() WHERE profile_id = user_id;
      END IF;
      
      IF is_verified IS NOT NULL THEN
        UPDATE public.advocates SET is_verified = is_verified, updated_at = NOW() WHERE profile_id = user_id;
      END IF;
    ELSIF user_role = 'advocate' THEN
      -- Create new advocate record
      INSERT INTO public.advocates (
        profile_id,
        specializations,
        bio,
        hourly_rate,
        is_verified,
        created_at,
        updated_at
      ) VALUES (
        user_id,
        COALESCE(advocate_specializations, ARRAY[]::TEXT[]),
        COALESCE(advocate_bio, ''),
        COALESCE(advocate_hourly_rate, 500.00),
        COALESCE(is_verified, false),
        NOW(),
        NOW()
      );
    END IF;
  END IF;

  result := json_build_object(
    'success', true,
    'user_id', user_id,
    'message', 'User updated successfully'
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'success', false,
    'error', SQLERRM,
    'message', 'Failed to update user: ' || SQLERRM
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Grant permissions
GRANT EXECUTE ON FUNCTION public.admin_update_user TO authenticated;

-- 4. Test message
SELECT 'Simple admin update function created successfully!' as status;
