// Test script to verify subscription service
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ebwyerwbifowpyukefqv.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVid3llcndiaWZvd3B5dWtlZnF2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NjE5NzQsImV4cCI6MjA1MDUzNzk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testSubscriptionService() {
  console.log('Testing subscription service...');
  
  try {
    // Test 1: Get subscription plans
    console.log('\n1. Testing getSubscriptionPlans...');
    const { data: plans, error: plansError } = await supabase
      .from('subscription_plans')
      .select('*');
    
    if (plansError) {
      console.error('Error getting plans:', plansError);
    } else {
      console.log('Plans found:', plans?.length || 0);
      plans?.forEach(plan => {
        console.log(`- ${plan.name} (${plan.name_ar}): ${plan.price} ${plan.currency}`);
      });
    }
    
    // Test 2: Check if user exists
    console.log('\n2. Testing user lookup...');
    const testUserId = '54f0b9b9-1693-4d6a-8799-7a412ece0008';
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', testUserId)
      .single();
    
    if (profileError) {
      console.error('Error getting profile:', profileError);
    } else {
      console.log('Profile found:', profile?.email);
      console.log('Current subscription tier:', profile?.subscription_tier);
    }
    
    // Test 3: Get user subscriptions
    console.log('\n3. Testing getUserSubscriptions...');
    const { data: subscriptions, error: subsError } = await supabase
      .from('user_subscriptions')
      .select(`
        *,
        subscription_plans (
          name,
          name_ar,
          price,
          currency
        )
      `)
      .eq('user_id', testUserId);
    
    if (subsError) {
      console.error('Error getting subscriptions:', subsError);
    } else {
      console.log('Subscriptions found:', subscriptions?.length || 0);
      subscriptions?.forEach(sub => {
        console.log(`- ${sub.subscription_plans?.name}: ${sub.status} (${sub.start_date})`);
      });
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testSubscriptionService();
