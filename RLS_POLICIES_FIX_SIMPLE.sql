-- R<PERSON> POLICIES FIX FOR ADVOCATE QUESTION ASSIGNMENT - SIMPLIFIED
-- Execute this script in Supabase SQL Editor to fix Row Level Security policies
-- This version avoids transaction block issues

-- 1. Drop existing conflicting policies
DROP POLICY IF EXISTS "Advocates can view all questions" ON public.legal_questions;
DROP POLICY IF EXISTS "Advocates can update questions assigned to them" ON public.legal_questions;
DROP POLICY IF EXISTS "Advocates can view pending questions" ON public.legal_questions;
DROP POLICY IF EXISTS "Advocates can assign questions to themselves" ON public.legal_questions;
DROP POLICY IF EXISTS "Advocates can update questions" ON public.legal_questions;

-- 2. Create comprehensive policies for advocates

-- Policy 1: Advocates can view all questions (pending and assigned)
CREATE POLICY "Advocates can view all questions" ON public.legal_questions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND role = 'advocate' 
      AND is_verified = true
    )
  );

-- Policy 2: Advocates can update questions (for assignment and status changes)
CREATE POLICY "Advocates can update questions" ON public.legal_questions
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND role = 'advocate' 
      AND is_verified = true
    )
    AND (
      -- Can assign unassigned questions to themselves
      (advocate_id IS NULL AND status = 'pending')
      OR
      -- Can update questions assigned to them
      EXISTS (
        SELECT 1 FROM public.advocates a
        WHERE a.profile_id = auth.uid() 
        AND a.id = advocate_id
      )
    )
  );

-- 3. Ensure advocates table policies allow proper access

-- Drop existing advocate policies
DROP POLICY IF EXISTS "Advocates can manage their own profile" ON public.advocates;
DROP POLICY IF EXISTS "Anyone can view verified advocates" ON public.advocates;
DROP POLICY IF EXISTS "Advocates can view all advocate profiles" ON public.advocates;
DROP POLICY IF EXISTS "Users can create advocate profile" ON public.advocates;

-- Policy for advocates to view advocate profiles
CREATE POLICY "Advocates can view advocate profiles" ON public.advocates
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND role IN ('advocate', 'admin')
    )
    OR
    EXISTS (
      SELECT 1 FROM public.profiles p
      WHERE p.id = profile_id AND p.is_verified = true
    )
  );

-- Policy for advocates to manage their own profile
CREATE POLICY "Advocates can manage their own profile" ON public.advocates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND id = profile_id
    )
  );

-- Policy for creating advocate records (when user becomes advocate)
CREATE POLICY "Users can create advocate profile" ON public.advocates
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND id = profile_id 
      AND role = 'advocate'
    )
  );

-- 4. Ensure responses table policies work correctly

-- Drop existing response policies
DROP POLICY IF EXISTS "Advocates can create responses" ON public.responses;
DROP POLICY IF EXISTS "Users can view responses to their questions" ON public.responses;
DROP POLICY IF EXISTS "Users can view responses" ON public.responses;
DROP POLICY IF EXISTS "Advocates can update their responses" ON public.responses;

-- Policy for advocates to create responses
CREATE POLICY "Advocates can create responses" ON public.responses
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() 
      AND a.id = advocate_id
      AND p.role = 'advocate'
      AND p.is_verified = true
    )
  );

-- Policy for viewing responses
CREATE POLICY "Users can view responses" ON public.responses
  FOR SELECT USING (
    -- Users can view responses to their questions
    EXISTS (
      SELECT 1 FROM public.legal_questions lq
      WHERE lq.id = question_id 
      AND lq.user_id = auth.uid()
    )
    OR
    -- Advocates can view their own responses
    EXISTS (
      SELECT 1 FROM public.advocates a
      WHERE a.profile_id = auth.uid() 
      AND a.id = advocate_id
    )
    OR
    -- Admins can view all responses
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND role = 'admin'
    )
  );

-- 5. Create basic indexes (without CONCURRENTLY)
CREATE INDEX IF NOT EXISTS idx_profiles_role_verified 
ON public.profiles(role, is_verified) 
WHERE role = 'advocate' AND is_verified = true;

CREATE INDEX IF NOT EXISTS idx_advocates_profile_auth 
ON public.advocates(profile_id);

-- 6. Success message
SELECT 
  'RLS policies updated successfully!' as status,
  'Advocates should now be able to assign questions to themselves.' as details,
  'Test the functionality in the advocate interface.' as next_step;
