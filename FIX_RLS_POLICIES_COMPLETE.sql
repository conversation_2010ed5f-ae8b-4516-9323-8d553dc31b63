-- FIX COMPLET DES POLITIQUES RLS POUR ADMIN ET AVOCAT
-- Exécutez ce script pour corriger tous les problèmes d'accès aux interfaces

-- 1. DÉSACTIVER TEMPORAIREMENT RLS POUR ÉVITER LES CONFLITS
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.advocates DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.legal_questions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.legal_documents DISABLE ROW LEVEL SECURITY;

-- 2. SUPPRIMER TOUTES LES POLITIQUES EXISTANTES
DROP POLICY IF EXISTS "profiles_admin_all" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Admin can manage all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Advocates can view other profiles" ON public.profiles;

DROP POLICY IF EXISTS "advocates_admin_all" ON public.advocates;
DROP POLICY IF EXISTS "advocates_select" ON public.advocates;
DROP POLICY IF EXISTS "advocates_permissive" ON public.advocates;
DROP POLICY IF EXISTS "Advocates can view all advocate profiles" ON public.advocates;
DROP POLICY IF EXISTS "Advocates can manage their own profile" ON public.advocates;
DROP POLICY IF EXISTS "Users can create advocate profile" ON public.advocates;

DROP POLICY IF EXISTS "questions_admin_all" ON public.legal_questions;
DROP POLICY IF EXISTS "questions_permissive" ON public.legal_questions;
DROP POLICY IF EXISTS "Advocates can view all questions" ON public.legal_questions;
DROP POLICY IF EXISTS "Advocates can update questions" ON public.legal_questions;
DROP POLICY IF EXISTS "Advocates can assign questions to themselves" ON public.legal_questions;

DROP POLICY IF EXISTS "responses_admin_all" ON public.responses;
DROP POLICY IF EXISTS "responses_permissive" ON public.responses;
DROP POLICY IF EXISTS "Advocates can create responses" ON public.responses;
DROP POLICY IF EXISTS "Users can view responses" ON public.responses;

-- 3. CRÉER DES FONCTIONS UTILITAIRES SIMPLES
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.is_verified_advocate()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'advocate' AND is_verified = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. CRÉER DES POLITIQUES TRÈS PERMISSIVES POUR PROFILES
CREATE POLICY "profiles_permissive_all" ON public.profiles
  FOR ALL USING (
    -- Admins peuvent tout faire
    public.is_admin() OR
    -- Utilisateurs peuvent voir/modifier leur propre profil
    auth.uid() = id OR
    -- Avocats vérifiés peuvent voir d'autres profils
    (public.is_verified_advocate() AND role IN ('advocate', 'user'))
  );

-- 5. CRÉER DES POLITIQUES PERMISSIVES POUR ADVOCATES
CREATE POLICY "advocates_permissive_all" ON public.advocates
  FOR ALL USING (
    -- Admins peuvent tout faire
    public.is_admin() OR
    -- Avocats peuvent gérer leur propre profil
    auth.uid() = profile_id OR
    -- Tout le monde peut voir les avocats vérifiés
    EXISTS (SELECT 1 FROM public.profiles WHERE id = profile_id AND is_verified = true)
  );

-- 6. CRÉER DES POLITIQUES PERMISSIVES POUR LEGAL_QUESTIONS
CREATE POLICY "questions_permissive_all" ON public.legal_questions
  FOR ALL USING (
    -- Admins peuvent tout faire
    public.is_admin() OR
    -- Utilisateurs peuvent voir/modifier leurs propres questions
    auth.uid() = user_id OR
    -- Avocats vérifiés peuvent voir toutes les questions
    public.is_verified_advocate() OR
    -- Avocats assignés peuvent modifier leurs questions
    EXISTS (
      SELECT 1 FROM public.advocates a 
      WHERE a.profile_id = auth.uid() AND a.id = advocate_id
    )
  );

-- 7. CRÉER DES POLITIQUES PERMISSIVES POUR RESPONSES
CREATE POLICY "responses_permissive_all" ON public.responses
  FOR ALL USING (
    -- Admins peuvent tout faire
    public.is_admin() OR
    -- Avocats peuvent créer/voir leurs réponses
    EXISTS (
      SELECT 1 FROM public.advocates a 
      WHERE a.profile_id = auth.uid() AND a.id = advocate_id
    ) OR
    -- Utilisateurs peuvent voir les réponses à leurs questions
    EXISTS (
      SELECT 1 FROM public.legal_questions lq 
      WHERE lq.id = question_id AND lq.user_id = auth.uid()
    )
  );

-- 8. CRÉER DES POLITIQUES PERMISSIVES POUR LEGAL_DOCUMENTS
CREATE POLICY "documents_permissive_all" ON public.legal_documents
  FOR ALL USING (
    -- Admins peuvent tout faire
    public.is_admin() OR
    -- Utilisateurs peuvent voir leurs propres documents
    auth.uid() = user_id OR
    -- Avocats vérifiés peuvent voir tous les documents
    public.is_verified_advocate()
  );

-- 9. RÉACTIVER RLS AVEC LES NOUVELLES POLITIQUES
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.advocates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.legal_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.legal_documents ENABLE ROW LEVEL SECURITY;

-- 10. ACCORDER LES PERMISSIONS AUX FONCTIONS
GRANT EXECUTE ON FUNCTION public.is_admin TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_verified_advocate TO authenticated;

-- 11. CRÉER DES INDEX POUR AMÉLIORER LES PERFORMANCES
CREATE INDEX IF NOT EXISTS idx_profiles_role_auth ON public.profiles(id, role) WHERE role IN ('admin', 'advocate');
CREATE INDEX IF NOT EXISTS idx_profiles_verified_advocates ON public.profiles(id, role, is_verified) WHERE role = 'advocate' AND is_verified = true;
CREATE INDEX IF NOT EXISTS idx_advocates_profile_lookup ON public.advocates(profile_id);

-- 12. VÉRIFICATION DES POLITIQUES CRÉÉES
SELECT 
  'VÉRIFICATION DES POLITIQUES RLS' as section,
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('profiles', 'advocates', 'legal_questions', 'responses', 'legal_documents')
ORDER BY tablename, policyname;

-- 13. TEST DES FONCTIONS UTILITAIRES
SELECT 
  'TEST DES FONCTIONS UTILITAIRES' as section,
  'Fonction is_admin créée: ' || CASE WHEN EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'is_admin') THEN 'OUI' ELSE 'NON' END as is_admin_function,
  'Fonction is_verified_advocate créée: ' || CASE WHEN EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'is_verified_advocate') THEN 'OUI' ELSE 'NON' END as is_verified_advocate_function;

-- 14. MESSAGE DE SUCCÈS
SELECT 
  'POLITIQUES RLS CORRIGÉES AVEC SUCCÈS' as status,
  'Toutes les tables ont des politiques permissives.' as message,
  'Les interfaces admin et avocat devraient maintenant être accessibles.' as result,
  'Testez les accès avec vos comptes.' as next_step;
