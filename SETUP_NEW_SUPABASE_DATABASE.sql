-- CONFIGURATION COMPLÈTE DE LA BASE DE DONNÉES SUPABASE
-- Law App Morocco - Nouveau Projet: ebwyerwbifowpyukefqv
-- Exécutez ce script dans votre Dashboard Supabase → SQL Editor

-- 1. <PERSON><PERSON><PERSON> les types ENUM
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('admin', 'user', 'advocate');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE subscription_tier AS ENUM ('free', 'pro_user', 'pro_advocate');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE document_status AS ENUM ('draft', 'completed', 'archived');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 2. C<PERSON>er la table profiles (étend auth.users)
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  full_name TEXT,
  phone TEXT,
  role user_role NOT NULL DEFAULT 'user',
  subscription_tier subscription_tier NOT NULL DEFAULT 'free',
  subscription_end TIMESTAMPTZ,
  trial_end TIMESTAMPTZ,
  stripe_customer_id TEXT,
  is_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 3. Créer la table advocates
CREATE TABLE IF NOT EXISTS public.advocates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  specializations TEXT[],
  bio TEXT,
  hourly_rate DECIMAL(10,2),
  is_featured BOOLEAN DEFAULT FALSE,
  rating DECIMAL(3,2) DEFAULT 0.0,
  total_reviews INTEGER DEFAULT 0,
  availability JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 4. Créer la table legal_questions
CREATE TABLE IF NOT EXISTS public.legal_questions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  advocate_id UUID REFERENCES public.advocates(id) ON DELETE SET NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  category TEXT DEFAULT 'general',
  status TEXT DEFAULT 'pending',
  is_answered BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 5. Créer la table responses (nouvellement ajoutée)
CREATE TABLE IF NOT EXISTS public.responses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  question_id UUID NOT NULL REFERENCES public.legal_questions(id) ON DELETE CASCADE,
  advocate_id UUID NOT NULL REFERENCES public.advocates(id) ON DELETE CASCADE,
  response_text TEXT NOT NULL,
  is_approved BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 6. Créer la table legal_documents
CREATE TABLE IF NOT EXISTS public.legal_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  content JSONB,
  template_id TEXT,
  language TEXT DEFAULT 'ar',
  status document_status DEFAULT 'draft',
  file_url TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 7. Créer la table subscriptions
CREATE TABLE IF NOT EXISTS public.subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  tier subscription_tier NOT NULL,
  status TEXT NOT NULL,
  stripe_subscription_id TEXT,
  current_period_start TIMESTAMPTZ,
  current_period_end TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 8. Créer la table usage_tracking
CREATE TABLE IF NOT EXISTS public.usage_tracking (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  questions_this_month INTEGER DEFAULT 0,
  documents_this_month INTEGER DEFAULT 0,
  last_reset_date TIMESTAMPTZ DEFAULT now(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 9. Créer la table subscribers
CREATE TABLE IF NOT EXISTS public.subscribers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  stripe_customer_id TEXT,
  subscribed BOOLEAN DEFAULT FALSE,
  subscription_tier subscription_tier DEFAULT 'free',
  subscription_end TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 10. Activer Row Level Security sur toutes les tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.advocates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.legal_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.legal_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.usage_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscribers ENABLE ROW LEVEL SECURITY;

-- 11. Créer les politiques RLS pour profiles
CREATE POLICY "Users can view their own profile" ON public.profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles" ON public.profiles
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 12. Créer les politiques RLS pour advocates
CREATE POLICY "Anyone can view verified advocates" ON public.advocates
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = profile_id AND is_verified = true
    )
  );

CREATE POLICY "Advocates can manage their own profile" ON public.advocates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND id = profile_id
    )
  );

CREATE POLICY "Admins can manage all advocates" ON public.advocates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 13. Créer les politiques RLS pour legal_questions
CREATE POLICY "Users can view their own questions" ON public.legal_questions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own questions" ON public.legal_questions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own questions" ON public.legal_questions
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Advocates can view all questions" ON public.legal_questions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'advocate' AND is_verified = true
    )
  );

CREATE POLICY "Advocates can update questions assigned to them" ON public.legal_questions
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id
    )
  );

CREATE POLICY "Admins can manage all questions" ON public.legal_questions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 14. Créer les politiques RLS pour responses
CREATE POLICY "Users can view responses to their questions" ON public.responses
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.legal_questions lq
      WHERE lq.id = question_id AND lq.user_id = auth.uid()
    )
  );

CREATE POLICY "Advocates can view their own responses" ON public.responses
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id
    )
  );

CREATE POLICY "Advocates can create responses" ON public.responses
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id
    )
  );

CREATE POLICY "Advocates can update their own responses" ON public.responses
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id
    )
  );

CREATE POLICY "Admins can manage all responses" ON public.responses
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 15. Créer les politiques RLS pour legal_documents
CREATE POLICY "Users can manage their own documents" ON public.legal_documents
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all documents" ON public.legal_documents
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 16. Créer les politiques RLS pour subscriptions
CREATE POLICY "Users can view their own subscriptions" ON public.subscriptions
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all subscriptions" ON public.subscriptions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 17. Créer les politiques RLS pour usage_tracking
CREATE POLICY "Users can view their own usage" ON public.usage_tracking
  FOR ALL USING (auth.uid() = user_id);

-- 18. Créer les politiques RLS pour subscribers
CREATE POLICY "select_own_subscription" ON public.subscribers
  FOR SELECT USING (user_id = auth.uid() OR email = auth.email());

CREATE POLICY "update_own_subscription" ON public.subscribers
  FOR UPDATE USING (true);

CREATE POLICY "insert_subscription" ON public.subscribers
  FOR INSERT WITH CHECK (true);

-- 19. Créer les index pour les performances
CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(role);
CREATE INDEX IF NOT EXISTS idx_advocates_profile_id ON public.advocates(profile_id);
CREATE INDEX IF NOT EXISTS idx_legal_questions_user_id ON public.legal_questions(user_id);
CREATE INDEX IF NOT EXISTS idx_legal_questions_advocate_id ON public.legal_questions(advocate_id);
CREATE INDEX IF NOT EXISTS idx_responses_question_id ON public.responses(question_id);
CREATE INDEX IF NOT EXISTS idx_responses_advocate_id ON public.responses(advocate_id);
CREATE INDEX IF NOT EXISTS idx_legal_documents_user_id ON public.legal_documents(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON public.subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_user_id ON public.usage_tracking(user_id);

-- 20. Créer la fonction pour gérer les nouveaux utilisateurs
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email)
  );
  
  INSERT INTO public.usage_tracking (user_id)
  VALUES (NEW.id);
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 21. Créer le trigger pour les nouveaux utilisateurs
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- 22. Créer la fonction pour mettre à jour le statut des questions
CREATE OR REPLACE FUNCTION public.update_question_status_on_response()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.legal_questions
  SET 
    is_answered = true,
    status = 'answered',
    updated_at = now()
  WHERE id = NEW.question_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 23. Créer le trigger pour mettre à jour le statut des questions
DROP TRIGGER IF EXISTS on_response_created ON public.responses;
CREATE TRIGGER on_response_created
  AFTER INSERT ON public.responses
  FOR EACH ROW EXECUTE PROCEDURE public.update_question_status_on_response();

-- 24. Créer la fonction pour assigner une question à un avocat
CREATE OR REPLACE FUNCTION public.assign_question_to_advocate(
  question_uuid UUID,
  advocate_profile_uuid UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  advocate_uuid UUID;
BEGIN
  SELECT id INTO advocate_uuid
  FROM public.advocates
  WHERE profile_id = advocate_profile_uuid;
  
  IF advocate_uuid IS NULL THEN
    RETURN FALSE;
  END IF;
  
  UPDATE public.legal_questions
  SET 
    advocate_id = advocate_uuid,
    status = 'assigned',
    updated_at = now()
  WHERE id = question_uuid;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 25. Créer la fonction pour vérifier les limites d'utilisation
CREATE OR REPLACE FUNCTION public.check_usage_limits(user_uuid UUID, action_type TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  user_tier subscription_tier;
  current_usage INTEGER;
BEGIN
  SELECT subscription_tier INTO user_tier
  FROM public.profiles
  WHERE id = user_uuid;
  
  IF user_tier IN ('pro_user', 'pro_advocate') THEN
    RETURN TRUE;
  END IF;
  
  IF action_type = 'question' THEN
    SELECT questions_this_month INTO current_usage
    FROM public.usage_tracking
    WHERE user_id = user_uuid;
    
    RETURN current_usage < 1;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 26. Accorder les permissions nécessaires
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- 27. Message de succès
SELECT 'Base de données Law App Morocco configurée avec succès!' as status,
       'Projet: ebwyerwbifowpyukefqv' as project_id,
       'Toutes les tables, fonctions et politiques sont prêtes.' as details;
